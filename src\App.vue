<template>
  <div>
    <app-logout v-if="!userData || loggedOut" />
    <div v-else>
      <app-layout />
    </div>
  </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex'
import AppLogout from '@/views/Logout.vue'
import AppLayout from '@/Layout/Layout'
import chrome from '@/utils/chrome'
import dummyData from '@/utils/dummy_data.json'
import axios from '@/utils/api.js'
import 'floating-vue/dist/style.css'
import '@/assets/styles/main.scss'
import Spinner from '@/components/Spinner'

export default {
  name: 'App',
  components: { AppLayout, AppLogout },

  data() {
    return {
      testing: false,
      interval: null,
      loading: true
    }
  },

  computed: {
    ...mapState(['userData', 'loggedOut', 'routePermissions'])
  },

  watch: {
    loggedOut() {
      if (this.loggedOut) {
        clearInterval(this.interval)
      }
    }
  },

  methods: {
    ...mapMutations(['setUserData', 'setErrorApp', 'setErrorMsgApp', 'setBanner', 'setConflict', 'setBannerContent']),

    initApp() {
      let user = {}
      var urlString = window.location.href
      var url = new URL(urlString)
      user.user_id = url.searchParams.get('user_id')
      user.portal_id = url.searchParams.get('portal_id')
      user.accountUser = url.searchParams.get('accountUser')
      user.accountPhone = url.searchParams.get('accountPhone')

      if (this.testing) {
        console.log('reading dummy data')
        this.setUserData(dummyData)
        this.fetchBanner()
        return
      }

      this.setUserData(user)
      this.fetchBanner()
    },

    updateOnlineStatus(e) {
      const { type } = e
      if (type === 'online') {
        window.location.reload()
      } else {
        this.setErrorApp(true)
        this.setErrorMsgApp('You are offline!')
      }
    },

    async fetchBanner() {
      try {
        const { data } = await axios.get(`api/banner?user_id=${this.userData.user_id}`)

        if (!data.ok) {
          return
        }

        // Banner
        let banner = data.banner
        let bannerTime = data.banner_time
        if (banner && bannerTime) {
          let oldBannerTime = Number(localStorage.getItem('banner_time')) || 0
          if (bannerTime > oldBannerTime) {
            banner.time = bannerTime
            this.setBanner(true)
            this.setBannerContent(banner)
          }
        }
      } catch (err) {
        console.log(err)
      }
    },
    async fetchData() {
      const urlParams = new URLSearchParams(window.location.search)
      let accountUser = urlParams.get('accountUser')
      const userId = this.$store.state.userData.user_id

      if (!accountUser || !userId) {
        console.error('Missing required parameters: accountUser or userId')
        return
      }

      try {
        const response = await axios.get(`/api/record?user_id=${userId}&email=${accountUser}`)
        this.reportData = response.data.data
      } catch (error) {
        console.error('Error fetching data:', error)
      }
    }
  },

  mounted() {
    // init data
    this.initApp()
    this.fetchData()
    setInterval(this.fetchData, 10000) // re-run every 10seconds
  }
}
</script>
