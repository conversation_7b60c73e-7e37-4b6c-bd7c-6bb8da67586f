<template>
  <div class="app-header">
    <!-- Banner -->
    <banner v-if="showBanner" :content="bannerContent" />

    <!-- Conflict -->
    <conflict v-if="showConflict" />

    <!-- Error -->
    <error-component v-else-if="errorApp" :errorMessage="errorMsgApp" />

    <!-- Hamburger -->
    <img :src="hamburgerIcon" alt="Menu Icon" @click.prevent="toggleMenuBar" />
  </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex'
import Banner from '@/components/Banner'
import Conflict from '@/components/Conflict'
import ErrorComponent from '@/components/Error'
// import HamburgerIcon from '@/assets/icons/hamburger_icon.png';
import HamburgerIcon from '@/assets/icons/hamburger_icon.svg'

export default {
  name: 'Header',
  components: { ErrorComponent, Banner, Conflict },

  data() {
    return {
      hamburgerIcon: HamburgerIcon
    }
  },

  computed: {
    ...mapState(['errorApp', 'errorMsgApp', 'showBanner', 'showConflict', 'bannerContent'])
  },

  methods: {
    ...mapMutations(['toggleMenuBar'])
  }
}
</script>

<style lang="scss" scoped>
.app-header {
  min-height: 7rem;
  //position: relative;
  background: #2c3f51;
  font-size: 2.18rem !important;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 4.08rem;

  .error-container {
    margin-bottom: 0;
    min-width: 40%;
    text-align: center;
    border: none;
  }

  img {
    cursor: pointer;
    position: absolute;
    right: 4.08rem;
  }

  @media screen and (max-width: 1919px) {
    height: 50px;
  }
}
</style>
