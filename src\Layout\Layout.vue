<template>
  <div>
    <side-bar />
    <div class="app-layout d-flex flex-column" :class="{ mod: modify }">
      <!-- <app-header /> -->

      <div class="app-layout_main d-flex flex-column h-100" :class="{ inactive: !active }">
        <router-view></router-view>
      </div>

      <menu-sidebar />
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import MenuSidebar from '@/components/MenuSidebar.vue'
// import AppHeader from '@/Layout/Header.vue';
import SideBar from '@/Layout/SideBar.vue'

export default {
  name: 'Layout',
  components: { MenuSidebar, SideBar },
  computed: {
    ...mapState([
      "bannerContent",
    ]),
    active() {
      return this.$store.state.menuBarShow
    },
    modify() {
      const pathName = this.$route.name
      if (pathName === 'Activities') {
        return true
      }
      return false
    }
  }, watch: {
    bannerContent(newContent) {
      this.$nextTick(() => {
        setTimeout(() => {
          // Calculate height after a small delay
          let bannerHeight = document.querySelector(".app-header")?.offsetHeight;
          let appLayout = document.querySelector(".app-layout");
          
          if (appLayout) {
            appLayout.style.height = `calc(100vh - ${bannerHeight}px)`; // Adjust height dynamically
          }
        }, 300);
      });
    },
  },
}
</script>
