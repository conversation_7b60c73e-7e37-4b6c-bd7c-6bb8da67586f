<template>
  <div class="app-header">
    <p class="plan-message danger">
      50,000 / 50,000 contacts used  
      (3,250 additional contacts synced silently in backend)
    </p>
    <!-- Banner -->
    <banner v-if="showBanner" :content="bannerContent" />

    <!-- Conflict -->
    <conflict v-if="showConflict" />

    <!-- Error -->
    <error-component v-else-if="errorApp" :errorMessage="errorMsgApp" />

    <!-- Hamburger -->
    <img class="hemburger-icon" :src="hamburgerIcon" alt="Menu Icon" @click.prevent="toggleMenuBar" />
  </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex'
import Banner from '@/components/Banner'
import Conflict from '@/components/Conflict'
import ErrorComponent from '@/components/Error'
// import HamburgerIcon from '@/assets/icons/hamburger_icon.png';
import HamburgerIcon from '@/assets/icons/hamburger_icon.svg'

export default {
  name: 'Header',
  components: { ErrorComponent, Banner, Conflict },

  data() {
    return {
      hamburgerIcon: HamburgerIcon
    }
  },

  computed: {
    ...mapState(['errorApp', 'errorMsgApp', 'showBanner', 'showConflict', 'bannerContent'])
  },

  methods: {
    ...mapMutations(['toggleMenuBar', 'setIframeMode'])
  },
  mounted() {
    if (window.self !== window.top) {
      // Embedded in iframe (or inside another page)
      document.querySelector('.hemburger-icon')?.classList.add('hide-hemburger');
      this.setIframeMode(true);
    } else {
      // Opened directly in browser
      document.querySelector('.hemburger-icon')?.classList.remove('hide-hemburger');
      this.setIframeMode(false);
    }
  }
}
</script>

<style lang="scss" scoped>
.plan-message{
  font-weight: 500;
  line-height: 2rem;
  padding: 1rem 1rem;
  border-radius: 5px;
  width: 80%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;

}
.warning {
    background-color: #FFA500; /* orange/yellow */
    color: white;
}

.danger {
    background-color: #FF0000; /* red */
    color: white;
}

.hide-hemburger {
  display: none !important;
}

.app-header {
  position: relative;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 5.08rem;
  min-height: 7rem;

  .error-container {
    margin-bottom: 0;
    min-width: 40%;
    text-align: center;
    border: none;
  }

  img {
    cursor: pointer;
    position: absolute;
    right: 5.08rem;
  }

  @media screen and (max-width: 1919px) {
    min-height: 7rem;
  }
}
</style>
