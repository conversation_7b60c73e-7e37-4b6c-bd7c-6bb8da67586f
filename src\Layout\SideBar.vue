<template>
  <div class="app-header">
    <!-- Banner -->
    <banner v-if="showBanner" :content="bannerContent" />

    <!-- Conflict -->
    <conflict v-if="showConflict" />

    <!-- Error -->
    <error-component v-else-if="errorApp" :errorMessage="errorMsgApp" />

    <!-- Hamburger -->
    <img class="hemburger-icon" :src="hamburgerIcon" alt="Menu Icon" @click.prevent="toggleMenuBar" />
  </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex'
import Banner from '@/components/Banner'
import Conflict from '@/components/Conflict'
import ErrorComponent from '@/components/Error'
// import HamburgerIcon from '@/assets/icons/hamburger_icon.png';
import HamburgerIcon from '@/assets/icons/hamburger_icon.svg'

export default {
  name: 'Header',
  components: { ErrorComponent, Banner, Conflict },

  data() {
    return {
      hamburgerIcon: HamburgerIcon
    }
  },

  computed: {
    ...mapState(['errorApp', 'errorMsgApp', 'showBanner', 'showConflict', 'bannerContent'])
  },

  methods: {
    ...mapMutations(['toggleMenuBar', 'setIframeMode'])
  },
  mounted() {
    if (window.self !== window.top) {
      // Embedded in iframe (or inside another page)
      document.querySelector('.hemburger-icon')?.classList.add('hide-hemburger');
      this.setIframeMode(true);
    } else {
      // Opened directly in browser
      document.querySelector('.hemburger-icon')?.classList.remove('hide-hemburger');
      this.setIframeMode(false);
    }
  }
}
</script>

<style lang="scss" scoped>
.hide-hemburger {
  display: none !important;
}

.app-header {
  position: relative;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 5.08rem;
  min-height: 7rem;

  .error-container {
    margin-bottom: 0;
    min-width: 40%;
    text-align: center;
    border: none;
  }

  img {
    cursor: pointer;
    position: absolute;
    right: 5.08rem;
  }

  @media screen and (max-width: 1919px) {
    min-height: 7rem;
  }
}
</style>
