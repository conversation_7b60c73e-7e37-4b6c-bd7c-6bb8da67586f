.app-layout {
  overflow: hidden;
  position: relative;
  height: calc(100vh - 7rem);

  &.mod {
    top: 0;
    // width: 100%;
    // height: 100%;
    // height: calc(100vh - 14rem);
    margin: 0rem 5rem 0rem 5rem;
    // box-shadow: 0 6px 18px rgba(11, 20, 26, 0.05);
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
    // .app-layout_main {
    //   height: calc(100vh - 50px);
    // }
    .app-layout_main {
      // height: calc(100vh - 50px - 38px);
      height: calc(100vh - 14rem);
    }
  }

  &_main {
    // height: calc(100vh - 7.10rem);
    // margin-right: 31rem;
    transition: all 0.3s;
    overflow: hidden auto;

    &.inactive {
      margin-right: 0;
    }

    &::-webkit-scrollbar-thumb {
      background: #34b7f1;

      &:hover {
        background: darken(#34b7f1, 10);
      }
    }
  }

  @media screen and (min-width: 1441px) {
    &.mod {
      // top: 19px;
      // width: 1396px;
      // height: calc(100% - 38px);
      // height: calc(100vh - 14rem);
      margin: 0rem 7rem 7rem 7rem;
      //box-shadow: 0 6px 18px rgba(11, 20, 26, 0.05);
      box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);

      // .app-layout_main {
      //   // height: calc(100vh - 50px - 38px);
      //   height: calc(100vh - 14rem);
      // }
    }
  }

  @media screen and (min-width: 1920px) {
    &_main {
      height: calc(100vh - 7rem);
    }

    &.mod {
      .app-layout_main {
        // height: calc(100vh - 7rem - 38px);
        height: calc(100vh - 7rem);
      }
    }
  }

  // @media screen and (max-width: 1919px) {
  //   &_main {
  //     height: calc(100vh - 50px);
  //   }

  //   &.mod {
  //     .app-layout_main {
  //       height: calc(100vh - 50px - 6rem);
  //     }
  //   }
  // }
}
