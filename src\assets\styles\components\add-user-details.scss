.add-user-details {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;

  .success-msg {
    position: absolute;
    width: 100%;
  }

  .back-div { 
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-block-end: 2.5rem;

    img {
      width: 26px;
      cursor: pointer;
    }

    h3 {
      font-size: 3.5rem;
    }
  }

  .user-details {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    gap: .5rem;
  }
  .content {
    display: flex;
    flex-direction: column;
    max-height: 100%;
    border: 2px solid #cbd6e2;
    background-color: #fff;

    .title {
      display: flex;
      align-items: center;
      gap: 1.5rem;
      padding: 0.5rem 1.5rem;
      border-block-end: 2px solid #cbd6e2;
      cursor: pointer;

      .rotated {
        transform: rotate(-90deg);
        transition: transform 0.3s ease;
      }
    }

    p {
      margin: 0;
      font-size: 3rem;
      color: #33475b;
    }

    span,
    label {
      font-size: 2.5rem;
    }

    span {
      margin-block: 1.5rem;
    }

    .add-user-detail {
      .title {
        img {
          cursor: pointer;
        }
      }

      input {
        all: unset;
        font-size: 2.5rem;
        background-color: #eef3fb;
        color: #33475b80;
        border: 1px solid #cbd6e2;
        border-radius: 10px;
        padding: 0.8rem 1rem;
        margin-top: 0.5rem;
      }
    }

    .user-detail {
      display: grid;
      padding: 0.5rem 1.5rem 3rem 1.5rem;

      input {
        width: 40%;
      }
    }

    .permissions {
      .title {
        border-block-start: 2px solid #cbd6e2;
      }

      img {
        cursor: pointer;
      }

      .permission-status {
        display: flex;
        flex-direction: column;
        overflow-x: auto;
        margin-block-end: 0 !important;
        padding-block-end: 1rem;
      }

      .permission-status,
      span {
        margin-block: 1.5rem;
        margin-inline: 1rem;

        div {
          display: grid;
          grid-template-columns: 40% 1fr;
          justify-items: flex-start;
          align-items: center;
          padding-block: 0.5rem;
        }

        input[type='checkbox'] {
          appearance: none;
          cursor: pointer;
          width: 2.3rem;
          height: 2.3rem;
          border-radius: 3px;
          background-color: #34b7f166; /* Default background color */
          border: 1px solid #33475b66; /* Default border color */
          position: relative;

          &:checked {
            &::after {
              content: '✔'; /* Tick symbol */
              color: #2c3f51; /* Tick color */
              font-size: 12px;
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
            }
          }
        }
      }
    }
  }

  button {
    width: fit-content;
  }
}

@media screen and (max-height: 630px) {
  .add-user-details {
    .max-height {
      max-height: 25rem;
    }

    .full-height {
      height: 100% ;
    }
  }
}