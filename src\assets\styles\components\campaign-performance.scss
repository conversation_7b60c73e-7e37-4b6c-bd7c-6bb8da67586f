@import '../../styles/utils/var';

.campaign-performance {
  position: relative;

  p {
    font-size: 4.27rem;
    font-weight: 600;
    font-family: 'Inter', sans-serif;
  }

  .row-1 {
    .child-col-1 {
      border-top-left-radius: 2.07rem;
      border-top-right-radius: 2.07rem;
      overflow: hidden;
      background-color: $white;
      position: sticky;
      top: -1px;

      .sub-child-col-1 {
        display: flex;
        color: $white;

        .grand-child-1 {
          width: 19%;
          background-color: $picton-blue;
        }

        .grand-child-2,
        .grand-child-3 {
          font-size: 2.67rem;
          font-weight: 600;
          text-align: center;
          padding-block: 2.9rem;
          font-family: 'Inter', sans-serif;
        }

        .grand-child-2 {
          width: 32%;
          background-color: $squid-ink-lighter;
        }

        .grand-child-3 {
          width: 49%;
          background-color: $squid-ink;
        }
      }

      .sub-child-col-2 {
        display: flex;
        color: $eerie-black;
        border-inline: 0.6px solid $light-gray;
        border-block-end: 0.6px solid $light-gray;
        min-height: 7.4rem;

        .grand-child-1,
        .grand-child-2,
        .grand-child-3 {
          font-size: 2.08rem;
          font-weight: 600;
          display: flex;
          padding: 0.55rem;
          align-items: center;
          font-family: 'Inter', sans-serif;
        }

        .grand-child-1 {
          justify-content: center;
          width: 19%;
        }

        .grand-child-2 {
          width: 32%;
          justify-content: space-around;
          text-align: center;
        }

        .grand-child-3 {
          width: 49%;
          justify-content: space-around;
          text-align: center;
          gap: 2%;
          padding-inline: 2rem !important;

          & span {
            &:first-child {
              width: 32%;
            }

            &:nth-child(2) {
              width: 32%;
            }

            &:nth-child(3) {
              width: 15%;
            }

            &:last-child {
              width: 15%;
            }
          }
        }
      }
    }

    .child-col-2 {
      padding-block: 1.5rem;
      display: flex;
      flex-direction: column;
      gap: 1.5rem;

      .sub-child-col-1 {
        min-height: 12.7rem;
        display: flex;
        background-color: $alice-blue;

        .grand-child-1,
        .grand-child-2,
        .grand-child-3 {
          display: flex;
          align-items: center;

          div {
            display: flex;
            flex-direction: column;
          }
        }

        .grand-child-1 {
          width: 19%;
          justify-content: center;
          font-size: 2.07rem;
          font-weight: 600;
          font-family: 'Nunito Sans', sans-serif;

          span {
            text-align: center;
          }
        }

        .grand-child-2 {
          display: flex;
          justify-content: space-around;
          background-color: $squid-ink-light;
          width: 32%;
          font-size: 2.07rem;
          font-weight: 400;
          font-family: 'Inter', sans-serif;

          div {
            text-align: center;
            gap: 6px;
          }
        }

        .grand-child-3 {
          display: flex;
          justify-content: space-around;
          width: 49%;
          padding-inline: 2rem;
          gap: 2%;
          text-align: center;
          font-size: 2.96rem;
          font-family: 'Inter', sans-serif;

          & span {
            &:first-child {
              width: 32%;
            }

            &:nth-child(2) {
              width: 32%;
            }

            &:nth-child(3) {
              width: 15%;
            }

            &:last-child {
              width: 15%;
            }
          }
        }
      }
    }
  }
}
