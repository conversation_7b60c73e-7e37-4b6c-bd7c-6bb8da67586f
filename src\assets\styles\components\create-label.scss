.create-label_wrapper {
  padding: 4.5rem 4.5rem 3rem;
  min-height: 100%;
  .create-label {
    .form-group {
      label {
        font-weight: 600;
        font-size: 2rem;
        line-height: 3rem;
        color: #2c3f51;
        margin-bottom: 0.8rem;
      }

      input,.color-picker {
        color: #000 !important;
        font-size: 2.5rem !important;
        font-weight: 500 !important;
      }

      .form-control {
        border: 2px solid #d2d2d2;
        border-radius: 2rem;
        font-size: 2rem;
        line-height: 3rem;
        color: #919192;
        padding: 1.9rem 2.8rem;
        font-weight: normal;

        &::placeholder {
          color: #919192;
        }
      }
      input.form-control {
        height: 7rem;
        padding: 1.9rem 2.8rem;
        margin-bottom: 1.8rem;
      }
      textarea.form-control {
        padding: 2.6rem 3.1rem;
        overflow: hidden;
        margin-bottom: 2.6rem;
      }
    }

    .color-picker {
      position: relative;
      height: 7rem;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .color-block {
        height: 3.2rem;
        width: 3.7rem;
        border-radius: 1rem;
      }

      img {
        height: 3.2rem;
      }
    }

    .swatches-container {
      margin-top: 1.4rem;

      .vc-swatches {
        width: 82%;
        margin: auto;

        &::-webkit-scrollbar-thumb {
          background-color: #f7892f;
        }
      }
    }

    .button-wrapper {
      text-align: right;

      button.btn {
        margin-top: 2.4rem;
        height: 5.6rem;
        padding: 1.4rem 2.3rem 1.2rem;
        font-weight: 600;
        font-size: 2rem;
        line-height: 1;
        letter-spacing: 0.001em;
      }
    }

    @media only screen and (max-width: 1919px) {
      .form-group {
        label {
          font-size: 15px;
          line-height: 22.5px;
          margin-bottom: 5px;
        }
      }

      .swatches-container {
        .vc-swatches {
          width: 90%;
        }
      }
    }
  }
}
