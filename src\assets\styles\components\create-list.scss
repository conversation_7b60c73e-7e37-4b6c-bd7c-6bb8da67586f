@import '../../styles/utils/var';

.create-list_wrapper {
  padding: 4.5rem 4.5rem 3rem;
  min-height: 100%;
  position: relative;
  font-family: 'Poppins';

  .vs__selected {
    font-size: 2.5rem;
    margin: 0;
  }

  .vs__dropdown-toggle {
    padding: 1.5rem 1rem;
  }

  .campaign-error-container .error-container,
  .campaign-error-container .success-container {
    position: absolute;
    top: 1rem;
    width: 85%;
    z-index: 1;
  }

  .create-list {
    display: block;
    font-size: 3rem;
    height: calc(100vh - 31rem);

    form {
      position: relative;
      height: 100%;
      display: flex;
      justify-content: space-between;
      flex-direction: column;

      .hs-list-title-div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin-bottom: 1.2rem;

        label {
          margin: 0;
        }

        // img {
        //   cursor: pointer;
        // }

        .tooltip-container {
          position: relative;
          display: inline-block;

          .info-icon {
            cursor: pointer;
          }

          .tooltip-text {
            font-size: 1.8rem;
            visibility: hidden;
            width: 39rem;
            background-color: #313d4f;
            color: #fff;
            border-radius: 6px;
            padding: 5px 1.3rem;
            position: absolute;
            z-index: 1;
            bottom: 125%; /* Position the tooltip above the icon */
            right: 0;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
          }

          &:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
          }
        }
      }

      .add-more-list {
        display: flex;
        align-items: center;
        gap: 0.8rem;
        cursor: pointer;
        width: fit-content;

        .icon {
          font-size: 3rem;
          color: $picton-blue;
          font-weight: 500;
        }

        .title {
          font-size: 2rem;
          font-weight: 500;
          color: $picton-blue;
          text-decoration: underline;
        }
      }

      .camp-title-div {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        // margin-bottom: 20px;
      }
    }

    .dropdown-menu {
      width: 50rem;
      inset: initial !important;
      top: 28 !important;
      right: 0 !important;
      // left: -9px !important;
      transform: none !important;
    }

    .template-body {
      font-size: 2.5rem;
      word-wrap: break-word;
      white-space: pre-line;
      font-weight: 400;
      background: #eef3fb;
      padding: 5px;
      border-radius: 5px;
      max-height: 200px;
      overflow-y: auto;
      margin-block: 2rem;
    }
    .tokenButton {
      border: 0px;
      padding: 5px 8px;
      font-size: 2.2rem;
      font-weight: 500;
    }
    .search-input {
      height: 6rem;
      margin-top: 5px;
      font-size: 2.2rem;
      border-radius: 5px;
      padding: 5px 10px;
      border: 2px solid #9e9eeb;
    }
    ul {
      font-size: 2.2rem;
      // min-width: 400px;
      max-height: 250px;
      overflow: hidden;
      overflow-y: scroll;
      // width: 48rem;
      // // inset: 0 !important;
      // top: 0 !important;
      // right: 0 !important;
    }
    .dropdown-item {
      padding: 8px;
      width: 400px;
      word-wrap: break-word;
      white-space: normal;
    }

    .show-list-name {
      display: flex;
      align-items: center;
      gap: .6rem;

      .title {
        font-size: 2.5rem;
        font-weight: 600;
        color: #313d4f;
      }

      p {
        margin: 0;
      }
    }

    .form-group {
      margin-bottom: 1rem;

      .restrict-color {
        color: red;
      }

      label {
        font-weight: 600;
        font-size: 2.5rem;
        line-height: 3rem;
        color: #2c3f51;
        margin-bottom: 0.8rem;
      }

      .camp-form-input {
        width: 100%;
        border-radius: 1rem !important;
        outline: none;
        color: #313d4f !important;
        border: .0625rem solid #d2d2d2 !important;
        padding: 1.5rem 1rem !important;
      }

      .form-control,
      .form-select,
      .camp-form-input {
        border: 2px solid #d2d2d2;
        border-radius: 2rem;
        font-size: 2.5rem;
        line-height: 3rem;
        color: #919192;
        padding: 1.9rem 2.8rem;
        font-weight: normal;

        &::placeholder {
          color: #919192;
        }
      }

      .search-input {
        height: 6rem !important;
        margin-top: 5px;
        font-size: 2.2rem;
        border-radius: 5px;
        padding: 5px 10px !important;
        border: 2px solid #9e9eeb;
      }

      input.form-control {
        height: 7rem;
        padding: 1.9rem 2.8rem;
      }

      textarea.form-control {
        padding: 2.6rem 3.1rem;
        overflow: hidden;
      }
    }

    .form-container {
      height: calc(100vh - 41rem);

      .app-spinner {
        height: 100%;
      }
    }

    .hs-template-div {
      overflow-x: auto;
      height: calc(100vh - 41rem);

      img {
        cursor: pointer;
      }

      label {
        display: block;
        margin-block: 3rem 2rem;
      }
    }

    .enrolled-content {
      img {
        cursor: pointer;
      }

      p {
        font-weight: 400;
      }

      .title {
        font-weight: 600;
        font-size: 3rem;
      }
    }

    .schedule-campaign-conatiner {
      .schedule-campaign-secondary {
        position: relative;
        gap: 2px;
        width: fit-content;

        button:disabled {
          cursor: not-allowed !important;
          // background-color: #d2d2d2;
          // border-color: #d2d2d2;
          // color: #fff;
        }

        .btn-primary {
          svg {
            fill: #fff;
          }
        }
        & :hover svg {
          fill: #34b7f1;
        }

        div {
          padding: 1.3rem;
        }

        .schedule-btn {
          // box-shadow: 0px 0px 1px 1px gray;
          position: absolute;
          top: -48px;
          right: 0;
          width: max-content;
          color: $picton-blue;
          font-weight: 600;
          font-size: 2.3rem;
          border: 2px solid $picton-blue !important;
          background-color: #fff;
        }
      }
    }

    .color-picker {
      position: relative;
      height: 7rem;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .color-block {
        height: 3.2rem;
        width: 3.7rem;
        border-radius: 1rem;
      }

      img {
        height: 3.2rem;
      }
    }

    .swatches-container {
      margin-top: 3.4rem;

      .vc-swatches {
        width: 82%;
        margin: auto;

        &::-webkit-scrollbar-thumb {
          background-color: #f7892f;
        }
      }
    }

    .create-list-container {
      > img {
        cursor: pointer;
      }

      label {
        font-weight: 600;
        font-size: 2.5rem;
        line-height: 3rem;
        color: #313d4f;
        margin-bottom: 0.8rem;
      }

      #hubspot-list {
        width: 100%;

        .vs__dropdown-toggle {
          border-radius: 4px !important;
        }

        ul {
          width: 100%;
        }
      }

      #hubspot-list .vs__search {
        margin: 0;
        width: 100% !important;
        // padding: 8px !important;
        font-size: 16px !important;
      }

      .list-name-div {
        display: grid;

        input {
          border-radius: 4px;
          border: 1px solid #d2d2d2;
          font-size: 2.5rem;
          outline: none;
          padding: 1rem 1rem;

          &:focus {
            outline: none;
          }
        }
      }
    }

    .button-wrapper {
      text-align: right;

      button.btn {
        margin-top: 3.4rem;
        height: 5.6rem;
        padding: 1.4rem 2.3rem 1.2rem;
        font-weight: 600;
        font-size: 2rem;
        line-height: 1;
        letter-spacing: 0.001em;
      }
    }
  }

  .progress-container {
    // display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 3.5rem;

    .step {
      display: flex;
      // flex-direction: column;
      align-items: center;
      justify-content: space-between;
      position: relative;

      .circle {
        width: 3.5rem;
        height: 3.5rem;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 2rem;
        font-weight: bold;
        color: white;
        background-color: lightgray;
        border: 2px solid lightgray;
      }

      .circle.completed {
        background-color: #00aaff;
        border-color: #00aaff;
      }

      .circle.active {
        background-color: white;
        border-color: #00aaff;
        color: #00aaff;
      }

      .label {
        margin-top: 8px;
        font-size: 14px;
        color: #333;
      }
    }
    .line {
      height: 2px;
      background-color: lightgray;
      width: 100%;
    }

    .line.active {
      background-color: #00aaff;
    }

    .step-space-inline {
      padding-inline: 1.2rem;
    }

    .step-title-spce {
      margin-left: 4px;
    }

    .step:last-child .line {
      width: 50%;
    }
  }
}

.hwt-container {
  display: inline-block;
  position: relative;
  overflow: hidden !important;
  -webkit-text-size-adjust: none !important;
}

.hwt-backdrop {
  position: absolute !important;
  top: 0 !important;
  right: -99px !important;
  bottom: 0 !important;
  left: 0 !important;
  padding-right: 99px !important;
  overflow-x: hidden !important;
  overflow-y: auto !important;
}

.hwt-highlights {
  width: auto !important;
  height: auto !important;
  border-color: transparent !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  color: transparent !important;
  overflow: hidden !important;
}

.hwt-input {
  display: block !important;
  position: relative !important;
  margin: 0;
  padding: 0;
  border-radius: 0;
  font: inherit;
  overflow-x: hidden !important;
  overflow-y: auto !important;
}

.hwt-content {
  border: 1px solid;
  background: none transparent !important;
}

.hwt-content mark {
  padding: 0 !important;
  color: inherit;
}
.custom-vue-select .vs__dropdown-toggle {
  border: 2px solid #d2d2d2;
  border-radius: 2rem;
  font-size: 2.5rem;
  line-height: 3rem;
  color: #919192;
  padding: 1.9rem 2.8rem;
  font-weight: normal;

  &::placeholder {
    color: #919192;
  }
}

.custom-vue-select input.vs__search {
  font-size: 15px !important; /* Adjust the font size as needed */
}

@media screen and (min-width: 1910px) {
  .schedule-campaign-secondary .schedule-btn {
    top: -68px !important;
  }
}
