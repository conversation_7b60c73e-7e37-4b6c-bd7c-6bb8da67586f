.create-template_wrapper {
  padding: 0 5rem;
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: <PERSON>pin<PERSON>;
  position: relative;

  .template-toast-container {
    .error-container,
    .success-container {
      position: absolute;
      top: 1rem;
      width: 85%;
      z-index: 1;
    }
  }

  .template-header {
    display: flex;
    gap: 2rem;
    align-items: center;
    margin-bottom: 1rem;

    img {
      cursor: pointer;
    }

    h2 {
      margin: 0;
      font-weight: 600;
      font-size: 3.5rem;
      color: #000;
    }
  }

  .description {
    color: #2c3f51;
    font-size: 2.8rem;
    margin-bottom: 2.5rem;
  }

  .create-template {
    display: grid;
    grid-template-columns: 60% 38%;
    gap: 2%;
    height: calc(100% - 13rem);
  }

  .template-form-card {
    background: #fff;
    width: 100%;
    // border-radius: 8px;
    overflow: auto;
    padding-bottom: 1.5rem;

    &::-webkit-scrollbar {
      display: none; /* For Chrome, Safari, and Edge */
    }
  }

  .template-preview {
    .preview {
      height: 100%;
      min-height: 35rem;
      max-height: 90%;

      .preview-header {
        background-color: #2c3f51;
        padding: 1rem 3rem;

        h3 {
          color: #fff;
          font-weight: 600;
          margin: 0;
        }
      }

      .preview-box {
        display: flex;
        flex-direction: column;
        max-height: 70%;
        min-height: 30rem;
        background-size: cover !important; /* Covers the entire element */
        background-size: contain; /* Fits the whole image inside the element */
        background: #fefefe;
        border: 1px dashed #ccc;
        padding: 16px;
        color: #666;

        .header {
          font-weight: 600;
          font-size: 2.5rem !important;
          margin-bottom: 3rem !important;
          word-wrap: break-word;
        }

        .preview-content {
          background-color: #fff;
          border-radius: 1.5rem;
          overflow: auto;
          width: 80%;
          padding-block: 2rem 0rem;
          min-height: 12rem;
          max-height: 100%;

          &::-webkit-scrollbar {
            display: none; /* For Chrome, Safari, and Edge */
          }

          .preview-img-div {
            width: 90%;
            margin-inline: auto;
            margin-bottom: 1rem;
          }

          p {
            padding: 0 2rem;
            padding-bottom: 0;
            color: #2c3f51;
            font-size: 2rem;
            margin: 0;
          }

          .format-content {
            font-size: 2.5rem !important;
            word-wrap: break-word;
          }

          hr {
            margin: 0.5rem 0;
          }

          span {
            font-size: 1.8rem;
            color: #9a9797;
            padding-bottom: 1rem;
            padding-inline: 2rem;
            word-wrap: break-word;
          }

          img {
            width: 100%;
            height: auto;
          }
        }

        .preview-btns {
          width: 100%;

          button {
            all: unset;
            background-color: #fff;
            font-size: 2.5rem;
            font-weight: 500;
            color: #34b7f1;
            width: 100%;
            padding: 1rem 0;
            text-align: center;
            border-top: 1px solid #0000001a;
            cursor: pointer;
          }
        }
      }
    }
  }

  /* Form Styles */
  .form-group {
    margin-bottom: 16px;
    position: relative;

    label {
      display: block;
      font-size: 2.4rem;
      font-weight: 600;
      color: #2c3f51;
      margin-bottom: 0;
    }

    .group-title,
    .example {
      font-size: 2.1rem;
      font-weight: 400;
      color: #2c3f51;
      margin: 0;
    }

    .custom-dropdown {
      position: relative;
      width: 100%;
      border: 1px solid #ccc;
      border-radius: 4px;
      background: white;
    }

    .dropdown-header {
      padding: 10px;
      cursor: pointer;
      font-size: 2.1rem;
      color: #000;
    }

    .placeholder-color{
      color: #2C3F5199;
    }

    .template-type-menu {
      position: absolute;
      top: 100%;
      left: 0;
      width: 100%;
      background: white;
      border: 1px solid #ccc;
      border-radius: 4px;
      z-index: 10;
      list-style: none;
      margin: 0;
      padding: 0;

      .selected-item {
        background-color: #34b7f14d;
      }

      .dropdown-item {
        font-size: 2.1rem;
        padding: 10px;
        display: flex;
        align-items: center;
        cursor: pointer;
      }
    }

    .dropdown-item:hover {
      background: #f0f0f0;
    }

    .dropdown-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }

    .name-error {
      position: absolute;
      color: red;
      font-size: 1.6rem;
    }

    .content-variables {
      margin-top: 1rem;

      div {
        display: flex;
        align-items: center;
        gap: 1.5rem;

        label {
          font-size: 2rem;
          font-weight: 500;
        }
      }

      .expires-container {
        color: #4a4a4a;
        display: flex;
        flex-direction: column;
        align-items: baseline;
        margin-left: 3.5rem;
        gap: 0.4rem;

        label {
          font-weight: 400 !important;
        }

        .input-group {
          position: relative;
          display: flex;
          align-items: center;
          // padding: 5px;
          width: 22rem;

          input {
            font-size: 2rem;
            background: #f8f8f8;
            border: 1px solid #ccc;
            border-radius: 5px !important;
            width: 100%;
            outline: none;
            padding: 1rem 0 1rem 1.5rem;
          }

          .time-unit {
            position: absolute;
            right: 3rem;
            font-size: 2rem;
          }
        }
        span {
          font-size: 1.7rem;
        }
      }
    }
  }

  .form-control {
    width: 98%;
    padding: 8px 12px;
    font-size: 2.1rem;
    color: #333;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-top: 0.6rem;
  }

  .form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 3px rgba(0, 123, 255, 0.5);
  }

  textarea.form-control {
    resize: none;
  }

  .error-message{
    color: red;
    font-size: 2rem;
    margin-top: 0.5rem;
  }

  /* Contact Variables */
  .variables {
    // background: #f1f1f1;
    padding: 8px 12px 8px 0;
    // border: 1px dashed #ccc;
    // border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 1.5rem;
  }

  .variables p {
    font-size: 14px;
    color: #333;
    margin: 4px 0;
  }

  /* Preview Section */
  .preview {
    // margin-top: 24px;
  }

  .preview h3 {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
  }
}
