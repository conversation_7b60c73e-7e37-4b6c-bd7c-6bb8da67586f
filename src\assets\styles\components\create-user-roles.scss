.create-user-roles {
  display: flex;
  flex-direction: column;
  height: 100%;

  .back-div {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-block-end: 2.5rem;

    img {
      width: 26px;
      cursor: pointer;
    }

    h3 {
      font-size: 3.5rem;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    max-height: 100%;
    border: 2px solid #cbd6e2;
    margin-block-end: 3.5rem;
    overflow: hidden;
    background-color: #fff;

    p {
      margin: 0 !important;
    }

    .title,
    .created-list-title {
      display: flex;
      align-items: center;
      gap: 1.5rem;
      padding: 0.5rem 1.5rem;
      border-block-end: 2px solid #cbd6e2;
    }

    .add-role {
      padding: 0.5rem 1.5rem;

      span {
        display: block;
        font-size: 2.5rem;
        margin-block: 1.5rem 1rem;
      }

      div {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #eef3fb;
        border: 1px solid #cbd6e2;
        width: fit-content;
        padding: 0.3rem 0.8rem .3rem 0.4rem;
        border-radius: 10px;
        width: 45%;

        input {
          all: unset;
          font-size: 2.5rem;
          color: #33475b80;
          border-radius: 10px;
          padding: 0.8rem 1rem;
          width: calc(100% - 18rem);
        }

        .btn-primary {
          padding: 1rem 2.3rem;
        }
      }
    }

    .created-list-title {
      border: none;
      padding-block: 3rem 1rem;
    }

    .created-list {
      display: flex;
      flex-direction: column;
      height: 100%;
      overflow-x: auto;
      padding: 0.5rem 1.5rem;

      p{
        text-transform: capitalize;
      }

      div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1.5rem;
        padding: 2rem 2.3rem;
        border: 1px solid #cbd6e2;
        width: 45%;
        border-radius: 10px;
        background-color: #eef3fb;
        margin-block: .3rem;
      }

      img {
        cursor: pointer;
      }

      button {
        all: unset;
        cursor: pointer;
      }
    }
  }
}
