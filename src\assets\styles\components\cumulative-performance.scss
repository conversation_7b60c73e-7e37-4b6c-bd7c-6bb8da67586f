@import '../utils/var';

.cumulative-performance {
  p {
    font-size: 4.27rem;
    font-weight: 600;
    font-family: 'Inter', sans-serif;
  }

  .content {
    .row-1 {
      display: flex;
      gap: 1%;

      .child-col-1 {
        display: flex;
        flex-direction: column;
        gap: 3rem;
        width: 25%;
        background-color: $alice-blue;
        border-radius: 2.07rem;
        padding: 5rem;

        img {
          width: 11rem;
        }

        span {
          font-size: 3.05rem;
          font-weight: 500;
        }

        h3 {
          font-size: 7.32rem;
          font-weight: 700;
        }
      }

      .child-col-2 {
        border-radius: 2.07rem;
        width: 74%;
        background-color: $alice-blue;
        padding: 2rem 4rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 46.81rem;

        .sub-child-1 {
          display: grid;
          grid-template-columns: 19% 19% 19% 19% 19%;
          justify-content: center;
          gap: 1%;

          div {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            text-align: center;
            gap: 2.5rem;
            position: relative;

            &::after {
              content: '';
              rotate: 180deg;
              position: absolute;
              top: -15px;
              bottom: 0;
              height: 285px;
              right: 0;
              transform: translateX(-50%);
              border-left: 3px solid $white;
              z-index: 11;
            }

            &:last-child::after {
              content: none !important;
            }

            span {
              font-size: 2.37rem;
              font-weight: 600;
              font-family: 'Inter', sans-serif;
            }

            h3 {
              font-size: 3.55rem;
              font-weight: 700;
              font-family: 'Nunito Sans', sans-serif;
            }
          }
        }

        .sub-child-2 {
          display: flex;
          align-items: center;
          margin: auto;
          height: 57%;
          width: 100%;

          .svg-funnel-js svg {
            width: 100% !important;
          }
          .svg-funnel-js svg path {
            width: 100% !important;
            height: 100% !important;
          }

          .svg-funnel-js:not(.svg-funnel-js--vertical) {
            width: 100% !important;
            padding: 0px !important;
            padding-top: 13px !important;
          }

          .svg-funnel-js__labels {
            display: none !important;
          }

          .svg-funnel-js .svg-funnel-js__container {
            padding-inline-start: 25px !important;
          }

          #myChart {
            width: 100% !important;
            height: 100% !important;
            position: relative;
            z-index: 11;
          }
        }
      }
    }

    .row-2,
    .row-3 {
      display: flex;
      gap: 1%;
      margin-block-start: 2rem;

      .child-col-1 {
        display: flex;
        width: 46.44%;
        background-color: $alice-blue;
        border-radius: 2.07rem;

        .sub-child-1 {
          border-inline-end: 2px solid $white;
        }

        .sub-child-1,
        .sub-child-2 {
          display: flex;
          gap: 3rem;
          flex-direction: column;
          align-items: center;
          width: 50%;
          padding-block: 5rem;

          span {
            font-size: 2.96rem;
            font-weight: 500;
          }

          h3 {
            font-size: 4.14rem;
            font-weight: 700;
          }

          .pie-chart {
            width: 14.81rem;
            height: 14.81rem;
            border-radius: 50%;
          }
        }
      }

      .child-col-2 {
        width: 53%;
        background-color: $picton-blue-light;
        border-radius: 2.07rem;
        padding-block: 5rem;
        padding-inline: 2rem;

        .title {
          display: block;
          font-size: 3.55rem;
          font-weight: 600;
          text-align: center;
        }

        .sub-child {
          display: flex;
          justify-content: space-around;
          padding-block-start: 4rem;
          gap: 2.5rem;

          div {
            text-align: center;

            span {
              font-size: 2.96rem;
              font-weight: 500;
            }

            h1 {
              font-size: 8.88rem;
              font-weight: 700;
              padding-block-start: 4rem;
            }
          }
        }
      }
    }

    .row-3 {
      .child-col-1 {
        width: 100%;

        .sub-child-1,
        .sub-child-2 {

          p{
            font-size: 3rem;
            font-weight: 600;
          }
        }

        .sub-child-1 {
          .incoming-msg-div {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 2rem;
            padding-block: 2rem;

            .pie-chart {
              width: 30rem;
              height: 30rem !important;
              height: auto;
            }

            .work-hrs-percentage{
              display: flex;
              flex-direction: column;

              .percentage {
                font-size: 4.5rem;
                font-weight: 700;
              }
            }

            .work-hrs-msgs {
              display: flex;
              gap: 1rem;
              margin-top: 3rem;

              .verticle-line {
                border: 1px solid #00000099;
                margin-inline: 1rem;
              }

              .work-hrs-msg .color {
                background-color: #34B7F1;
              }

              .non-work-hrs-msg .color {
                background-color: rgb(33, 37, 41);
              }

              .non-work-hrs-msg,
              .work-hrs-msg{
                display: flex;
                flex-direction: column;

                div {
                  display: flex;
                  align-items: center;
                  gap: 1rem;
                  
                  .color {
                    display: block;
                    border-radius: 50%;
                    width: 1.5rem;
                    height: 1.5rem;
                  }
                }

              }

            }

           
          }

          .work-hour-time-title {
            width: 100%;
            padding-left: 6rem;

            p{
              font-size: 2.5rem;
              font-weight: normal;
            }
          }
        }

        .sub-child-2 {
       
          > div {
            display: flex;
            align-items: center;
            padding-left: 1rem;
            height: 100%;
            width: 100%;

            .barchart-container {
              display: flex;
              justify-content: center;
            }

            p{
              writing-mode: vertical-rl;
              transform: rotate(180deg); 
              font-size: 2rem;
              font-weight: 400;
              margin: 0;
              margin-block: 1.5rem;
            }

            div{
              width: 89%;
              height: 100%;
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 800px) {
  .row-1 {
    .child-col-2 {
      .svg-funnel-js:not(.svg-funnel-js--vertical) {
        padding-top: 10px !important;
      }
    }
  }
}

  @media screen and (min-width: 800px) and (max-width: 830px) {
  .row-1 {
    .child-col-2 {
      .sub-child-1 {
        div {
          &::after {
            top: -10px !important;
            height: 193px !important;
          }
        }
      }
    }
  }
}

@media screen and (min-width: 1919px) {
  .row-1 {
    .child-col-2 {
      .sub-child-1 {
        div {
          &::after {
            top: -34px !important;
            height: 486px !important;
          }
        }
      }
    }
  }

  
}