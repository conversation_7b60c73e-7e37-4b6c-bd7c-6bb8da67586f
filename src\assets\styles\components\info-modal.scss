@import '../utils/var';

.info-modal-main-container {
  background-color: $white;
  border: 2px solid $spanish-gray;
  border-radius: 2.07rem;
  overflow: hidden;
  // &::-webkit-scrollbar {
    //   display: none;
    // }
    
    .info-modal-secondary-div {
    overflow-y: auto;
    max-height: 89vh;
    
    .info-modal {
      display: contents;
      gap: 7rem;
      position: relative;
      padding: 4.5rem;
  
      img {
        position: absolute;
        right: 7rem;
        top: 3rem;
        cursor: pointer;
      }
      .row-1{
        padding: 20px;
      }

      .row-1,
      .row-2 {
        h3 {
          font-weight: 600;
          color: $squid-ink;
          margin-block-end: 3rem;
          font-family: 'Poppins', sans-serif;
        }
  
        div {
          font-size: 2.37rem;
          color: $modal-color-lighter;
          margin-block-end: 2rem;
  
          span:first-child {
            font-weight: 500;
            color: $modal-color !important;
            font-family: 'Poppins', sans-serif;
          }
  
          ol {
            margin-inline-start: 12rem;
            margin-block-start: 1rem;
          }
  
          .child-row {
            margin-inline-start: 12rem;
          }
        }
      }
    }
  }
}
