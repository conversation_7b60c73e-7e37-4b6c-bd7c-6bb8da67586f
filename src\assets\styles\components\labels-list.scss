.labels_list {
  &-item {
    width: 100%;
    background: #eef3fb;
    display: flex;
    justify-content: space-between;
    border: none;
    height: 7rem;
    align-items: center;
    margin-bottom: 6px;
    border-radius: 0.5rem;
    padding: 0 5rem 0 2.6rem;

    .list-item_left {
      display: flex;
      align-items: center;
      width: 100%;

      .label-icon {
        width: 3rem;
        height: 2rem;
        margin-right: 4.5rem;
        clip-path: polygon(65% 0, 100% 50%, 65% 100%, 0 100%, 0 0);
      }
      .item-name {
        font-weight: 500;
        font-size: 2rem;
        line-height: 3rem;
        width: 50%;
        text-align: left;
        color: #000000;
      }
    }

    .list-item_right {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 50%;

      .label-date {
        font-weight: normal;
        font-size: 2rem;
        line-height: 3rem;
        margin-right: 14rem;

        color: #000000;
      }

      .delete-icon {
        width: 2.5rem;
        height: 2.5rem;
        cursor: pointer;

        &:hover {
          transform: scale(1.2);
        }
      }
    }
  }

  @media only screen and (max-width: 1919px) {
    &-item {
      height: 55px;

      .list-item_left {
        .item-name {
          font-size: 17px;
          line-height: 1.2;
          margin-bottom: 0;
        }
      }

      .list-item_right {
        .label-date {
          font-size: 17px;
          line-height: 1.2;
        }
      }
    }
  }
}
