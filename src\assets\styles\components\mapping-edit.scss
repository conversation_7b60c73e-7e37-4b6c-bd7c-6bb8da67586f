.mapping-edit {
  .list-group {
    font-size: 3rem;
    .list-group-item {
      background-color: #eef3fc;
    }
    .list-group-item.active {
      background-color: #2c3f51;
      border-color: #2c3f51;
    }
    .list-content > div {
      flex: 1;
    }
  }
  .form-group {
    label {
      font-weight: 600;
      font-size: 2rem;
      line-height: 3rem;
      color: #2c3f51;
      margin-bottom: 0.8rem;
    }
    .form-control {
      border: 1px solid #d2d2d2;
      border-radius: 2rem;
      font-size: 3rem;
      line-height: 4rem;
      color: #000;
      padding: 1.9rem 2.8rem;
      font-weight: normal;

      &::placeholder {
        color: #919192;
      }
    }
    input.disabled:disabled {
      background-color: #fff;
    }
    textarea.disabled:disabled {
      background-color: #fff;
    }
    input.form-control {
      height: 7rem;
      padding: 1.9rem 2.8rem;
      margin-bottom: 1.8rem;
    }
    textarea.form-control {
      padding: 2.6rem 3.1rem;
      overflow: hidden;
      margin-bottom: 2.6rem;
    }
  }

  .color-picker {
    position: relative;
    height: 7rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .color-block {
      height: 3.2rem;
      width: 3.7rem;
      border-radius: 1rem;
    }

    img {
      height: 3.2rem;
    }
  }

  .swatches-container {
    margin-top: 3.4rem;

    .vc-swatches {
      width: 82%;
      margin: auto;

      &::-webkit-scrollbar-thumb {
        background-color: #f7892f;
      }
    }
  }

  .button-wrapper {
    text-align: right;

    button.btn {
      margin-top: 3.4rem;
      height: 5.6rem;
      padding: 1.4rem 2.3rem 1.2rem;
      font-weight: 600;
      font-size: 2rem;
      line-height: 1;
      letter-spacing: 0.001em;
    }
  }

  @media only screen and (max-width: 1919px) {
    .form-group {
      label {
        font-size: 15px;
        line-height: 22.5px;
        margin-bottom: 5px;
      }
    }

    .swatches-container {
      .vc-swatches {
        width: 90%;
      }
    }
  }
}
