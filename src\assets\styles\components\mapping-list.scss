.mapping_list {
  padding-bottom: 2rem;
  
  &-item {
    width: 100%;
    background-color: #f5f5f5;
    justify-content: space-between;
    border: none;
    height: 7rem;
    align-items: center;
    border-radius: 0.5rem;
    align-items: center;
    border-radius: 0.5rem;
    display: grid;
    grid-template-columns: 40% 15% 15% 15% 15%;
    padding-left: 2rem;

    &.header {
      background: #eef3fb;
      margin-bottom: 1rem;
    }
    .mapping-item_left {
      .mapping-icon {
        width: 3rem;
        height: 2rem;
        margin-right: 4.5rem;
        clip-path: polygon(65% 0, 100% 50%, 65% 100%, 0 100%, 0 0);
      }
      .item-name {
        font-weight: 500;
        font-size: 2rem;
        line-height: 3rem;
        text-align: left;
        color: #000000;
      }

      .template-name {
        text-overflow: ellipsis;
        width: 90%;
        display: block;
        white-space: nowrap;
        overflow: hidden;
      }
    }

    .mapping-item_right {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 50%;

      .mapping-date {
        font-weight: normal;
        font-size: 2rem;
        line-height: 3rem;
        margin-right: 14rem;

        color: #000000;
      }

      .delete-icon {
        width: 2.5rem;
        height: 2.5rem;
        cursor: pointer;

        &:hover {
          transform: scale(1.2);
        }
      }
    }
  }

  @media only screen and (max-width: 1919px) {
    &-item {
      height: 55px;

      .mapping-item_left {
        .item-name {
          font-size: 17px;
          line-height: 1.2;
          margin-bottom: 0;
        }
      }

      .mapping-item_right {
        .mapping-date {
          font-size: 17px;
          line-height: 1.2;
        }
      }
    }
  }
}
