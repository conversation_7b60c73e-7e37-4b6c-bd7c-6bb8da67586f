#menu-sidebar {
  width: 37rem;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 9999;
  height: 100%;
  color: #fff;
  transition: all 0.3s;
  background: #2c3f51;
  box-shadow: 0px 0px 25px 15px rgba(0, 0, 0, 0.15);
  border-radius: 2rem 0px 0px 2rem;
  opacity: 1;

  &.inactive {
    margin-right: -37rem;
    opacity: 0;
  }

  .sidebar-close {
    position: absolute;
    top: 2.2rem;
    right: 3.9rem;
    background: none;
    border: none;
    color: #f7892f;
    font-size: 6rem;
    padding: 0;
    margin: 0;
    line-height: 4rem;
  }

  .nav {
    width: 100%;

    .nav-item {
      position: relative;

      .sab-menu-wrapper {
        background-color: #2c3f51;
        position: absolute;
        top: 50%;
        left: -37rem;
        transform: translateY(-18%);
        z-index: 1000;
        border-radius: 1.5rem;
      }

      .dropdown-icon {
        width: 10px;
        height: 10px;
        position: absolute;
        left: 1rem;
        transform: translateY(95%);
      }

      a, button {
        margin: 1rem 0;
        padding: 8px 4rem;
        transition: all 0.3s;
        font-weight: 500;
        font-size: 2.5rem;
        line-height: 3.7rem;
        color: #ffffff;
        position: relative;

        &:hover {
          background-color: #f7892f;
        }
      }

      button {
        width: 100%;
        text-align: start;

        &.active_link {
          background-color: #f7892f;
        }

        &:disabled {
          opacity: 0.5;
        }
      }

      img {
        height: 4rem;
        width: 4rem;
        margin-right: 2rem;

        &.sub-icon {
          height: 3rem;
          width: 3rem;
          margin-right: 0;
          position: relative;
          bottom: 2px;
        }
      }

      .tooltip {
        opacity: 1;
        position: absolute;
        top: -24px;
        right: 17px;
        background-color: #f4f7fc;
        color: #333;
        padding: 5px 10px;
        border-radius: 5px;
        white-space: nowrap;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        display: block;
        font-size: 1.8rem;
      }
      
      .nav-item {
      
        position: relative;
      }
      
      .disabled {
        opacity: .5;
        position: relative;
        pointer-events: none;
      }
      
      .lock-icon {
        width: 15px;
        position: absolute;
        right: 0;
        top: 15px;
      }
    }
  }

  .beamerTrigger {
    position: absolute;
    bottom: 4rem;
    left: 4rem;
    background: none;
    border: none;

    img {
      width: 4rem;
      height: 4rem;
    }
  }
}
