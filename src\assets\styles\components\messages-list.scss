.messages-list {
  &_item {
    height: 12rem;
    background: #eef3fb;
    border-radius: 0.5rem;
    margin-bottom: 6px;
    padding: 2.5rem 5.6rem 2.5rem 3.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;

    .message-info {
      display: flex;
      align-items: center;

      .user-avatar {
        margin-right: 3.9rem;
        img {
          width: 7rem;
          height: 7rem;
          border-radius: 50%;
        }
      }

      .message-wrapper {
        .username {
          font-weight: 600;
          font-size: 2.5rem;
          line-height: 3.7rem;
          color: #000000;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          width: 25rem;
        }
        .message {
          font-weight: normal;
          font-size: 1.6rem;
          line-height: 2.4rem;
          color: #000000;
          width: 17.3rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }

    .message-status {
      font-weight: normal;
      font-size: 1.6rem;
      line-height: 2.4rem;
      color: #4232ff;

      &.error {
        color: #ff6a6a;
      }
    }

    .message-meta {
      display: flex;
      align-items: center;

      .message-date {
        font-weight: normal;
        font-size: 2rem;
        line-height: 3rem;
        color: #000000;
      }

      .delete-icon {
        width: 2.5rem;
        height: 2.5rem;
        cursor: pointer;

        &:hover {
          transform: scale(1.2);
        }
      }
    }
  }

  @media only screen and (max-width: 1919px) {
    &_item {
      .message-info {
        .message-wrapper {
          .message {
            font-size: 12px;
            width: 130px;
          }
        }
      }
      .message-status {
        font-size: 14px;
        line-height: 20px;
      }

      .message-meta {
        .message-date {
          font-size: 17px;
          line-height: 1.2;
        }
      }
    }
  }
}
