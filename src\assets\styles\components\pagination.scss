@import '../utils/var';

.pagination-div {
    display: flex;
    gap: 2rem;
    margin: auto;

    .select-page{
        background-color: $picton-blue !important;
        font-weight: 700;
        color: $white !important;
        cursor: context-menu !important;
    }
    
    button {
        padding-block: 1.4rem !important;
        cursor: pointer !important;
    }
    
    .arrow-btn:disabled {
        cursor: not-allowed !important;
    }

    .page-number,
    .arrow-btn{
        all: unset;
        border-radius: 1.2rem;
        background-color: $alice-blue;
        padding: 1rem 2rem;

        img{
            width: 1.5rem;
        }
    }

    .page-number{
        font-size: 2.07rem;
        padding: 0;
        text-align: center;
        width: 6.5rem;
    }
    
    div{
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        cursor: context-menu !important;
    }

    .right-arrow{
        width: 1.3rem;
    }
}