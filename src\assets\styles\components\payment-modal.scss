.payment-modal {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 300;
  // background-color: red;
  display: flex;
  align-items: center;
  justify-content: center;

  &_content {
    // position: absolute;
    // top: 50%;
    // left: 50%;
    // transform: translate(-50%, -50%);
    max-width: 90%;
    min-width: 550px;
    height: 200px;
    //max-height: 90%;
    background: #ffffff;
    box-shadow: 4px 4px 26px rgba(213, 211, 211, 0.3);
    overflow: hidden;
    z-index: 325;

    .payment-header {
      background-color: #2c3f51 !important;
      height: 50px;
      color: #ffffff;
      display: flex;
      align-items: center;
      font-weight: 600;
      font-size: 20px;
      line-height: 30px;
      padding: 0 20px;
      position: relative;

      .close-button {
        background-color: transparent;
        color: white;
        outline: none;
        border-color: transparent;
        font-size: 30px;
        position: absolute;
        right: 18px;
        top: 8px;
      }
    }
  }

  &_overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.85);
    z-index: 315;
  }
}

@media only screen and (max-width: 1919px) {
  .payment-modal {
    &_content {
      min-width: 431px;
      height: 550px;

      .payment-header {
        font-weight: 500;
        font-size: 18px;
        line-height: 22px;

        .close-button {
          font-size: 30px;
          top: 13px;
        }
      }
    }
  }
}
