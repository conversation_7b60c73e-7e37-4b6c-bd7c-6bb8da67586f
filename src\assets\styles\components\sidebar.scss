.app-sidebar {
  position: fixed;
  z-index: 10000;
  top: 0;
  right: 0;
  width: 60rem;
  height: 100%;
  background: #ffffff;
  display: block;
  box-shadow: 0px 4px 40px rgba(197, 196, 196, 0.25);
  transition: all 0.3s ease-out;

  &.inactive {
    width: 0;
  }

  &_header {
    // height: 25.1rem;
    background: #2c3d4f;
    color: #fff;

    .heading-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 2.5rem 4.5rem 2.5rem 4.5rem;

      .heading {
        font-weight: 600;
        font-size: 3rem;
        line-height: 4.5rem;
        margin: 0;
      }

      button.sidebar-close {
        background: none;
        border: none;
        color: #fff;
        font-size: 5rem;
        padding: 0 .7rem;
      }
    }
  }

  &_content {
    position: relative;
    height: calc(100% - 9rem);
    overflow: auto;
    background: white;
  }
}
