.accordion {
  padding: 0;

  dd {
    margin-left: 0;
  }
}

.accordion-item-title {
  position: relative;

  h4 {
    font-weight: 500;
    font-size: 2rem;
    line-height: 3rem;
    width: 50%;
    text-align: left;
    color: #000000;
  }

  .accordion-heading-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 50%;

    .title-date {
      font-weight: normal;
      font-size: 2rem;
      line-height: 3rem;
      margin-right: 14rem;

      color: #000000;
    }

    .delete-icon {
      width: 2.5rem;
      height: 2.5rem;

      &:hover {
        transform: scale(1.2);
      }
    }
  }
}

.accordion-item-trigger {
  width: 100%;
  background: #eef3fb;
  display: flex;
  justify-content: space-between;
  border: none;
  height: 7rem;
  align-items: center;
  padding: 0 3rem;
  border-radius: 0.5rem;
  transition: all 0.2s;

  &:hover {
    background: rgba(238, 243, 251, 0.3);
  }
}

.accordion-item-details {
  overflow: hidden;
  background: rgba(238, 243, 251, 0.3);
  padding: 3rem;
  padding-top: 0;
  margin-bottom: 6px;
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;

  .content {
    opacity: 0;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    transition: opacity 0.2s;

    p {
      margin-bottom: 2.5rem;
      font-weight: normal;
      font-size: 1.6rem;
      line-height: 2.4rem;
      color: #000000;
      white-space: pre-wrap;
    }

    button.btn {
      padding: 1.3rem 2.6rem;
      height: 5.6rem;
      font-size: 2rem;
      line-height: 1;
      letter-spacing: 0.001em;
    }
  }
}

.accordion-item.is-active {
  .accordion-item-trigger {
    background: rgba(238, 243, 251, 0.3);
    margin-bottom: 0;
  }
  .accordion-item-details {
    .content {
      opacity: 1;
    }
  }
}

.accordion-item-enter-active,
.accordion-item-leave-active {
  will-change: height;
  transition: height 0.2s ease;
}
.accordion-item-enter,
.accordion-item-leave-to {
  height: 0 !important;
}

@media only screen and (max-width: 1919px) {
  .accordion-item-trigger {
    height: 55px;
  }
  .accordion-item-title {
    h4 {
      font-size: 17px;
      line-height: 1.2;
      margin-bottom: 0;
    }

    .accordion-heading-right {
      .title-date {
        font-size: 17px;
        line-height: 1.2;
      }

      .delete-icon {
        font-size: 2rem;
        line-height: 3rem;
      }
    }
  }

  .accordion-item-details {
    .content {
      p {
        font-size: 14px;
        line-height: 1.2;
      }

      button.btn {
        line-height: 1.1;
      }
    }
  }
}
