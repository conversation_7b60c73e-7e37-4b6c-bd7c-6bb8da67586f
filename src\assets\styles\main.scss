// @import "node_modules/bootstrap/scss/bootstrap.scss";

@import 'base/font';
@import 'base/base';
@import 'base/layout';
@import 'base/animation';

@import 'utils/utility';

@import 'views/templates';
@import 'views/lists';
@import 'views/flow-mapping';
@import 'views/settings';
@import 'views/account';
@import 'views/report.scss';
@import 'views/user-roles.scss';

@import 'components/menu-sidebar';
@import 'components/templates-list';
@import 'components/create-template';
@import 'components/sidebar';
@import 'components/labels-list';
@import 'components/mapping-list';
@import 'components/create-label';
@import 'components/mapping-edit';
@import 'components/create-list';
@import 'components/messages-list';
@import 'components/color-palette';
@import 'components/spinner';
@import 'components/error-modal.scss';
@import 'components/payment-modal.scss';
@import 'components/campaign-performance.scss';
@import 'components/cumulative-performance.scss';
@import 'components/info-modal.scss';
@import 'components/pagination.scss';
@import 'components/add-user-details.scss';
@import 'components/create-user-roles.scss';
@import 'components/confirmation-modal.scss';
@import 'floating-vue/dist/style.css';

.v-popper__popper {
  font-size: 12px;
}
