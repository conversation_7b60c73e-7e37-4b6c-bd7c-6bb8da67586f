.active-link {
  background-color: #f7892f !important;
}

.block_ui {
  position: relative;
  &::after {
    content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.15);
    z-index: 2;
    cursor: wait;
  }
}

.error-modal .modal-dialog {
  max-width: var(--bs-modal-width);
  margin-right: auto;
  margin-left: auto;
  padding: 25px;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 10px;
}

// Tooltip styles
.info-icon-div {
  display: flex;
  align-items: center;
  gap: 1.5rem;

  label {
    margin: 0 !important;
  }

  .info-hint {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-top: 1rem;
    position: relative;

    p {
      display: none;
      position: absolute;
      color: #343a40;
      margin: 0 !important;
      padding: .7rem !important;
      background-color: gray;
      border-radius: 1rem;
      color: #fff;
      font-size: 1.7rem;
      width: 34rem !important;
      z-index: 1;
      left: 0;
      top: -8.6rem;

      a{
        color: #fff;
      }
    }

    &:hover p {
      display: block !important;
    }
  }
}