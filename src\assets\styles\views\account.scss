.account {
  padding: 2rem 5rem 5rem;

  &::-webkit-scrollbar-thumb {
    background: #34b7f1;

    &:hover {
      background: darken(#34b7f1, 10);
    }
  }

  &__heading-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 6.8rem;

    .account__heading {
      font-weight: 600;
      font-size: 5rem;
      line-height: 7.5rem;
      color: #000000;
    }

    .account__heading-right {
      button {
        span {
          font-weight: 400;
          font-size: 150%;
          line-height: 1;
          margin-right: 1.2rem;
          top: 2px;
          position: relative;

          @media only screen and (min-width: 1920px) {
            top: 3px;
          }
        }
      }

      img {
        margin-left: 10rem;
        cursor: pointer;
      }
    }
  }

  &__content {
    background: #eef3fb;
    border-radius: 2rem;

    .text-head {
      font-weight: 600;
      font-size: 2.5rem;
      line-height: 3.7rem;
      margin-bottom: 8px;
    }

    .text-subhead {
      font-weight: normal;
      font-size: 2rem;
      line-height: 3rem;
    }

    .user__info-wrapper {
      display: flex;
      justify-content: space-between;
      padding: 4.1rem 4.6rem 4.1rem;
      border-bottom: 1.5px solid #fff;

      .profile__wrapper {
        display: flex;
        width: 50%;

        img {
          width: 11.1rem;
          height: 11.1rem;
          border-radius: 50%;
          margin-right: 4.3rem;
        }

        .user__info {
          display: flex;
          flex-direction: column;
          justify-content: center;
          color: #000;
        }
      }

      .user__meta {
        width: 50%;
        display: flex;
        align-items: center;
        justify-content: space-around;

        &-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          color: #000000;
        }
      }
    }

    .account__info {
      &-wrapper {
        justify-content: space-between;
        padding: 3.4rem 6.7rem 3.4rem 6.2rem;
      }

      &-left {
        .screenshot {
          img {
            cursor: pointer;
            margin-top: 2.6rem;
            height: 38.2rem;
            margin-bottom: 5.1rem;
          }

          .buttons-wrapper {
            button {
              padding: 1.3rem 3.7rem;

              &:not(:last-of-type) {
                margin-right: 5.4rem;
              }
            }
          }
        }

        .subscription {
          margin-top: 5rem;
          p {
            margin-top: 2.8rem;
            font-weight: normal;
            max-width: 66rem;
          }

          button {
            margin-top: 2rem;
            padding: 1.3rem 4.3rem;
          }
        }

        .payment {
          margin-top: 10rem;
          p {
            margin-top: 2.8rem;
            font-weight: normal;
            max-width: 66rem;
          }

          button {
            margin-top: 5rem;
            padding: 1.3rem 4.3rem;
          }
        }
      }
    }
  }

  .btn-danger-custom {
    background: #fe7777;
    color: #fff;

    &:focus {
      color: #fff;
      background: #fe7777;
    }

    &:hover {
      color: #fe7777;
      background: #fff;
      border-color: #fe7777;
    }

    &:disabled {
      background: #d2d2d2;
      color: #fff;
      border-color: #d2d2d2;
    }
  }

  @media only screen and (max-width: 1919px) {
    &__content {
      .text-head {
        font-size: 18px;
        line-height: 28px;
      }

      .text-subhead {
        font-size: 14px;
        line-height: 21px;
      }
    }
  }
}
