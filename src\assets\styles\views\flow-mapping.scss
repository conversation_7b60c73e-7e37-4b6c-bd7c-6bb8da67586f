.mappings {
  padding: 0 5rem 2rem 5rem;

  &::-webkit-scrollbar-thumb {
    background: #34b7f1;

    &:hover {
      background: darken(#34b7f1, 10);
    }
  }

  .heading-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;

    .mappings_heading {
      font-style: normal;
      font-weight: 600;
      font-size: 5rem;
      line-height: 7.5rem;
      color: #000000;
    }

    img {
      cursor: pointer;
    }
  }

  &_info {
    max-width: 80%;
    font-weight: normal;
    font-size: 2.5rem;
    line-height: 3.7rem;
    color: #000000;
  }

  &_list-header {
    margin-top: 4rem;
    // margin-bottom: 4.8rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .input_wrapper {
      position: relative;
      width: 100rem;
      max-width: 70%;

      input {
        background: #f5f5f5;
        height: 6rem;
        padding: 1.8rem 6rem 1.8rem 7.2rem;
        color: rgba(156, 166, 175, 0.6);
        font-size: 2rem;
        line-height: 2.4rem;
        outline: none;
        box-shadow: none;
        background: #ffffff;
        width: 100%;
        border: 0.3rem solid #d2d2d2;
        border-radius: 6rem;

        &:focus {
          box-shadow: none;
          border: 0.3rem solid #d2d2d2;
        }

        &::placeholder {
          color: rgba(156, 166, 175, 0.6);
        }
      }

      img {
        position: absolute;
        left: 2.4rem;
        top: 50%;
        transform: translateY(-50%);
        width: 2rem;
        height: 2rem;
      }
    }

    button.btn {
      padding: 1.3rem 2.6rem;
      height: 5.6rem;
      font-size: 2rem;
      line-height: 1;
      letter-spacing: 0.001em;
    }
  }

  .status_block {
    margin-top: 1rem;
    min-height: 55px;
  }

  @media screen and (max-width: 1919px) {
    &_info {
      font-size: 18px;
      line-height: 25px;
    }

    &_list-header {
      button.btn {
        height: 38px;
      }
    }
  }
}
