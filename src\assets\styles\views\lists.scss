@import '../../styles/utils/var';

.lists {
  font-family: 'Poppins';
  padding: 0rem 5rem;

  .lists_top {
    position: relative;
    padding-bottom: 1.5rem;
  }

  p {
    margin: 0;
  }

  &::-webkit-scrollbar-thumb {
    background: #34b7f1;

    &:hover {
      background: darken(#34b7f1, 10);
    }
  }

  .heading-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .lists_heading {
      font-style: normal;
      font-weight: 600;
      font-size: 4.8rem;
      line-height: 7.5rem;
      color: #000000;
    }

    img {
      cursor: pointer;
    }
  }

  &_info {
    max-width: 80%;
    font-weight: normal;
    font-size: 2.8rem;
    line-height: 3.7rem;
    color: #000000;
  }

  &_list-header {
    margin-top: 4rem;
    // margin-bottom: 4.8rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    button.btn {
      padding: 1.3rem 2.6rem;
      height: 5.6rem;
      font-size: 2rem;
      line-height: 1;
      letter-spacing: 0.001em;
    }
  }

  &_body {
    border: 1px solid #e0e0e080;
    border-radius: 6px;
    overflow: hidden;
    margin-top: 3rem;

    .lists_list-body {
      overflow-x: auto;
      // height: 20rem;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .search_wrapper {
      display: flex;
      align-items: center;
      width: 100%;
      background-color: #eef3fb;

      .input_wrapper {
        position: relative;
        width: 46%;
        padding: 0.8rem;

        input {
          padding: 1.5rem 6rem 1.5rem 1.5rem;
          color: #000;
          font-size: 2.2rem;
          line-height: 2.4rem;
          outline: none;
          box-shadow: none;
          background: #ffffff;
          width: 100%;
          border: 0.3rem solid #d2d2d2;
          border-radius: 0.6rem;
          border: 1px solid #d2d2d280;

          &::placeholder {
            color: rgba(156, 166, 175, 0.6);
          }
        }

        .search-icon {
          position: absolute;
          right: 2.4rem;
          top: 50%;
          transform: translateY(-50%);
          width: 3rem;
        }
      }

      .selected-campaigns-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1.5rem 2rem;

        .campaign-counts {
          font-size: 2.2rem;
          line-height: 2.4rem;
          color: #000;
        }

        .download-campaigns-icon {
          display: flex;
          align-items: center;
          gap: 1.2rem;
          margin-inline-start: 4rem;
          cursor: pointer;
          text-decoration: none;

          p {
            font-weight: 600;
            color: $picton-blue;
          }
        }
      }
    }

    .list_header,
    .lists_list-item .list-row {
      display: grid;
      grid-template-columns: 7rem calc(100% - 154rem) 33rem 16rem 16rem 10rem 10rem 23rem 26rem 13rem;
      align-items: center;
    }

    .list_header {
      background-color: #eef3fb;
      border-block: 1px solid #e0e0e080;
      color: #2c3f51;

      > div {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 7.8rem;
        border: 1px solid #e0e0e080;

        p {
          font-size: 2.6rem;
          margin: 0;
        }

        .name {
          font-size: 2.9rem;
        }
      }
    }

    .lists_list-item .list-row {
      > div {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 7.8rem;
        border: 1px solid #e0e0e080;

        p {
          background-color: $picton-blue !important;
          font-size: 1.8rem;
        }

        .download-campaigns {
          width: 3rem;
          height: 2.6rem;
        }

        input[type="checkbox"]:checked {
          background-color: $picton-blue;
          border-color: $picton-blue;
        }
      }
    }

    .list_header div:nth-child(2),
    .lists_list-item .list-row div:nth-child(2),
    .list_header div:nth-child(3),
    .lists_list-item .list-row div:nth-child(3) {
      justify-content: left;
      padding-inline-start: 2rem;

      p {
        width: 98%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    .lists_list-item .list-row div:nth-child(2),
    .lists_list-item .list-row div:nth-child(3) {
      p {
        background-color: transparent !important;
        font-size: 2.5rem;
        font-weight: 500;
      }
    }
  }

  .status_block {
    min-height: 55px;
    position: absolute;
    width: 100%;
    z-index: 1;
    top: 1rem;
  }
}

.lists_list {
  // display: flex;
  // flex-direction: column;
  // height: calc(100vh - 26rem);

  &-item {
    width: 100%;
    border-radius: 0.5rem;

    .list-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 500;
      font-size: 3rem;
      width: 100%;
      .list-item {
        word-wrap: break-word;
        font-weight: 400;
        font-size: 2.5rem;
        width: 50%;
        text-align: center;
        color: #000000;

        span {
          display: inline-block;
          font-weight: 700;
        }
        &:first-child {
          text-align: left;
        }
      }
    }
  }

  .no-data-find {
    margin: auto;
  }
}
