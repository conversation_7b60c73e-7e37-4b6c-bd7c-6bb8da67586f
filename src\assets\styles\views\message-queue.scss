.message-queue {
  padding: 2rem 10rem;

  &::-webkit-scrollbar-thumb {
    background: #34b7f1;

    &:hover {
      background: darken(#34b7f1, 10);
    }
  }

  .heading-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;

    .message-queue_heading {
      font-style: normal;
      font-weight: 600;
      font-size: 5rem;
      line-height: 7.5rem;
      color: #000000;
    }

    img {
      cursor: pointer;
    }
  }

  &_info {
    max-width: 80%;
    width: 867px;
    font-weight: normal;
    font-size: 2.5rem;
    line-height: 3.7rem;
    color: #000000;
    margin-bottom: 10rem;
  }

  &_header {
    margin-bottom: 4.8rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .input_wrapper {
      position: relative;
      width: 100rem;
      max-width: 70%;

      input {
        border: 2px solid #d2d2d2;
        box-sizing: border-box;
        border-radius: 2rem;
        height: 7rem;
        width: 100%;
        font-weight: 500;
        font-size: 2rem;
        line-height: 3rem;
        color: #919192;
        padding: 2rem 5.5rem 2rem 2.5rem;

        &:focus {
          outline: none;
        }
      }

      img {
        position: absolute;
        right: 2.4rem;
        top: 50%;
        transform: translateY(-50%);
        width: 2rem;
        height: 2rem;
      }
    }

    button.btn-clear {
      padding: 1.3rem 2.6rem;
      height: 5.6rem;
      font-size: 2rem;
      line-height: 1;
      letter-spacing: 0.001em;
    }
  }

  .empty-queue {
    margin-top: 22.4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    img {
      width: 9.6rem;
      height: 9.6rem;
    }

    .empty-text {
      margin-top: 4px;
      font-weight: 500;
      font-size: 2.5rem;
      line-height: 3.7rem;
      color: #000000;
    }
  }

  @media screen and (max-width: 1919px) {
    &_info {
      width: 600px;
      font-size: 18px;
      line-height: 25px;
    }

    &_header {
      button.btn-clear {
        height: 38px;
      }
    }
  }
}
