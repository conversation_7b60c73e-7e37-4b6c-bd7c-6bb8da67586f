@import '../../styles/utils/var';

.report {
  position: relative;

  .report-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: $alice-blue;
    padding: 2rem 5rem;
    justify-content: space-between;

    .report-header-left {
      display: flex;
      align-items: center;
      gap: 2rem;
      position: relative;

      .dropdown {
        width: 2.5rem;
        cursor: pointer;
        margin-left: 2rem;
      }
    }

    .report-dropdown {
      position: absolute;
      bottom: -5.5rem;
      left: 55rem;
      background-color: #fff;
      border: 1px solid #34b7f1;
      border-radius: 4px;
      cursor: pointer !important;
      width: max-content;
      box-shadow: 6px 6px 54px 0px #0000000D;

      button {
        all: unset;
        font-size: 3rem;
        padding: 1rem;
        display: flex;
        align-items: center;

        img {
          width: 30px;
          margin-right: 1rem;
        }
      }
    }

    img {
      width: 6rem;
    }

    h3 {
      color: $eerie-black;
      text-shadow: 2px 3px 5px $text-shadow-color;
      font-weight: 700;
      font-size: 4.75rem;
    }
    .report-header-left {
      display: flex;
      align-items: center;
      gap: 15px;
      position: relative;

      .dropdown {
        width: 20px;
        height: 20px;
        cursor: pointer;

      }
      .report-dropdown {
        position: absolute;
        bottom: -.8rem;
        left:62rem;
        background-color: #fff;
        border: 1px solid #34b7f1;
        border-radius: 4px;
        cursor: pointer !important;
        width: max-content;
        box-shadow: 6px 6px 54px 0px #0000000D;
        z-index: 1;

        button {
          all: unset;
          font-size: 3rem;
          padding: 1rem;
          display: flex;
          align-items: center;

          img {
            width: 30px;
            margin-right: 1rem;
          }
        }

      }
    }
  }

  .data-condition {
    position: fixed;
    width: 100%;
  }

  .data {
    display: grid;
    gap: 5rem;
    padding-inline: 5rem;
    padding-block: 3.6rem;

    .date-select-div {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .date-range {
        position: relative;

        .calendar {
          position: absolute;
          top: 2.3rem;
          left: 2.37rem;
          width: 3.7rem;
        }

        .vue-daterange-picker {
          height: 8.44rem;
          width: 49rem !important;

          .reportrange-text {
            border-radius: 1rem;
            border-color: $alice-blue !important;
            font-size: 2.96rem !important;
            padding: 2.7rem 2rem 2.7rem 7rem !important;
            height: 100%;
            display: flex;
            align-items: center;
            color: $eerie-black-lighter !important;
            box-shadow: 6px 6px 54px 0px #0000000d;
            background-color: transparent;

            span {
              line-height: 1 !important;
            }
          }
        }
        .dropdown-icon {
          position: absolute;
          top: 3.7rem;
          right: 2.07rem;
          cursor: pointer;
          z-index: -1;
          width: 1.5rem;
        }
      }

      .report-div {
        display: flex;
        align-items: center;
        gap: 2rem;

        button {
          &:first-child {
            all: unset;
            border: 1px solid $picton-blue !important;
            color: $picton-blue;
            font-weight: 600;
            font-size: 2.6rem;
            border-radius: 0.8rem;
            padding: 1.8rem 2.9rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: 0.2s;

            &:hover {
              background-color: $picton-blue-lighter;
              color: $modal-color;
            }
          }
        }
        .read-report {
          color: $modal-color;
          background-color: $picton-blue-lighter;
        }

        div {
          display: flex;
          align-items: center;
          gap: 1rem;
          border: 1px solid $picton-blue;
          font-size: 2.65rem;
          color: $picton-blue;
          padding: 1.9rem 2.9rem;
          border-radius: 1rem;
          font-weight: 600;

          img {
            cursor: pointer;
            width: 3.7rem;
            aspect-ratio: 1;
          }
        }

        button {
          a {
            all: unset;
            display: block;
            font-size: 2.65rem;
            font-weight: 600;
            padding: 0.5rem 2.9rem;
            border-color: transparent !important;

            &:hover {
              border-color: $picton-blue !important;
            }
          }
        }
      }
    }

    .content-div {
      display: grid;
      gap: 5rem;
    }

    .not-found-div img {
      width: 53rem;
      margin-block-end: 5rem;
    }

    .not-found-div,
    .report-loader {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 64vh;

      div p {
        margin: auto;
        width: fit-content;
        font-size: 2.5rem;
        font-weight: 400;
      }
    }
  }

  .Info-modal-div {
    position: fixed;
    top: 7.5rem;
    width: 100%;
    padding-inline: 5rem;
    z-index: 1;
  }

  .top-to-scroll {
    all: unset;
    background-color: $picton-blue;
    clip-path: circle(50%);
    width: fit-content;
    padding: 1.6rem;
    position: fixed;
    right: 5rem;
    bottom: 2.5rem;
    cursor: pointer;
  }
}

.campaign-performance {

  .campaign-performance-header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 15px;
    position: relative;

    .dropdown {
      width: 20px;
      cursor: pointer;

    }

    .report-dropdown {
      background-color: #fff;
      border: 1px solid #34b7f1;
      border-radius: 4px;
      cursor: pointer !important;
      width: -moz-max-content;
      width: max-content;
      box-shadow: 6px 6px 54px 0px rgba(0, 0, 0, 0.0509803922);
      position: absolute;
      left: 68rem;
      bottom: 3px;
      z-index: 1;

      button {
        all: unset;
        font-size: 3rem;
        padding: 1rem;
        display: flex;
        align-items: center;
      }
    }
  }

  .pagination-div {
    display: flex;
    gap: 2rem;
    margin: 30px auto;
    justify-content: center;

    .select-page {
      background-color: #34b7f1 !important;
      font-weight: 700;
      color: #fff !important;
      cursor: context-menu !important;
    }


    .page-number,
    .arrow-btn {
      all: unset;
      border-radius: 1.2rem;
      background-color: #f0f8ff;
      padding: 1rem 2rem;
    }

    .arrow-btn:disabled {
      cursor: not-allowed !important;
    }

    .page-number {
      font-size: 2.07rem;
      padding: 0;
      text-align: center;
      width: 6.5rem;
    }

    .right-arrow {
      width: 1.3rem;
    }

  }
}
