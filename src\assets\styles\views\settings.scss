.settings {
  padding: 2rem 5rem 0;

  &__heading-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 6.8rem;

    .settings__heading {
      font-weight: 600;
      font-size: 5rem;
      line-height: 7.5rem;
      color: #000000;
    }

    img {
      cursor: pointer;
    }
  }

  &__content {
    background: #eef3fb;
    border-radius: 0.8rem;
    padding: 5rem;
    display: flex;
    justify-content: space-between;

    &-left {
      width: 45%;
    }

    &-right {
      width: 45%;

      .users-list_heading {
        font-size: 2rem;
        line-height: 3rem;
        font-weight: 500;
        margin: 5rem 0 2.6rem 0;
      }

      .users-list {
        margin-bottom: 0;
        padding: 3rem;
        max-height: 27rem;
        overflow: auto;
        border-radius: 1rem;
        background: white;

        &-item {
          list-style: none;
          font-weight: normal;
          font-size: 2rem;
          line-height: 3rem;
          color: #000000;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-right: 1.2rem;

          &:not(:last-of-type) {
            margin-bottom: 3rem;
          }

          img {
            width: 3rem;
            cursor: pointer;

            &:hover {
              transform: scale(1.2);
            }
          }
        }
      }
    }
  }

  &__form-group {
    display: flex;
    flex-direction: column;

    label {
      font-weight: 500;
      font-size: 2.5rem;
      line-height: 3.7rem;
      color: #000000;
      margin-bottom: 2.3rem;
    }

    input {
      background: #ffffff;
      border-radius: 1rem;
      height: 6rem;
      width: 100%;
      border: none;
      font-weight: normal;
      font-size: 1.6rem;
      line-height: 2.4rem;
      padding: 1.7rem 1.8rem;
      margin-bottom: 3.8rem;

      &::placeholder {
        color: #d2d2d2;
      }

      &:focus {
        outline: none;
      }
    }

    button {
      align-self: flex-start;
    }
  }

  &__toggle-box {
    margin-bottom: 5.4rem;

    .heading {
      font-weight: 500;
      font-size: 2.5rem;
      line-height: 3.7rem;
      color: #000000;
      margin-bottom: 2.7rem;
    }

    .toggle__item {
      display: flex;
      align-items: center;

      &:not(:last-child) {
        margin-bottom: 3.7rem;
      }
    }

    .toggle__icon {
      position: relative;
      display: inline-block;
      width: 7.5rem;
      height: 2rem;
      margin-right: 5.4rem;
      margin-bottom: 0;

      input {
        opacity: 0;
        width: 0;
        height: 0;
      }

      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #e6e6e6;
        border-radius: 6rem;
        -webkit-transition: 0.4s;
        transition: 0.4s;
      }

      .slider:before {
        position: absolute;
        content: '';
        height: 2.5rem;
        width: 2.5rem;
        left: -0.2rem;
        bottom: -0.2rem;
        background-color: white;
        border-radius: 50%;
        -webkit-transition: 0.4s;
        transition: 0.4s;
      }

      input:checked + .slider {
        background-color: rgba(52, 183, 241, 0.4);
      }

      input:checked + .slider:before {
        background-color: #34b7f1;
      }

      input:focus + .slider {
        box-shadow: 0 0 1px rgba(52, 183, 241, 0.4);
      }

      input:checked + .slider:before {
        -webkit-transform: translateX(5.3rem);
        -ms-transform: translateX(5.3rem);
        transform: translateX(5.3rem);
      }
    }

    .toggle__text {
      font-weight: 500;
      font-size: 2rem;
      line-height: 3rem;
      color: #000000;
    }
  }

  @media only screen and (max-width: 1919px) {
    &__form-group {
      input {
        height: 45px;
        font-size: 13px;
        line-height: 1;
      }

      label {
        font-size: 19px;
        line-height: 24px;
      }
    }

    &__content {
      &-right {
        .users-list_heading {
          font-size: 15px;
          line-height: 21px;
        }

        .users-list {
          &-item {
            font-size: 14px;
            line-height: 21px;
          }
        }
      }
    }

    &__toggle-box {
      .heading {
        font-size: 19px;
        line-height: 24px;
      }

      .toggle__text {
        font-size: 14px;
        line-height: 18px;
      }
    }
  }
}
