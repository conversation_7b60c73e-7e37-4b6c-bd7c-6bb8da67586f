@import '../../styles/utils/var';

.templates {
  font-family: 'Poppins';
  padding: 0rem 5rem;

  .campaign-error-container {
    position: fixed;
    width: 80%;
    left: 0;
    right: 0;
    margin: auto;
  }

  &::-webkit-scrollbar-thumb {
    background: #34b7f1;

    &:hover {
      background: darken(#34b7f1, 10);
    }
  }

  .heading-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .templates_heading {
      font-style: normal;
      font-weight: 600;
      font-size: 4.8rem;
      line-height: 7.5rem;
      color: #000000;
    }

    img {
      cursor: pointer;
    }
  }

  &_info {
    max-width: 90%;
    font-weight: normal;
    font-size: 2.8rem;
    line-height: 3.7rem;
    color: #000000;
  }

  &_list-header {
    margin-top: 10rem;
    margin-bottom: 4.8rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .input_wrapper {
      position: relative;
      width: 100rem;
      max-width: 70%;

      input {
        background: #f5f5f5;
        height: 6rem;
        padding: 1.8rem 6rem 1.8rem 7.2rem;
        color: rgba(156, 166, 175, 0.6);
        font-size: 2rem;
        line-height: 2.4rem;
        outline: none;
        box-shadow: none;
        background: #ffffff;
        width: 100%;
        border: 0.3rem solid #d2d2d2;
        border-radius: 6rem;

        &:focus {
          box-shadow: none;
          border: 0.3rem solid #d2d2d2;
        }

        &::placeholder {
          color: rgba(156, 166, 175, 0.6);
        }
      }

      img {
        position: absolute;
        left: 2.4rem;
        top: 50%;
        transform: translateY(-50%);
        width: 2rem;
        height: 2rem;
      }
    }

    button.btn {
      padding: 1.3rem 2.6rem;
      height: 5.6rem;
      font-size: 2rem;
      line-height: 1;
      letter-spacing: 0.001em;
    }
  }

  .table-container {
    margin: 2.5rem 0;
    border-radius: 6px;
    height: 77%;
    border: 1px solid #E0E0E080;

    .custom-table-body-height {
      height: calc(100% - 9rem) !important;
    }

    .loading-container {
      margin: auto;
      font-size: 3.5rem;
      font-weight: bold;

      .app-spinner {
        height: fit-content;
        background-color: transparent;
      }
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      background-color: #eef3fb;
      padding: 1.2rem;
      border-top-right-radius: 6px;
      border-top-left-radius: 6px;

      .search-input {
        padding: 8px;
        font-size: 2.2rem;
        border: 1px solid #ccc;
        border-radius: 5px;
        width: 50%;
        color: #000;
        outline: none;
        background-color: #fff;
      }

      .filters {
        display: flex;
        align-items: center;
        gap: 10px;
        width: 40%;
        justify-content: end;

        p {
          margin: 0;
          font-size: 2.2rem;
        }

        .filter-select {
          padding: 8px;
          font-size: 14px;
          border: 1px solid #ccc;
          border-radius: 5px;
          outline: none;
          cursor: pointer;
        }
      }

     
    }

    // .custom-table {
    //   width: 100%;
    //   border-collapse: collapse;
    //   text-align: left;
    //   height: calc(100% - 8rem) !important;

    //   thead {
    //     background-color: #eef3fb;

    //     tr th:first-child {
    //       display: flex;
    //       justify-content: center;
    //       align-items: center;
    //       padding-block: 2.46rem;
    //     }

    //     tr th:nth-child(2) {
    //       font-size: 2.95rem;
    //       padding-block: 1.21rem;
    //     }

    //     th {
    //       display: flex;
    //       align-items: center;
    //       padding: 1.4rem;
    //       border: 1px solid #ddd;
    //       font-size: 2.7rem;
    //       font-weight: 500;
    //       // height: 10rem;
    //     }
    //   }

    //   thead tr,
    //   tbody tr {
    //     display: grid;
    //     grid-template-columns: 6rem minmax(15rem, 1fr) 17rem 20rem 21rem 17rem 17rem 26rem;
    //     width: 100%;
    //     // align-items: center;
    //   }

    //   tbody {
    //     overflow: auto;

    //     &::-webkit-scrollbar {
    //       display: none;
    //     }

    //     tr {
    //       &:hover {
    //         background-color: #f9f9f9;
    //       }
    //     }

    //     td {
    //       padding: 1.7rem;
    //       border: 1px solid #ddd;
    //       font-size: 2.45rem;
    //       font-weight: 500;
    //       // background-color: #2c3f51;

    //       .badge {
    //         padding: 5px 10px;
    //         border-radius: 12px;
    //         font-size: 12px;
    //         text-align: center;
    //         background-color: $picton-blue;
    //         color: #fff;
    //         font-weight: 600;

    //         &.active-quality {
    //           // background-color: #d1f7c4;
    //           // color: #0a7f00;
    //         }
    //       }

    //       &:nth-child(5), &:last-child {
    //         font-size: 1.4rem;
    //         padding-block: 1.9rem;
    //       }
    //     }
    //   }
    // }

    .custom-table {
      width: 100%;
      border-collapse: collapse;
      text-align: left;
      // height: calc(100% - 8rem) !important;
      height: 100%;

      thead {
        background-color: #eef3fb;

        tr {
          th:first-child {
            display: flex;
            justify-content: center;
            align-items: center;
            padding-block: 2.46rem;
          }

          th:nth-child(2) {
            font-size: 2.95rem;
            padding-block: 1.21rem;
          }

          th {
            display: flex;
            align-items: center;
            padding: 1.4rem;
            border: 1px solid #ddd;
            font-size: 2.7rem;
            font-weight: 500;
          }
        }
      }

      thead tr,
      tbody tr {
        display: grid;
        grid-template-columns: 6rem 2fr 1fr 1fr 1fr 2fr;
        width: 100%;
      }

      tbody {
        overflow: auto;

        &::-webkit-scrollbar {
          display: none;
        }

        tr {
          transition: background-color 0.3s ease;

          &:hover {
            background-color: #f0f0f0; // Light grey on hover
          }

          td {
            padding: 2.5rem;
            border: 1px solid #ddd;
            font-size: 2.45rem;
            font-weight: 500;
            position: relative;

            input {
              cursor: pointer;
            }

            input:disabled {
              cursor: not-allowed;
            }
          }

          /* First column - Template Name */
          td:nth-child(2) {
            position: relative;
            line-height: 3.7rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;

            /* Insights Button */
            .insights-btn {
              font-size: 1.6rem;
              color: #34b7f1;
              font-weight: 600;
              cursor: pointer;
              background: none;
              border: none;
              outline: none;
              padding: 0;
              text-decoration: underline;
              position: absolute;
              bottom: 0px;
              left: 15px;
              opacity: 0;
              visibility: hidden;
              transition: opacity 0.5s ease, visibility 0.5s ease;
            }

          }

          /* Show Insights button on hover */
          &:hover td:nth-child(2) {
            .insights-btn {
              opacity: 1;
              visibility: visible;
            }
          }
        }

        .badge {
          padding: 5px 10px;
          border-radius: 12px;
          font-size: 12px;
          text-align: center;
          background-color: #34b7f1;
          color: #fff;
          font-weight: 600;
        }
      }
    }

  }

  .template-actions {
    display: flex;
    align-items: center;
    width: 40%;
    gap: 10px;
    margin-left: 15px;
  }

  .template-edit,
  .template-delete {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
    cursor: pointer;
    opacity: 1;
    transition: opacity 0.3s ease;
  }

  .template-edit img,
  .template-delete img {
    width: 20px;
    height: 20px;
  }

  .template-edit.disabled img,
  .template-delete.disabled img {
    opacity: 0.3;
    /* Makes the disabled icon less visible */
    pointer-events: none;
  }

  .template-edit p,
  .template-delete p {
    margin: 0;
    color: #34B7F1;
  }

  .templates-selected p {
    margin: 0;
  }

  .template-edit.disabled p,
  .template-delete.disabled p {
    color: #ccc !important;
  }


  .badge-approved {
    background-color: #28a745;
    /* Green */
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
  }

  .badge-pending {
    background-color: #ffc107;
    /* Yellow */
    color: black;
    padding: 5px 10px;
    border-radius: 5px;
  }

  .badge-rejected {
    background-color: #dc3545;
    /* Red */
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
  }

  /* Pagination Controls */
  .pagination-controls {
    position: fixed;
    bottom: 0%;
    left: 50%;
  }

  .pagination-controls button {
    background: none;
    border: none;
    cursor: pointer;
  }

  .pagination-controls button svg {
    width: 35px;
    height: auto;
    transition: color 0.3s ease, opacity 0.3s ease;
  }

  .pagination-controls button:disabled {
    cursor: not-allowed;
    opacity: 0.3;
  }

  .no-data {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    color: #888;
    padding: 20px;
  }

  // @media screen and (max-width: 1919px) {
  //   &_info {
  //     font-size: 18px;
  //     line-height: 25px;
  //   }

  //   &_list-header {
  //     button.btn {
  //       height: 38px;
  //     }
  //   }
  // }
}