.users-roles-template {
  height: 100%;

  h1 {
    font-weight: 600;
    font-size: 5rem;
    line-height: 7.5rem;
    color: #000000;
    margin-inline: auto;
    width: 90%;
  }

  .secondary {
    width: 90%;
    height: 85%;
    background-color: #eef3fb;
    border-radius: 8px;
    margin: auto;
    padding: 5rem;

    .main-content {
      display: flex;
      flex-direction: column;
      height: 100%;

      .user-role-header {
        display: flex;
        justify-content: space-between;
      }

      .section-title {
        font-weight: 500;
      }

      .btn-div {
        display: flex;
        justify-content: end;
        align-items: center;
        gap: 2rem;
      }

      .description {
        margin-block-start: 2rem;

        h2 {
          font-size: 3.7rem;
          color: #212529;
        }
      }

      .user-role-permission-table {
        display: flex;
        flex-direction: column;
        border: 2px solid #cbd6e2;
        margin-block-start: 1rem;
        height: calc(100% - 16.5rem);
        background-color: #fff;

        .search-div {
          border-bottom: 2px solid #cbd6e2;
          padding: 1.5rem 2.3rem 1.5rem 1.5rem;
          position: relative;

          input {
            all: unset;
            width: 45%;
            background-color: #eef3fb;
            font-weight: 400;
            font-size: 2.3rem;
            padding: 0.8rem 5rem 0.8rem 1.3rem;
            border: 1px solid #cbd6e2;
            color: #33475b;
            border-radius: 6px;
          }

          img {
            width: 2.5rem;
            position: absolute;
            top: 2.8rem;
            left: 46%;
          }
        }

        .table-title,
        .table-value {
          display: grid;
          grid-template-columns: 25% 38% 19% 18%;

          p {
            margin: 0;
          }
        }

        .table-title p {
          font-size: 2.7rem;
          font-weight: 600;
          color: #33475b;
          border-right: 2px solid #cbd6e2;
          border-bottom: 2px solid #cbd6e2;
          padding: 1rem 2.4rem;

          &:last-child {
            border-right: none;
          }
        }

        .table-value {
          > div {
            border-right: 2px solid #cbd6e2;
            border-bottom: 2px solid #cbd6e2;
            padding: 2rem 2rem;
            color: #33475b;

            &:last-child {
              border-right: none;
            }

            button {
              border: 2px solid #34B7F1;
              background: #fff;
              color: #34B7F1;
              font-weight: 500;
            }

            p {
              font-size: 2.5rem;
            }
          }

          .permissions-value {
            display: flex;
            gap: 5px;
            align-items: baseline;
            justify-content: space-between;

            .initial-permission {
              border-radius: 6px;
              padding: 1rem;
              font-size: 2.5rem;


              &:disabled {
                border-color: #a1a1a1;
                color: #a1a1a1;
                cursor: not-allowed;
                opacity: 0.7;
              }
            }

            div {
              display: flex;
              flex-wrap: wrap;
              gap: 7px;
            }

            p {
              display: inline-block;
              background-color: #52c2f3;
              color: #fff;
              font-size: 2.5rem;
              font-weight: 600;
              padding: 4px 8px;
              border-radius: 6px;
            }

            .edit-permissions {
              all: unset;
              position: relative !important;
              display: inline-block;
              border: none;
              background: none;
              padding-inline: 5px;
              cursor: pointer;

              &:disabled {
                cursor: not-allowed;

                img {
                  opacity: 0.5;
                }
              }

              img {
                width: 2.5rem;
              }

              //   .tooltip-text {
              //     justify-content: space-between;
              //     visibility: hidden;
              //     width: 130px;
              //     background-color: #eef3fb;
              //     color: #33475b;
              //     text-align: center;
              //     border-radius: 5px;
              //     padding: 5px 0;
              //     position: absolute;
              //     z-index: 1;
              //     bottom: 125%; /* Position the tooltip above the button */
              //     left: 50%;
              //     transform: translateX(-50%);
              //     opacity: 0;
              //     transition: opacity 0.3s;

              //     span {
              //       font-size: 2rem;
              //     }
              //   }

              //   &:hover .tooltip-text {
              //     visibility: visible;
              //     opacity: 1;
              //   }

              //   .dropdown-icon {
              //     margin-left: 8px;
              //     fill: #eef3fb; /* Adjust the color as needed */
              //   }
            }
          }

          .approovel-permission {
            display: flex;
            justify-content: center;
            align-items: baseline;

            button {
              all: unset;
              cursor: pointer;
            }

            div {
              width: 100%;
              display: flex;
              // justify-content: space-between;
              gap: 3rem;

              button img {
                width: 4rem !important;
              }
            }

            img {
              width: 2.8rem;
              cursor: pointer;
            }
          }
        }

        .table-value:last-child {
          border-bottom: none;
        }

        .table-value-column {
          display: flex;
          flex-direction: column;
          height: 100%;
          overflow-x: auto;

          .useremail-value p {
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 100%;
            font-weight: 500;
            overflow: hidden;
            color: #000;
          }

          .no-list-found {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          &::-webkit-scrollbar {
            display: none;
          }

          select {
            cursor: pointer;
            width: 100%;
            outline: none;
            border: none;
            font-weight: 500;
            font-size: 2.5rem;

            option {
              padding: 1rem;
            }

            &:disabled {
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }
}
