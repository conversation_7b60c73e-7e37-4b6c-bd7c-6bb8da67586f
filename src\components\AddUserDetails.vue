<template>
    <div class="add-user-details">
        <div class="success-msg">
            <success-component v-show="changesSaved" :successMessage="'Changes Saved Successfully'"
                 @close="$emit('closeToast')" />
            <ErrorComponent v-show="conditionError || errorWhileSave" :errorMessage="errorMessage" />
        </div>
        <div class="back-div">
            <img @click="selectUserRole" :src="backIcon" alt="back btn">
            <h3>Go Back</h3>
        </div>
        <Spinner v-if="loading" />
        <div v-else class="user-details">
            <div class="content">
                <div v-show="selectDropDown" class="add-user-detail">
                    <div @click="toggleSection" class="title">
                        <img :src="dropDown" :class="{ rotated: !isUserDetailExpanded }" alt="dropdown">
                        <p>Add User Details</p>
                    </div>
                    <transition name="expand">
                        <div v-if="isUserDetailExpanded" class="user-detail">
                            <span>Please enter the user details:</span>
                            <label for="">Email Address*</label>
                            <input v-model="userEmail" type="text" value="" placeholder="<EMAIL>">
                        </div>
                    </transition>
                </div>
                <div class="permissions">
                    <div v-show="selectDropDown" @click="toggleSection" class="title">
                        <img :src="dropDown" :class="{ rotated: !isPermissionsExpanded }" alt="dropdown">
                        <p>Set Permissions</p>
                    </div>
                    <div v-show="!selectDropDown" class="title" :style="{ borderTop: !selectDropDown ? 'none' : '' }">
                        <p>Choose Permissions</p>
                    </div>
                    <transition name="expand">
                        <div v-if="isPermissionsExpanded || !selectDropDown">
                            <span>Set the permissions for the user here:</span>
                            <div class="permission-status" :class="!selectDropDown ? 'full-height' : 'max-height' ">
                                <div v-for="(permission, index) in permissions" :key="index">
                                    <label>{{ permission.name }}</label>
                                    <input @change="togglePermission(permission.id)"
                                        :checked="permissionsAllowed.includes(permission.name)" type="checkbox">
                                </div>
                            </div>
                        </div>
                    </transition>
                </div>
            </div>
            <button @click="saveChanges" class="btn btn-primary">Save Changes</button>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex'
import axios from '@/utils/api.js'
import BackIcon from '@/assets/icons/back.svg';
import DropDown from '@/assets/icons/dropdown_icon.svg';
import Spinner from '@/components/Spinner'
import SuccessComponent from '@/components/Success'
import ErrorComponent from '@/components/Error'

export default {
    name: 'AddUserDetails',
    components: { Spinner, SuccessComponent, ErrorComponent },
    data() {
        return {
            backIcon: BackIcon,
            dropDown: DropDown,
            isUserDetailExpanded: true,
            isPermissionsExpanded: false,
            permissions: [],
            loading: true,
            userEmail: '',
            selectedPermissionsId: [],
            conditionError: false,
            errorMessage: '',
        }
    },
    computed: {
        ...mapState(['userData'])
    },
    props: {
        selectUserRole: {
            type: Function,
            required: true,
        },
        selectDropDown: {
            type: Boolean,
        },
        selectedUserEmail: {
            type: String,
        },
        permissionsAllowed: {
            type: Array
        },
        changesSaved: {
            type: Boolean,
            default: false
        },
        errorWhileSave:{
            type : Boolean,
        }
    },
    async mounted() {
        await this.getPermissions();
        this.setSelectedPermissions();
    },
    methods: {
        togglePermission(id) {
            if (this.selectedPermissionsId.includes(id)) {
                this.selectedPermissionsId = this.selectedPermissionsId.filter(
                    (permId) => permId !== id
                );
            } else {
                this.selectedPermissionsId.push(id);
            }
        },
        toggleSection() {
            this.isUserDetailExpanded = !this.isUserDetailExpanded;
            this.isPermissionsExpanded = !this.isPermissionsExpanded;
        },
        // Get the permissions
        async getPermissions() {
            try {
                const response = await axios.get('api/permissions');

                this.permissions = response.data.permissions;
            } catch (error) {
                console.log(error);
            } finally {
                this.loading = false;
            }
        },

        setSelectedPermissions() {
            this.selectedPermissionsId = this.permissions
                .filter(permission => this.permissionsAllowed.includes(permission.name))
                .map(permission => permission.id);
        },

        saveChanges() {
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            // Verify email address
            if (this.selectDropDown && !emailPattern.test(this.userEmail)) {
                
                this.errorMessage = 'Invalid email format';
                this.conditionError = true;
                setTimeout(() => {
                this.conditionError = false;
                }, 3000)
                return;
            }

            if (this.selectedPermissionsId.length !== 0) {
                if (this.selectDropDown) {
                    this.verifyUser();
                } else {
                    this.editPermissions();
                }
            } else {
                this.errorMessage = 'Please select at least one permission';
                this.conditionError = true;
                setTimeout(() => {
                this.conditionError = false;
                }, 3000)
            }
        },

        // Verify the user exist in past or not
        async verifyUser() {
            
            const body = {
                "user_id": this.userData.user_id,
                "email": this.userEmail,
                "name": this.userEmail,
                "permission_ids": this.selectedPermissionsId,
                "approved": 1
            }
            this.$emit('saveUserDetails', 'post', body)
            
        },

        editPermissions() {
            const body = {
                "user_id": this.userData.user_id,
                "permission_ids": this.selectedPermissionsId,
            }
            this.$emit('saveUserDetails', 'put', body, this.selectedUserEmail)
        }
    },
    watch: {
        errorWhileSave(newVal) {
            if (newVal) {
                this.errorMessage = 'User already exists please try another email !';
            }   
        }
    }
}
</script>

<style scoped>
/* .expand-enter-active,
.expand-leave-active {
    transition: all 0.3s ease;
}

.expand-enter,
.expand-leave-to {
    max-height: 0;
    opacity: 0;
}*/
</style>