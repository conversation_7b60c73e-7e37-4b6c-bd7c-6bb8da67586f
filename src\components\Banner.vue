<template>
  <div class="banner-container">
    <div v-html="content.html"></div>
    <!-- <span @click.prevent="closeBanner(content.time)">&times;</span> -->
    <span @click.prevent="closeBanner()">&times;</span>
  </div>
</template>

<script>
import { mapMutations } from 'vuex'
export default {
  name: 'Banner',
  props: ['content'],

  data() {
    return {
      localTime: localStorage.getItem('banner_time')
    }
  },
  methods: {
    ...mapMutations(['setBanner', 'setBannerContent']),
    closeBanner() {
      localStorage.setItem('banner_time', this.content.time)
      this.setBanner(false)
      this.setBannerContent(null)
    }
  }
}
</script>

<style lang="scss" scoped>
.banner-container {
  font-size: 3rem;
  font-weight: 500;
  background: #ceefe4;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: .5rem;
  width: 100%;
  text-align: center;
  border-radius: 2px;
  margin: 1rem 6rem 1rem 0rem;

  span {
    line-height: .5;
    font-weight: 300;
    color: #261f43;
    border-radius: 8px;
    cursor: pointer;
    padding: 2px 5px;
    font-size: 6rem;
    transition: 0.3s;
  }
}

::v-deep p {
  max-width: 100% !important;
  width: 100%;
  padding: 2px 7px !important;
  border: 1px solid;
  border-radius: 8px;
}

@media only screen and (min-width: 1050px) {
  .banner-container span {
    padding: 1px 10px;
  }
}

@media only screen and (min-width: 1910px) {
  .banner-container span {
    padding: 7px 10px;
  }
}
</style>
