<template>
    <div class="campaign-performance" @click="closeDropdown">
        <Loader v-if="loaderState" />
        <div v-else>
            <div class="campaign-performance-header">
                <p>{{ currentReportName }}-wise Performance</p>
                <img @click.stop="toggleReportDropdown" 
                    class="dropdown" :src="dropDownIcon" alt="Dropdown" />
                <!-- Report Type Dropdown (Same as Report Component) -->
                <div v-show="showReportDropDown" class="report-dropdown">
                    <button v-for="report in availableReports" :key="report" @click="switchReport(report)">
                        <span>{{ report }} Report</span>
                    </button>

                </div>
            </div>
            <!-- Conditional Rendering Based on reportName -->
            <div v-if="reportName === 'Campaign' || reportName === 'Workflow'">
                <ReportTable v-if="currentReportName" :tableData="currentTableData" :labels="currentLabels" :currentReportName="currentReportName" />
            </div>
            <!-- Add Pagination Component for Template Data -->
            <div v-if="currentReportName === 'Template' && (campaignTableData.length > 1 || totalTemplateCampaigns > 1)"
                class="pagination-div">
                    <button @click="goToPage(currentTemplatePage - 1)" class="arrow-btn"
                        :disabled="currentTemplatePage === 1">
                    <img :src="leftArrow" alt="left arrow">
                    </button>
                    <button v-for="pageNumber in totalTemplatePages" :key="pageNumber" @click="goToPage(pageNumber)"
                    :class='{ "page-number": true, "select-page": currentTemplatePage === pageNumber }'
                    :disabled="currentTemplatePage === pageNumber">
                        {{ pageNumber }}
                    </button>
                    <button @click="goToPage(currentTemplatePage + 1)" class="arrow-btn"
                        :disabled="currentTemplatePage === totalTemplatePages">
                    <img :src="rightArrow" class="right-arrow" alt="right arrow">
                    </button>
                </div>
        </div>
    </div>
</template>
<script>
import DropDownIcon from '@/assets/icons/dropdown_icon.svg';
import axios from '@/utils/api.js';
import ReportTable from '@/components/ReportTable.vue';
import Loader from '@/components/ReportLoader.vue';
import LeftArrow from '@/assets/icons/pagination_arrow_left.svg';
import RightArrow from '@/assets/icons/pagination_arrow_right.svg';
export default {
    name: 'CampaignPerformance',
    components: { ReportTable, Loader },
    props: {
        campaignTableData: {
            type: Array,
            required: true
        },
        reportName: {
            type: String,
            required: true
        },
        workflowTableData: {
            type: Array,
            required: false
        },
        startDate: {
            type: String,
            required: false
        },
        endDate: {
            type: String,
            required: false
        }
    },
    data() {
        return {
            dropDownIcon: DropDownIcon,
            showReportDropDown: false,
            currentReportName: this.reportName,
            templateTableData: [],
            totalTemplateCampaigns: 0,
            currentTemplatePage: 1,
            itemsPerPage: 25,
            leftArrow: LeftArrow,
            rightArrow: RightArrow,
            campaignLabels: {
                title: this.reportName === "Workflow" ? "Workflow Statistics" : "Campaign Statistics",
                subtitle: this.reportName === "Workflow" ? "Workflow Influence" : "Campaign Influence",
                name: this.reportName === "Workflow" ? "Workflow Name" : "Campaign Name",
                contactlist: "Contact List Name",
                metrics: {
                    sent: "Message Sent",
                    delivered: "Delivered Rate",
                    read: "Read Rate"
                },
                conversions: {
                    opportunities: "Contacts who became Opportunity",
                    customers: "Contacts who became Customers",
                    deals: "Deals Created",
                    won: "Deals Won"
                }
            },
            templateLabels: {
                title: "Template Statistics",
                subtitle: "Template Influence",
                name: "Template Name",
                metrics: {
                    click_rate: "Click Rate",
                    reply_rate: "Reply Rate",
                    name: "Template Name",
                    clicked: "Clicked",
                    delivered: "Delivered",
                    sent: "Sent",
                    delivery_rate: "Delivery Rate",
                },
                conversions: {
                    opportunities: "Contacts who became Opportunity",
                    customers: "Contacts who became Customers",
                    deals: "Deals Created",
                    won: "Deals Won"
                }
            },
            workflowLabels: {
                title: "Workflow Statistics",
                subtitle: "Workflow Influence",
                name: "Workflow Name",
                metrics: {
                    sent: "Message Sent",
                    delivered: "Delivered Rate",
                    read: "Read Rate"
                },
                conversions: {
                    opportunities: "Contacts who became Opportunity",
                    customers: "Contacts who became Customers",
                    deals: "Deals Created",
                    won: "Deals Won"
                }
            },
            loaderState: false,
            portalId: null,
        };
    },
    computed: {
        currentTableData() {
            return this.currentReportName === "Campaign" || this.currentReportName === "Workflow" ? this.campaignTableData : this.paginatedTemplateData;
        },
        currentLabels() {
            return this.currentReportName === "Campaign" || this.currentReportName === "Workflow" ? this.campaignLabels : this.templateLabels;
        },
        availableReports() {
            const reports = this.reportName === "Workflow" ? ["Workflow", "Template"] : ["Campaign", "Template"];
            return reports.filter(report => report !== this.currentReportName);
        },

        paginatedTemplateData() {
            const start = (this.currentTemplatePage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return this.templateTableData.slice(start, end);
        },
        totalTemplatePages() {
            return Math.ceil(this.templateTableData.length / this.itemsPerPage);
        },
        isDropdownVisible() {
            return this.showReportDropDown;
        }
    },
    methods: {
        toggleReportDropdown() {
            this.showReportDropDown = !this.showReportDropDown;
        },
        closeDropdown(event) {
            if (!this.$el.contains(event.target)) {
                this.showReportDropDown = false;
            }
        },
        async switchReport(reportType) {
            this.currentReportName = reportType;
            this.showReportDropDown = false;
            this.$emit('update:currentReportName', reportType); // Emit event to update currentReportName
            if (reportType === 'Template' && this.templateTableData.length === 0) {
                await this.fetchTemplateData(1);
            }
        },
        async fetchTemplateData(pageNumber = 1) {
            this.loaderState = true;
            this.currentTemplatePage = pageNumber;
            try {
                const userId = this.$store.state.userData.user_id;
                const fromDate = this.startDate ? this.startDate : this.$route.query.from;
                const toDate = this.endDate ? this.endDate : this.$route.query.to;
                const apiUrl = `/api/whatsapp/template/analytics?user_id=${userId}&from=${fromDate}&to=${toDate}`;
                const response = await axios.get(apiUrl);
                if (response.data && Array.isArray(response.data.data)) {
                    this.templateTableData = response.data.data.map(item => ({
                        template_id: item.template_id,
                        name: item.template_name,
                        clicked: item.clicked,
                        delivered: item.delivered,
                        delivery_rate: item.delivery_rate,
                        sent: item.sent,
                        click_rate: item.click_rate,
                        reply_rate: item.reply_rate,
                    }));
                    this.totalTemplateCampaigns = response.data.total;
                    console.log("Mapped Template Data:", this.templateTableData);
                } else {
                    console.error("Unexpected API response format:", response.data);
                }
            } catch (error) {
                console.error("Error fetching template data:", error);
            } finally {
                this.loaderState = false;
            }
        },
        goToPage(pageNumber) {
            if (pageNumber < 1 || pageNumber > this.totalTemplatePages) return;
            this.currentTemplatePage = pageNumber;
        }
    },
    mounted() {
        var urlString = window.location.href
        var url = new URL(urlString)
        this.portalId = url.searchParams.get('portal_id')
    },
    watch: {
        currentReportName(newVal) {
            console.log('currentReportName changed to:', newVal);
        }
    }
};
</script>
