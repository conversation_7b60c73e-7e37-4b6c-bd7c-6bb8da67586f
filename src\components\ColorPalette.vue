<template>
  <div class="color-palette">
    <div
      v-for="color in colors"
      :key="color.id"
      class="color-item"
      :style="{ background: color.code }"
      @click.prevent="$emit('select-color', color.code)"
    >
      &nbsp;
    </div>
  </div>
</template>

<script>
export default {
  name: 'ColorPalette',

  emits: ['select-color'],

  methods: {
    getRandomColor() {
      const brightness = Math.trunc(Math.random() * 200)

      function randomChannel() {
        var r = 255 - brightness
        var n = 0 | (Math.random() * r + brightness)
        var s = n.toString(16)
        return s.length == 1 ? '0' + s : s
      }

      return '#' + randomChannel(brightness) + randomChannel(brightness) + randomChannel(brightness)
    }
  },

  computed: {
    colors() {
      return [...Array(81)].map((el, i) => {
        return { id: i + 1, code: this.getRandomColor(45) }
      })
    }
  }
}
</script>
