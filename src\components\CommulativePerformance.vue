<template>
    <div class="cumulative-performance">
        <p>Cumulative Performance</p>
        <div class="content">
            <div class="row-1">
                <div class="child-col-1">
                    <img :src="announcement" alt="">
                    <span>Number of {{ reportName }}</span>
                    <h3>{{ totalNumberOfCampaigns }}</h3>
                </div>
                <div class="child-col-2">
                    <div class="sub-child-1">
                        <div>
                            <span>Messages Sent</span>
                            <h3>{{ chartData?.sent }}</h3>
                        </div>
                        <div>
                            <span>Delivered Messages</span>
                            <h3>{{ chartData?.delivered }}</h3>
                        </div>
                        <div>
                            <span>Read Messages</span>
                            <h3>{{ chartData?.read }}</h3>
                        </div>
                        <!-- Advanced metrics - only for Professional+ plans -->
                        <div v-if="hasAdvancedReporting">
                            <span>Contacts who became Opportunity</span>
                            <h3>{{ chartData?.opportunities }}</h3>
                        </div>
                        <div v-if="hasAdvancedReporting">
                            <span>Contacts who became Customers</span>
                            <h3>{{ chartData?.customers }}</h3>
                        </div>

                        <!-- Upgrade prompt for Starter plan users -->
                        <div v-if="!hasAdvancedReporting" class="advanced-metrics-upgrade">
                            <div class="upgrade-card">
                                <img :src="lockIcon" alt="Locked" class="lock-icon" />
                                <span>Advanced conversion metrics available in Pro plan</span>
                                <button class="upgrade-btn-small" @click="handleUpgradeClick">
                                    Upgrade
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="sub-child-2">
                        <vue-funnel-graph :width="computedWidth" :height="computedHeight" :labels="labels"
                            :values="values" :colors="colors" :direction="direction"
                            :gradient-direction="gradientDirection" :animated="true"
                            :display-percentage="false"></vue-funnel-graph>
                    </div>
                </div>
            </div>
            <div class="row-2">
                <div class="child-col-1">
                    <div class="sub-child-1">
                        <span>Delivered Rate</span>
                        <h3>{{ initialRateDelivered }}%</h3>
                        <div :style="{ background: conicGradientDeliveredStyle }" class="pie-chart"></div>
                    </div>
                    <div class="sub-child-2">
                        <span>Read Rate</span>
                        <h3>{{ initialRateRead }}%</h3>
                        <div :style="{ background: conicGradientReadStyle }" class="pie-chart"></div>
                    </div>
                </div>
                <div class="child-col-2">
                    <span class="title">{{reportName}} Influence on Deals</span>
                    <div class="sub-child">
                        <div>
                            <span>Number of Deals Created</span>
                            <h1>{{ rateDealData?.dealsCreated }}</h1>
                        </div>
                        <div>
                            <span>Number of Deals Won</span>
                            <h1>{{ rateDealData?.dealsWon }}</h1>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row-3">
                <div class="child-col-1">
                    <div class="sub-child-1">
                        <p>Incoming Messages: Work vs. Non-Work Hours</p>
                        <div class="incoming-msg-div">
                            <div :style="{ background: workingHoursMsgsRateStyle }" class="pie-chart"></div>
                            <div>
                                <div class="work-hrs-percentage">
                                    <span class="percentage">{{ this.incomingMessagesData?.working_hours_percentage
                                        }}%</span>
                                    <span>Work Hours Messages</span>
                                </div>
                                <div class="work-hrs-msgs">
                                    <div class="work-hrs-msg">
                                        <div>
                                            <span class="color"></span>
                                            <span>{{ this.incomingMessagesData?.working_hours_count }}</span>
                                        </div>
                                        <span>Work Hour</span>
                                    </div>
                                    <div class="verticle-line"></div>
                                    <div class="non-work-hrs-msg">
                                        <div>
                                            <span class="color"></span>
                                            <span>{{ this.incomingMessagesData?.non_working_hours_count }}</span>
                                        </div>
                                        <span>Non - Work Hour</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="work-hour-time-title">
                            <p>*Work Hour : 9:00 AM to 6:00 PM</p>
                        </div>
                    </div>
                    <div class="sub-child-2">
                        <p>Incoming Messages per Hour</p>
                        <div>
                            <p>Number of Incoming Messages</p>
                            <div class="barchart-container">
                                <canvas width="calc(100% - 40px)" ref="barChartCanvas"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { VueFunnelGraph } from 'vue-funnel-graph-js';
import Announcement from '@/assets/icons/announcement_icon.svg';
import LockIcon from '@/assets/icons/lock_icon.svg';
import { Chart, BarController, BarElement, CategoryScale, LinearScale, Tooltip, Legend } from "chart.js";

Chart.register(BarController, BarElement, CategoryScale, LinearScale, Tooltip, Legend);
export default {
    name: 'CommulativePerformance',
    components: {
        VueFunnelGraph
    },
    props: [
        'chartData',
        'rateDealData',
        'totalNumberOfCampaigns',
        'incomingMessagesData',
        'reportName'
    ],
    data() {
        return {
            announcement: Announcement,
            lockIcon: LockIcon,
            initialRateDelivered: 0,
            initialRateRead: 0,
            deliveredRate: this.rateDealData?.deliveryRate,
            readRate: this.rateDealData?.readRate,
            rateIncrement: 1,
            intervalDuration: 10,
            labels: ['Message Sent', 'Delivered Messages', 'Read Messages', 'Contacts who became Opportunity', 'Contacts who became Customers'],
            values: [this.chartData?.sent, this.chartData?.delivered, this.chartData?.read, this.chartData?.opportunities, this.chartData?.customers],
            colors: ['#67CEFA', '#2C3D4F'],
            direction: 'horizontal',
            gradientDirection: 'horizontal',
            height: 150,
            width: 900,
            screenWidth: window.innerWidth,
            barChartInstance: null,
            barChartData: [],
            blueArrayData: [],
            orangeArrayData: [],
            greenArrayData: [],
            chartLabels: [],
        }
    },

    watch: {
        chartData(newValues) {
            this.values = [this.chartData?.sent, this.chartData?.delivered, this.chartData?.read, this.chartData?.opportunities, this.chartData?.customers]
        }
    },
    computed: {

        computedWidth() {
            if (this.screenWidth > 800 && this.screenWidth < 999) {
                return 570;
            } else if (this.screenWidth > 1000 && this.screenWidth < 1199) {
                return 750;
            } else if (this.screenWidth > 1200 && this.screenWidth < 1384) {
                return 900;
            } else if (this.screenWidth > 1385 && this.screenWidth < 1500) {
                return 1050;
            } else if (this.screenWidth > 1500 && this.screenWidth < 1808) {
                return 1200;
            } else if (this.screenWidth > 1809 && this.screenWidth < 2190) {
                return 1400;
            } else if (this.screenWidth > 2192) {
                return 1600;
            } else {
                return 900;
            }
        },

        computedHeight() {
            if (this.screenWidth > 800 && this.screenWidth < 835) {
                return 100;
            } else if (this.screenWidth > 835 && this.screenWidth < 999) {
                return 110;
            } else if (this.screenWidth > 1000 && this.screenWidth < 1199) {
                return 130;
            } else if (this.screenWidth > 1200 && this.screenWidth < 1384) {
                return 150;
            } else if (this.screenWidth > 1385 && this.screenWidth < 1808) {
                return 160;
            } else if (this.screenWidth > 1925 && this.screenWidth < 2190) {
                return 250;
            } else if (this.screenWidth > 2192) {
                return 300;
            } else {
                return 170;
            }
        },

        // Delivered rate
        conicGradientDeliveredStyle() {
            const gradientValue = `#34B7F1 0 ${this.initialRateDelivered === 100 ? this.initialRateDelivered + 1 : this.initialRateDelivered}%, #202224CC  0 100%`;

            return `conic-gradient(${gradientValue})`;
        },

        // Read rate
        conicGradientReadStyle() {
            const gradientValue = `#34B7F1 0 ${this.initialRateRead === 100 ? this.initialRateRead + 1 : this.initialRateRead}%, #202224CC 0 100%`;

            return `conic-gradient(${gradientValue})`;
        },

        // Incoming Messages Rate
        workingHoursMsgsRateStyle() {
            const percentage = this.incomingMessagesData?.working_hours_percentage || 0;
            const gradientValue = `#34B7F1 0 ${percentage === 100 ? percentage + 1 : percentage}%, #202224CC 0 100%`;
            return `conic-gradient(${gradientValue})`;
        },

        // Plan-based access control
        hasAdvancedReporting() {
            return this.$store.getters.hasAdvancedReporting;
        },
    },
    methods: {
        separaBarChartData() {
            this.incomingMessagesData?.messages.forEach(element => {
                this.chartLabels.push(element.time_range);
                element?.splited_time.forEach((item, index) => {
                    switch (index) {
                        case 0:
                            this.blueArrayData.push(item?.count);
                            break;
                        case 1:
                            this.orangeArrayData.push(item?.count);
                            break;
                        default:
                            this.greenArrayData.push(item?.count);
                            break;
                    }
                });
            });
        },

        renderChart() {
            this.separaBarChartData();
            // Create new chart instance
            this.barChartInstance = new Chart(this.$refs.barChartCanvas, {
                type: "bar",
                data: {
                    labels: this.chartLabels,
                    datasets: [
                        {
                            label: "",
                            backgroundColor: "#0353a4",
                            data: this.blueArrayData,
                        },
                        {
                            label: "",
                            backgroundColor: "#ff8552",
                            data: this.orangeArrayData,
                        },
                        {
                            label: "",
                            backgroundColor: "#4ecdc4",
                            data: this.greenArrayData,
                        },
                    ],
                },
                options: {
                    plugins: {
                        legend: {
                            display: false // Hide legend (colored dots on top)
                        },
                        tooltip: {
                            enabled: true,
                            callbacks: {
                                label: function (tooltipItem) {
                                    let datasetLabel = tooltipItem.dataset.label || "";
                                    let value = tooltipItem.raw;
                                    return `${datasetLabel}: ${value} msgs`;
                                },
                                title: function (tooltipItems) {
                                    if (!tooltipItems.length) return "";

                                    let index = tooltipItems[0].dataIndex;

                                    let subTimeRanges = this.incomingMessagesData?.messages[index]?.splited_time
                                        ?.map(item => `${item.time_range}: ${item.count} msgs`)
                                        .join("\n") || "";

                                    return `${subTimeRanges}`;
                                }.bind(this)
                            },
                        }
                    },
                    scales: {
                        y: { stacked: true }, // 
                        x: { stacked: true },
                    },
                }
            });
        },

        handleUpgradeClick() {
            const upgradeUrl = process.env.VUE_APP_UPGRADE_URL || '#';
            window.open(upgradeUrl, '_blank');
        },
    },
    mounted() {

        this.renderChart();

        // Piechart animation
        const incrementRate = () => {
            if (this.initialRateDelivered <= this.deliveredRate || this.initialRateRead <= this.readRate) {
                if (this.initialRateDelivered < this.deliveredRate) {
                    this.initialRateDelivered += this.rateIncrement;
                }
                if (this.initialRateRead < this.readRate) {
                    this.initialRateRead += this.rateIncrement;
                }
                setTimeout(incrementRate, this.intervalDuration);
            }
        };
        // Start the incrementation after a short delay
        setTimeout(incrementRate, 1000);

    }
}
</script>

<style scoped>
.advanced-metrics-upgrade {
    grid-column: span 2;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 1rem;
}

.upgrade-card {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 1rem 1.5rem;
    color: #6c757d;
    font-size: 0.9rem;
}

.lock-icon {
    width: 20px;
    height: 20px;
    opacity: 0.6;
}

.upgrade-btn-small {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    color: white;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upgrade-btn-small:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}
</style>