<template>
  <div class="conflict-container">
    WhatsApp is open on another computer or browser. Click “Logout” and reload HubSpot to authorize again.
    <button class="btn btn-sm btn-primary" @click.stop="logout">LOG OUT</button>
  </div>
</template>

<script>
export default {
  name: 'Conflict',

  methods: {
    async logout() {
      const userData = this.$store.state.userData
      try {
        await this.$store.dispatch('logout', userData.user_id)
      } catch (err) {
        console.log(err)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.conflict-container {
  font-size: 14px;
  background: #ffbbbb;
  padding: 7px 10px;
  min-width: 40%;
  text-align: center;
  position: relative;
}
</style>
