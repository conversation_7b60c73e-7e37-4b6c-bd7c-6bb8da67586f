<template>
  <div class="create-label_wrapper" :class="{ block_ui: addingLabel }">
    <!-- Error -->
    <error-component v-show="error" />

    <!-- create label -->
    <div class="create-label">
      <div class="form-group">
        <label for="name">Label name</label>
        <input required v-model="name" type="input" class="form-control" id="name" placeholder="Enter label name" />
      </div>
      <div class="form-group">
        <label for="color">Label color</label>
        <div class="color-picker form-control">
          <span class="color-name">{{ colorLabel }}</span>
          <span v-if="color" class="color-block" :style="{ background: color.hex }"></span>
          <img v-else :src="gradientIcon" alt="Gradient Icon" />
        </div>
      </div>

      <!-- Color Palette -->
      <!-- <color-palette @select-color="selectColor" /> -->
      <div class="swatches-container">
        <Swatches v-model="color"></Swatches>
      </div>

      <!-- Submit Button -->
      <div class="button-wrapper">
        <button :disabled="disabled" @click.prevent="submitHandler" class="btn btn-primary">Submit</button>
      </div>
    </div>
  </div>
</template>

<script>
// import { ChromePicker } from "vue-color";
import { Swatches } from 'vue-color'
// import ColorPalette from "@/components/ColorPalette";
import GradientIcon from '@/assets/icons/gradient_icon.png'
import ErrorComponent from '@/components/Error'

export default {
  name: 'CreateLabel',

  components: {
    ErrorComponent,
    Swatches
  },

  emits: ['create-handler'],

  props: ['addingLabel', 'error'],

  data() {
    return {
      name: '',
      color: '',
      gradientIcon: GradientIcon
    }
  },

  methods: {
    // selectColor(color) {
    //   this.color = color;
    // },

    submitHandler() {
      const data = {
        name: this.name,
        color: this.color.hex
      }
      this.$emit('create-handler', data)
    }
  },

  computed: {
    colorLabel() {
      return this.color ? this.color.hex : 'Select label color'
    },

    disabled() {
      return !this.name || !this.color
    }
  }
}
</script>
