<template>
  <div class="create-list_wrapper" :class="{ block_ui: addingList }">
    <!-- Error -->
    <div class="campaign-error-container">
      <error-component v-show="error || this.show" :errorMessage="errorMsg" />
      <success-component
        v-show="successMsg"
        :successMessage="successMsg"
        @close="closeToast"
      />
    </div>

    <div class="progress-container">
      <div class="step step-space-inline">
        <div>
          <div v-if="progressStep !== 1" class="circle completed">&#10003;</div>
          <div v-if="progressStep === 1" class="circle active">1</div>
        </div>
        <div class="line" :class="progressStep > 1 ? 'active' : ''"></div>
        <div>
          <div v-if="progressStep > 2" class="circle completed">&#10003;</div>
          <div
            v-if="progressStep <= 2"
            class="circle"
            :class="progressStep > 1 ? 'active' : ''"
          >
            2
          </div>
        </div>
        <div class="line" :class="progressStep > 2 ? 'active' : ''"></div>
        <div class="step">
          <div class="circle">3</div>
        </div>
      </div>

      <div class="step step-title-spce">
        <span class="label">Lists</span>
        <span class="label">Templates</span>
        <span class="label">Enroll</span>
      </div>
    </div>

    <!-- create label -->
    <div class="create-list">
      <!-- Example single danger button -->
      <form @submit="submitHandler" id="myForm">
        <div v-show="!isShowCreateListContent" class="form-container">
          <!-- HubSpolt List -->
          <div v-if="progressStep == 1" class="form-group">
            <!-- Campaign Name -->
            <div class="camp-title-div form-group">
              <label for="hubspot-list" class="form-label">Campaign Name</label>
              <input
                type="text"
                placeholder="Enter Campaign Name"
                name="campaignName"
                class="camp-form-input"
                v-model="campaignName"
              />
            </div>

            <div class="hs-list-title-div">
              <label for="hubspot-list" class="form-label">HubSpot List</label>
              <div class="tooltip-container">
                <img class="info-icon" :src="infoIcon" alt="info" />
                <div class="tooltip-text">
                  <span>
                    All your HubSpot lists and newly created lists from
                    campaigns appear here</span
                  >
                  <img :src="toolTipArrow" alt="dropdown" />
                </div>
              </div>
            </div>
            <v-select
              id="hubspot-list"
              v-model="selectedList"
              :options="filteredHubspotLists"
              label="name"
              @input="listChanged"
              @search="debouncedHandleKeypress"
              @blur="handleBlur"
              :reduce="(list) => list.listId"
              :searchable="true"
              :clearable="false"
              placeholder="Please select a list"
              required
            >
              <template #no-options>
                <p v-if="loading">Loading...</p>
                <p v-else>Sorry, no matching options.</p>
              </template>
            </v-select>
          </div>

          <!-- WhatsApp Templates -->
          <div v-if="progressStep == 2" class="form-group hs-template-div">
            <img @click="goBack" :src="backArrow" alt="back arrow" />
            <label for="facebook-template" class="form-label"
              >WhatsApp Template</label
            >
            <v-select
              id="facebook-template"
              v-model="selectedTemplate"
              :options="templates"
              label="name"
              @input="templateChanged"
              @search="debouncedHandleKeypressWhatsApp"
              @blur="handleBlur"
              :reduce="(template) => template.id"
              :searchable="true"
              :clearable="false"
              placeholder="Search or select a template"
              required
            >
              <template #no-options>
                <p v-if="loading">Loading...</p>
                <p v-else>Sorry, no matching options.</p>
              </template>
            </v-select>

            <div v-if="selectedTemplate" class="template-body">
              {{ selectedTemplate.body }}
            </div>

            <div v-if="selectedTemplate">
              <template v-if="selectedTemplate.hasHeaderParam">
                <div class="form-group" :key="selectedTemplate.id">
                  <div
                    class="d-flex justify-content-between align-items-center"
                  >
                    <label class="form-label">Header Param Text</label>
                    <div class="dropdown">
                      <button
                        class="btn tokenButton dropdown-toggle"
                        type="button"
                        data-bs-toggle="dropdown"
                        aria-expanded="false"
                      >
                        Contact Tokens
                      </button>
                      <ul class="dropdown-menu ++">
                        <li>
                          <div class="px-3">
                            <input
                              @keyup="searchProperties($event)"
                              type="text"
                              class="form-control search-input"
                              placeholder="Search property name"
                            />
                          </div>
                        </li>
                        <li
                          v-for="property in properties"
                          :key="property.name"
                          :data-name="property.label"
                          class="tokenItem"
                        >
                          <button
                            @click="addToken(property.name, 'header_text_1')"
                            :title="property.label"
                            class="dropdown-item"
                            type="button"
                          >
                            {{ property.label }}
                          </button>
                        </li>
                      </ul>
                    </div>
                  </div>

                  <input
                    name="header_text_1"
                    v-model="templateFields['header_text_1']"
                    @keydown.enter.prevent
                    type="text"
                    class="form-control"
                    placeholder="Enter value"
                    required
                  />
                </div>
              </template>

              <!-- handle template params -->
              <template v-if="selectedTemplate.params > 0">
                <div
                  class="form-group"
                  v-for="index in selectedTemplate.params"
                  :key="selectedTemplate.id + index"
                >
                  <div
                    class="d-flex justify-content-between align-items-center"
                  >
                    <label class="form-label">Placeholder {{ index }}</label>

                    <div class="dropdown">
                      <button
                        class="btn tokenButton dropdown-toggle"
                        type="button"
                        data-bs-toggle="dropdown"
                        aria-expanded="false"
                      >
                        Contact Tokens
                      </button>
                      <ul class="dropdown-menu --">
                        <li>
                          <div class="px-3">
                            <input
                              @keyup="searchProperties($event)"
                              type="text"
                              class="form-control search-input"
                              placeholder="Search property name"
                            />
                          </div>
                        </li>
                        <li
                          v-for="property in properties"
                          :key="property.name"
                          :data-name="property.label"
                          class="tokenItem"
                        >
                          <button
                            @click="
                              addToken(property.name, 'placeholder_' + index)
                            "
                            class="dropdown-item"
                            type="button"
                          >
                            {{ property.label }}
                          </button>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <input
                    :name="'placeholder_' + index"
                    v-model="templateFields[`placeholder_${index}`]"
                    type="text"
                    class="form-control"
                    placeholder="Enter placeholder value"
                    required
                  />
                </div>
              </template>

              <!-- Dynamic data template -->
              <template v-if="matchedDependency">
                <template
                  v-for="(
                    field, index
                  ) in matchedDependency.dependentFieldNames"
                >
                  <div class="form-group" :key="index">
                    <div
                      class="d-flex justify-content-between align-items-center"
                    >
                      <label class="form-label">{{
                        Object.values(field)[0]
                      }}</label>
                      <div class="dropdown">
                        <button
                          class="btn tokenButton dropdown-toggle"
                          type="button"
                          data-bs-toggle="dropdown"
                          aria-expanded="false"
                        >
                          Contact Tokens
                        </button>
                        <ul class="dropdown-menu ==">
                          <li>
                            <div class="px-3">
                              <input
                                @keyup="searchProperties($event)"
                                type="text"
                                class="form-control search-input"
                                placeholder="Search property name"
                              />
                            </div>
                          </li>
                          <li
                            v-for="property in properties"
                            :key="property.name"
                            :data-name="property.label"
                            class="tokenItem"
                          >
                            <button
                              @click="
                                addToken(property.name, Object.keys(field)[0])
                              "
                              :title="property.label"
                              class="dropdown-item"
                              type="button"
                            >
                              {{ property.label }}
                            </button>
                          </li>
                        </ul>
                      </div>
                    </div>

                    <input
                      :name="Object.keys(field)[0]"
                      v-model="templateFields[Object.keys(field)[0]]"
                      @keydown.enter.prevent
                      type="text"
                      class="form-control"
                      placeholder="Enter value"
                      required
                    />
                  </div>
                </template>
              </template>

              <!-- handle media type -->
              <!-- handle button -->
              <!-- handle media button -->
            </div>
          </div>

          <!-- Enrolled Content -->
          <div class="enrolled-content" v-if="progressStep == 3">
            <img @click="goBack" :src="backArrow" alt="back arrow" />
            <div class="mt-5">
              <p class="title">HubSpot List Name:</p>
              <p>{{ this.selectedList?.name || this.listPayload?.name }}</p>
            </div>
            <div class="mt-5">
              <p class="title">Campaign Name:</p>
              <p>{{ this.campaignName }}</p>
            </div>
            <div class="mt-5">
              <p class="title">WhatsApp Template Name:</p>
              <p>{{ this.selectedTemplate.name }}</p>
            </div>
          </div>

          <div
            @click="handleCreateNewList"
            v-if="this.progressStep == 1"
            class="add-more-list"
          >
            <span class="icon">&#43;</span>
            <span class="title">Create New List from Previous Campaigns</span>
          </div>
          <Spinner v-if="this.loader" />
        </div>

        <!-- Create new list -->
        <div v-show="isShowCreateListContent" class="create-list-container">
          <img
            @click="handleCreateNewList"
            class="mb-4"
            :src="backArrow"
            alt="lefy arrow"
          />
          <div class="mb-3">
            <label for="hubspot-list" class="title">Select Campaigns</label>
            <v-select
              id="hubspot-list"
              v-model="selectedList"
              :options="filteredCampaignLists"
              label="campaign_name"
              @input="listCampaignChanged"
              @search="debouncedHandleKeypressCampaignList"
              @blur="handleBlur"
              :reduce="(list) => list.id"
              :searchable="true"
              :clearable="false"
              placeholder="Please select a list"
              required
            >
              <template #no-options>
                <p v-if="loading">Loading...</p>
                <p v-else>Sorry, no matching options.</p>
              </template>
            </v-select>
            <span
              v-if="selectedList && selectedList.name"
              class="fs-3 d-block text-decoration-underline mt-0"
            >
              {{ selectedList.name }} hubspot list
            </span>
          </div>

          <div class="form-group mb-3">
            <div class="hs-list-title-div">
              <label for="hubspot-list" class="form-label">Select Status</label>
              <div class="tooltip-container">
                <img class="info-icon" :src="infoIcon" alt="info" />
                <div class="tooltip-text">
                  <span>
                    All your HubSpot lists and newly created lists from
                    campaigns appear here</span
                  >
                  <img :src="toolTipArrow" alt="dropdown" />
                </div>
              </div>
            </div>
            <v-select
              id="hubspot-list"
              v-model="selectedStatus"
              :options="staticStatuses"
              label="name"
              @blur="handleBlur"
              :reduce="(list) => list.listId"
              :searchable="true"
              :clearable="false"
              placeholder="Please select a list"
              :disabled="!selectedCampaign"
              @input="listStatusChanged"
              required
            >
              <template #no-options>
                <p v-if="loading">Loading...</p>
                <p v-else>Sorry, no matching options.</p>
              </template>
            </v-select>
            <span
              v-if="this.selectedStatus"
              :class="statusCountMessage != 0 ? 'text-black' : 'restrict-color'"
              class="fs-3 d-block text-decoration-underline mt-0"
              >{{ this.statusCountMessage }} contacts enrolled into the
              list</span
            >
          </div>

          <div class="list-name-div mt-4">
            <label for="list-name">List Name</label>
            <input
              type="text"
              placeholder="Give a name to your list"
              name=""
              id="list-name"
              v-model="listName"
              @input="debouncedHandleKeypress($event.target.value, true)"
            />
            <span
              v-if="isMatch"
              class="text-danger fs-3 text-decoration-underline"
              >There is already a list with the name '{{ listName }}'</span
            >
          </div>
          <div class="list-name-div mt-4 mb-4">
            <label for="list-name">Campaign Name</label>
            <input
              type="text"
              placeholder="Give a campaign name"
              name=""
              id="list-name"
              v-model="campaignName"
            />
            <!-- <span v-if="isMatch" class="text-danger fs-3 text-decoration-underline">There is already a list with the
              name '{{
                campaignName }}'</span> -->
          </div>
        </div>

        <!-- Manage Buttons -->
        <div class="schedule-campaign-conatiner">
          <button
            type="submit"
            v-if="!isShowCreateListContent && progressStep < 3"
            class="btn btn-primary"
            :disabled="
              progressStep === 1
                ? !selectedList || !campaignName
                : !selectedTemplate
            "
          >
            Next
          </button>

          <div
            v-if="progressStep >= 3"
            class="schedule-campaign-secondary d-flex"
          >
            <button type="submit" class="btn btn-primary rounded-end">
              Enroll Now
            </button>
            <div @click="showScheduleBtn" class="btn btn-primary rounded-start">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M7 10L12 15L17 10H7Z" />
              </svg>
            </div>
            <button
              id="schedule-dropdown"
              @click="showModal"
              v-if="this.isScheduleSendBtnActive"
              class="btn schedule-btn"
            >
              Schedule send
            </button>
          </div>

          <div
            v-click-outside="closeSavebtn"
            v-if="isShowCreateListContent"
            class="schedule-campaign-secondary d-flex"
          >
            <button
              class="btn btn-primary rounded-end"
              type="button"
              :disabled="
                this.selectedStatus == null ||
                this.selectedCampaign == null ||
                !this.listName ||
                this.statusCountMessage == 0 ||
                !this.campaignName
              "
              @click="CreateNewList"
            >
              Save & Proceed
            </button>
            <div @click="showScheduleBtn" class="btn btn-primary rounded-start">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M7 10L12 15L17 10H7Z" />
              </svg>
            </div>
            <button
              @click="() => CreateNewList(1)"
              id="schedule-dropdown"
              type="button"
              v-if="this.isScheduleSendBtnActive"
              :disabled="
                this.selectedStatus == null ||
                this.selectedCampaign == null ||
                !this.listName ||
                this.statusCountMessage == 0
              "
              class="btn schedule-btn"
            >
              Save List
            </button>
          </div>
        </div>
      </form>
      <schedule-campaign-modal
        @schedule-campaign="scheduleCampaign"
        :showCampaignScheduleModal="isModalOpen"
        @close-modal="showModal"
        ref="childComponent"
      />
      <!-- ends here -->
    </div>
  </div>
</template>

<script>
import 'bootstrap';
import axios from '@/utils/api.js';
import vClickOutside from 'v-click-outside';
import vSelect from 'vue-select';
import 'vue-select/dist/vue-select.css';
import templateData from '../responses/templates.json';
import GradientIcon from '@/assets/icons/gradient_icon.png';
import Dropdown from '@/assets/icons/dropdown_icon.svg';
import InfoIcon from '@/assets/icons/info_icon.png';
import Info from '@/assets/icons/info.svg';
import LeftArrowIcon from '@/assets/icons/left_arrow.svg';
import TooltipIcon from '@/assets/icons/tooltipArrow.png';
import ErrorComponent from '@/components/Error';
import SuccessComponent from '@/components/Success';
import 'vue-select/dist/vue-select.css';
import ScheduleCampaignModal from '../vuechat/components/ScheduleCampaignModal/ScheduleCampaignModal.vue';
import { STATIC_STATUSES } from '../vuechat/utils/constants';
import Spinner from '@/components/Spinner';
import moment from 'moment';

export default {
  name: 'CreateList',

  components: {
    ScheduleCampaignModal,
    ErrorComponent,
    SuccessComponent,
    vSelect,
    Spinner,
  },

  directives: {
    clickOutside: vClickOutside.directive,
  },

  emits: ['create-handler'],

  props: [
    'addingList',
    'templates',
    'hubspotLists',
    'campaignLists',
    'properties',
    'error',
    'loader',
  ],

  data() {
    return {
      selectedList: null,
      selectedTemplate: null,
      templateFields: {},
      campaignName: '',
      activeTokenModal: null,
      gradientIcon: GradientIcon,
      dropdown: Dropdown,
      toolTipArrow: TooltipIcon,
      infoIcon: InfoIcon,
      info: Info,
      backArrow: LeftArrowIcon,
      isModalOpen: false,
      isScheduleSendBtnActive: false,
      scheduledTime: null,
      scheduledTimeZone: null,
      errorMsg: '',
      successMsg: '',
      show: false,
      filteredHubspotLists: [...this.hubspotLists],
      originalHubspotLists: [...this.hubspotLists],
      previousValues: [...this.hubspotLists],

      filteredCampaignLists: [...this.campaignLists],
      originalCampaignLists: [...this.campaignLists],
      previousCampaignListValues: [...this.campaignLists],

      loading: false,
      isShowCreateListContent: false,
      progressStep: 1,
      staticStatuses: STATIC_STATUSES,
      selectedCampaign: null,
      selectedStatus: null,
      listName: '',
      statusCountMessage: '',
      selectedListType: '',
      listPayload: null,
      isMatch: false,
      scheduledTimeZoneOffSet: null,
    };
  },
  created() {
    const userData = this.$store.state.userData;
    this.user_id = userData.user_id;
    this.debouncedHandleKeypress = this.debounce(this.handleKeypress, 300);
    this.debouncedHandleKeypressWhatsApp = this.debounce(
      this.handleKeypressWhatsApp,
      300,
    );
    this.debouncedHandleKeypressCampaignList = this.debounce(
      this.handleKeypressCampaignList,
      300,
    );
  },
  filters: {
    capitalize: function (value) {
      if (!value) return '';
      return value.charAt(0).toUpperCase() + value.slice(1);
    },
  },

  watch: {
    hubspotLists: {
      handler(newVal) {
        this.filteredHubspotLists = [...newVal];
        const preselectedList = this.filteredHubspotLists.find(
          (list) => list.listId === this.listPayload?.id,
        );
        this.selectedList = preselectedList;
      },
      deep: true,
      immediate: true,
    },

    loader: {
      handler(newVal) {
        return newVal;
      },
      deep: true,
      immediate: true,
    },
    selectedList: {
      handler(newVal) {
        console.log(newVal, 'selectedList---');
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    handleCreateNewList() {
      this.selectedCampaign = null;
      this.isMatch = false;
      this.isShowCreateListContent = !this.isShowCreateListContent;
      this.selectedList = null;
      this.selectedStatus = null;
      this.listName = null;
      this.statusCountMessage = '';
      this.campaignName = '';
    },

    goBack() {
      if (this.progressStep > 1) {
        this.progressStep -= 1;
      }
    },
    closeToast() {
      this.errorMsg = '';
    },
    debounce(func, wait) {
      let timeout;
      return function (...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
      };
    },
    async handleKeypressWhatsApp(searchText) {
      let searchValue = searchText?.toLowerCase();
      if (searchValue.length === 0) {
        return;
      }

      if (searchValue.length >= 3) {
        this.loading = true;
        try {
          const response = await axios.post(`api/whatsapp/template`, {
            user_id: this.user_id,
            filters: {
              name: searchValue,
              status: 'APPROVED',
              parse: true,
            },
          });
          let matchTemplates = response?.data?.templates?.data || [];
          if (matchTemplates.length > 0) {
            // Instead of directly modifying this.templates, emit an event
            this.$emit('update-templates', matchTemplates);
          }
        } catch (error) {
          console.error('Error fetching HubSpot lists:', error);
        } finally {
          this.loading = false;
        }
      }
    },

    async handleKeypress(searchText) {
      this.isMatch = false;
      let searchValue = searchText?.toLowerCase();

      if (searchValue.length === 0) {
        this.filteredHubspotLists = [...this.originalHubspotLists];
        return;
      }

      if (searchValue.length >= 3) {
        this.loading = true;
        try {
          const response = await axios.get(
            `api/hubspot/lists?user_id=${this.user_id}&query=${searchValue}`,
          );
          this.filteredHubspotLists = response.data.data;
        } catch (error) {
          console.error('Error fetching HubSpot lists:', error);
        } finally {
          this.loading = false;
        }
      } else {
        this.filteredHubspotLists = this.previousValues;
      }
    },

    // 🔹 Function to Compare create list Input & API Response
    checkMatch(searchValue) {
      return this.filteredHubspotLists.some((list) => {
        if (list.name.toLowerCase() === searchValue) {
          this.isMatch = true;
          return true; // Stop further execution
        }
        return false;
      });
    },

    async handleKeypressCampaignList(searchText) {
      let searchValue = searchText.toLowerCase();
      if (searchValue.length === 0) {
        this.filteredCampaignLists = [...this.originalCampaignLists];
        return;
      }
      if (searchValue.length >= 3) {
        this.loading = true;
        try {
          const response = await axios.get(
            `api/lists?user_id=${this.user_id}&query=${searchValue}`,
          );
          this.filteredCampaignLists = response.data.data;
        } catch (error) {
          console.error('Error fetching HubSpot lists:', error);
        } finally {
          this.loading = false;
        }
      } else {
        this.filteredCampaignLists = this.previousCampaignListValues;
      }
    },
    handleBlur() {
      this.filteredCampaignLists = this.previousCampaignListValues;
    },

    async CreateNewList(param = 0) {
      this.loading = true;

      if (this.checkMatch(this.listName)) {
        return true;
      }

      try {
        const response = await axios.post(`api/hubspot/createContactList`, {
          campaign: this.selectedCampaign,
          status: this.selectedStatus,
          listName: this.listName,
        });
        if (response?.data?.status === 'ok') {
          this.show = false;
          this.errorMsg = '';
          this.successMsg = 'List created successfully';
          this.selectedList = null;
          this.isScheduleSendBtnActive = false;
          if (param === 1) {
            this.isShowCreateListContent = false;
          } else {
            this.isMatch = false;
            this.isShowCreateListContent = false;
            this.progressStep = 2;
            this.listPayload = {
              id: response?.data?.message?.list?.listId,
              name: response?.data?.message?.list?.name,
              size: `${this.statusCountMessage}`,
              type: ['SNAPSHOT', 'MANUAL'].includes(
                response?.data?.message?.list?.processingType,
              )
                ? 'STATIC'
                : 'DYNAMIC',
              lastSizeChangeAt: 0,
              enabled: true,
            };
          }
          setTimeout(() => {
            this.$emit('fetch-initial-list', true);
          }, 500);
          setTimeout(() => {
            this.successMsg = '';
          }, 3000);
        } else {
          this.errorMsg = response?.data?.message;
          this.show = true;
        }
      } catch (error) {
        console.error('Error fetching HubSpot lists:', error);
        // Handle the error (e.g., display an error message)
      } finally {
        this.loading = false;
      }
    },

    showScheduleBtn() {
      this.isScheduleSendBtnActive = !this.isScheduleSendBtnActive;
    },

    closeSavebtn() {
      this.isScheduleSendBtnActive = false;
    },

    showModal(event) {
      let form = document.getElementById('myForm');

      if (!form.checkValidity()) {
        event.preventDefault(); // Prevent action if form is invalid
        this.errorMsg = 'Please fill all the required fields.'; // Assign the error message directly
        this.show = true;
      } else {
        this.show = false;
        this.isModalOpen = !this.isModalOpen;
        this.isModalOpen && this.$refs.childComponent.resetDateTime();
        this.isScheduleSendBtnActive = false;
      }
    },

    tokenModalHandler(selectedData) {
      if (this.activeTokenModal === selectedData) {
        this.activeTokenModal = null;
      } else {
        this.activeTokenModal = selectedData;
      }
    },
    searchProperties(event) {
      let searchValue = event.target.value;
      let tokenItems =
        event.target.parentElement.parentElement.parentElement.querySelectorAll(
          '.tokenItem',
        );

      if (tokenItems) {
        for (let i = 0; i < tokenItems.length; i++) {
          let token = tokenItems[i];
          let tokenName = token.getAttribute('data-name').trim();
          if (tokenName.toLowerCase().includes(searchValue.toLowerCase())) {
            token.style.display = 'block';
          } else {
            token.style.display = 'none';
          }
        }
      }
    },
    addToken(token, target) {
      let searchInput = document.querySelector('.search-input');
      let targetElement = document.querySelector(`input[name='${target}']`);
      targetElement.value = '';
      targetElement.value += `[${token}]`;
      this.templateFields[target] = `[${token}]`;
      targetElement.style.color = 'blue';
      // jQuery(`input[name='${target}']`).highlightWithinTextarea({
      //   highlight: targetElement.value, // string, regexp, array, function, or custom object
      // });
      // targetElement.dispatchEvent(new Event("change"));
      searchInput.value = '';
    },
    templateChanged(selectedTemplatedId) {
      const selectedTemplate = this.templates.find(
        (template) => template.id == selectedTemplatedId,
      );
      this.selectedTemplate = selectedTemplate;
    },

    listChanged(selectedListId) {
      const selectedList = this.filteredHubspotLists.find(
        (listItem) => listItem.listId == selectedListId,
      );
      this.selectedList = selectedList;
    },

    listCampaignChanged(selectedListId) {
      this.selectedCampaign = selectedListId;
      this.selectedStatus = null;
      this.statusCountMessage = '';
      this.selectedList =
        this.filteredCampaignLists.find((list) => list.id === selectedListId) ||
        null;
    },

    listStatusChanged(selectedStatus) {
      if (this.selectedCampaign) {
        const campaign = this.campaignLists.find(
          (campaign) => campaign.id === this.selectedCampaign,
        );

        if (campaign) {
          this.selectedStatus = selectedStatus;
          const statusCounts = {
            delivered: campaign.delivered || 0,
            read: campaign.viewed || 0,
            failed: campaign.failed || 0,
            sent: campaign.sent || 0,
          };

          // this.selectedListType = campaign.list_type;

          if (statusCounts[selectedStatus] > 0) {
            this.statusCountMessage = statusCounts[selectedStatus];
          } else {
            this.statusCountMessage = 0;
          }
        }
      }
    },

    // Schedule campaihn function
    scheduleCampaign(scheduledDate, scheduledTime, scheduleZone) {
      const [time, modifier] = scheduledTime.split(' ');
      let [hours, minutes] = time.split(':');
      let timeZoneOffset = moment.tz(scheduleZone);
      const offsetInMinutes = timeZoneOffset.utcOffset();
      const convertedOffset = -offsetInMinutes;

      // Convert hours based on AM/PM
      if (modifier === 'PM' && hours !== '12') {
        hours = parseInt(hours) + 12;
      }
      if (modifier === 'AM' && hours === '12') {
        hours = '00';
      }

      // Set the 24-hour format
      let time24Hour = `${hours}:${minutes}:00`;

      this.scheduledTime = scheduledDate + ' ' + time24Hour;
      this.scheduledTimeZone = scheduleZone;
      this.scheduledTimeZoneOffSet = convertedOffset;

      this.submitHandler();
    },

    submitHandler(event) {
      this.progressStep = this.progressStep + 1;

      event?.preventDefault();
      console.log(this.campaignName, 'campaignName');

      let data = {
        template: {
          id: this.selectedTemplate.id,
          name: this.selectedTemplate.name,
          mediaType: this.selectedTemplate.mediaType,
          fields: this.templateFields,
        },

        list: this.selectedList?.additionalProperties
          ? {
            id: this.selectedList?.listId,
            campaign_name: this.campaignName,
            name: this.selectedList?.name,
            size:
              this.statusCountMessage ||
              this.selectedList?.additionalProperties.hs_list_size,
            type: ['SNAPSHOT', 'MANUAL'].includes(
              this.selectedList?.processingType,
            )
              ? 'STATIC'
              : 'DYNAMIC',
            lastSizeChangeAt: Math.max(
              this.selectedList?.additionalProperties
                .hs_last_record_added_at || 0,
              this.selectedList.additionalProperties
                .hs_last_record_removed_at || 0,
            ),
            enabled: true,
            ...(this.scheduledTime !== null && {
              schedule_time: this.scheduledTime,
            }),
            ...(this.scheduledTimeZone !== null && {
              timezone: this.scheduledTimeZoneOffSet,
            }),
          }
          : {
            ...this.listPayload,
            campaign_name: this.campaignName,
            ...(this.scheduledTime !== null && {
              schedule_time: this.scheduledTime,
            }),
            ...(this.scheduledTimeZone !== null && {
              timezone: this.scheduledTimeZoneOffSet,
            }),
          },
      };

      if (this.progressStep === 4) {
        this.$emit('create-handler', data);
      }
    },
  },

  computed: {
    matchedDependency() {
      return templateData.find(
        (dep) => dep.controllingFieldValue === this.selectedTemplate?.type,
      );
    },

    colorLabel() {
      return this.color ? this.color.hex : 'Select label color';
    },

    disabled() {
      return !this.name || !this.color;
    },
  },
  mounted: function () {
    // setTimeout((e) => {
    //   const button = document.getElementById("button");
    //   console.log(button);
    //   // Pass the button, the tooltip, and some options, and Popper will do the
    //   // magic positioning for you:
    //   createPopper(button);
    // }, 1000);
  },
};
</script>
<style>
.vs__dropdown-toggle {
  width: 100% !important;
  border-radius: 8px !important;
}

#facebook-template .vs__search,
#hubspot-list .vs__search {
  width: 100% !important;
  padding: 8px !important;
  font-size: 16px !important;
  padding: 0 !important;
  margin: 0 !important;
}
</style>
