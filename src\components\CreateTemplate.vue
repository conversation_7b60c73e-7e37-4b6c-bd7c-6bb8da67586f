<template>
<div class="create-template_wrapper" :class="{ block_ui: updatingTemplate }">
<!-- Error -->
<div class="template-toast-container">
  <error-component v-show="errorMsg" :errorMessage="errorMsg" />
  <success-component v-show="successMsg" :successMessage="successMsg" />
</div>

<!-- create-template -->
<div class="template-header">
  <img @click="$emit('showTemplateList')" :src="arrowIcon" alt="arrow icon">
  <h2>New Template Message</h2>
</div>
<p class="description">Create personalized templates and messages that fit your needs.</p>
<div class="create-template">
  <div class="template-form-card">

    <form @submit="submitHandler">
      <!-- Template Category -->
      <div class="form-group">
        <label for="templateCategory">Template Category</label>
        <p class="group-title">Your template should fall under one of these categories</p>
        <select v-model="selectedCategory" id="templateCategory" name="templateCategory" class="form-control"
          required>
          <option value="" disabled>Select Category</option>
          <option v-for="(category, index) in templateCategory" :key="index" :value="category">
            {{ category }}
          </option>
        </select>
      </div>

      <!-- Template Language -->
      <div class="form-group">
        <label for="templateLanguage">Template Language</label>
        <p class="group-title">You will need to specify the language in which message template is submitted.</p>
        <select id="templateLanguage" v-model="selectedLanguage" name="templateLanguage" class="form-control"
          required>
          <option value="" disabled>Select Language</option>
          <option v-for="lang in languages" :key="lang.code" :value="lang.code">
            {{ lang.name }}
          </option>
        </select>
      </div>

      <!-- Template Name -->
      <div class="form-group">
        <label for="templateName">Template Name</label>
        <p class="group-title">Name can only be in lowercase alphanumeric characters and underscores. Special
          characters and white-space are not allowed.</p>
        <span class="example">Eg: product_cause_number</span>
        <input v-model="templateName" @input="validateTemplateName" id="templateName" name="templateName"
          type="text" placeholder="Enter your name here" class="form-control" required />
        <span v-if="templateNameError" class="name-error">{{ templateNameError }}</span>
      </div>

      <!-- Template Type -->
      <div v-if="this.selectedCategory !== 'AUTHENTICATION'" class="form-group">
        <label for="templateHeader">Template Type (Optional)</label>
        <p class="group-title">Choose your desired header type from the dropdown menu.</p>
        <div class="custom-dropdown">
          <div class="dropdown-header" :class="!selectedType ? 'placeholder-color' : ''" @click="toggleDropdown">
            {{ selectedType ? selectedType : 'Select Type' }}
          </div>
          <ul v-if="dropdownOpen" class="template-type-menu">
            <li v-for="(type, index) in templateType" :key="index" @click="selectType(type.value)"
              :class="{ 'selected-item': type.value === selectedType }" class="dropdown-item">
              <img v-if="type.title !== 'NONE'" :src="type.icon" :alt="type.icon" class="dropdown-icon" />
              {{ type.title }}
            </li>
          </ul>
        </div>
      </div>          <!-- Template Header Text/Attachment -->
      <div v-if="this.selectedCategory !== 'AUTHENTICATION' && this.selectedType && this.selectedType.toLowerCase() === 'text'" class="form-group">
        <label for="headerText">Template Header Text/Attachment (Optional)</label>
        <p class="group-title">Header text/attachment is to be updated here and is limited to 60 characters.</p>
        <input v-model="templateHeader" id="headerText" type="text"
          placeholder="Enter Text Here" name="header" class="form-control" maxlength="60" />
      </div>

      <!-- Template variable selection -->
      <div v-if="this.selectedCategory !== 'AUTHENTICATION'" class="form-group">
        <label for="templateCategory">Variable</label>
        <p class="group-title">You can insert either a name or number as a variable.</p>
        <span class="example" v-pre>E.g: Name: {{order_id}}, Number {{1}}</span>
        <select v-model="selectedVarType" id="templateCategory" name="templateCategory" class="form-control"
          required>
          <option value="" disabled>Select Variable</option>
          <option v-for="(type, index) in variableType" :key="index" :value="type">
            {{ type }}
          </option>
        </select>
      </div>

      <!-- Template Message Format -->
      <div v-if="this.selectedCategory !== 'AUTHENTICATION'" class="form-group">
        <label for="messageFormat">Template Message Format</label>
        <p class="group-title">Name variable can only contain lowercase alphanumeric characters and underscores (no special characters or white spaces). 
          Message content is limited to 1024 characters.</p>
          <span v-if="selectedVarType === 'Number'" class="example" v-text="numberEg"></span>
          <span v-if="selectedVarType === 'Name'" class="example" v-text="nameEg"></span>
        <textarea v-model="messageContent" @input="processTemplate" maxlength="1024" id="messageFormat"
          name="messageContent" rows="4" placeholder="Enter your message here..." class="form-control"
          required :disabled="selectedVarType === ''"></textarea>
        <span v-if="variableError" class="error-message">{{ variableError }}</span>
      </div>

      <!-- Contact Variables -->
      <div v-if="hasSampleValues && this.selectedCategory !== 'AUTHENTICATION'" class="form-group">
        <label>Contact Variables</label>
        <p class="group-title">Specify variables for your message. These variables can be changed at the time of
          spending</p>
        <div v-for="(value, key) in sampleValues" :key="key" class="variables">
          <label>{{ key }}</label>
          <input v-model="sampleValues[key]" @input="updateSampleValue(key, $event.target.value)"
            placeholder="Sample value" name="sampleValues" class="form-control" />
        </div>
      </div>

      <!-- Content Variables, Use for Auth -->
      <div v-if="this.selectedCategory === 'AUTHENTICATION'" class="form-group">
        <label>Content </label>
        <p class="group-title">Content for authentication message templates can't be edited. You can add additional
          content from the options below</p>
        <div class="content-variables">
          <div>
            <input v-model="securityRecommendation" type="checkbox" name="securityRecommendation" id="security">
            <label for="security">Add security recommendation</label>
          </div>
          <div>
            <input v-model="expiryTime" type="checkbox" name="expiryTime" id="expiry time">
            <label for="expiry time">Add expiry time for the code</label>
          </div>
          <!-- Expire time -->
          <div v-if="expiryTime" class="expires-container">
            <label for="expires">Expires in</label>
            <div class="input-group">
              <input type="number" v-model="timeValue" min="1" max="90" name="timeValue" class="time-input" />
              <span class="time-unit">minutes</span>
            </div>
            <span>The time should be between 1 to 90 minutes.</span>
          </div>
        </div>
      </div>

      <!-- Template Footer -->
      <div v-if="this.selectedCategory !== 'AUTHENTICATION'" class="form-group">
        <label for="footerText">Template Footer Text (Optional)</label>
        <p class="group-title">Footer text is optional and only upto 60 characters are allowed.</p>
        <input v-model="templateFooter" maxlength="60" id="footerText" name="templateFooter" type="text"
          placeholder="Enter footer text here" class="form-control" />
      </div>

      <!-- Template Dynamic buttons -->
      <div class="form-group">
        <label>Buttons</label>
        <p class="group-title">Create buttons that let customers respond to your message or take action. You can add
          up to ten buttons. If you add more than three buttons, they will appear in a list.</p>
        <!-- <TemplateDynamicButton :selectedCategory="selectedCategory" @update-buttons="updateButtonData" /> -->
        <TemplateDynamicButton 
        :selectedCategory="selectedCategory" 
        :initialButtons="buttonData"
        @update-buttons="updateButtonData" 
      />

      </div>

      <!-- Submit Button -->
      <div class="form-group">
        <button type="submit" class="btn btn-primary submit-btn" :disabled="this.isTemplateSubmitRunning || this.variableError !== ''">{{
          this.isTemplateSubmitRunning ? 'Submitting ...' : 'Submit' }}</button>
      </div>
    </form>
  </div>
  <!-- Preview Section -->
  <div class="template-preview">
    <div class="preview">
      <div class="preview-header">
        <h3>{{ templateName ? templateName : 'Template Preview' }}</h3>
      </div>
      <div class="preview-box">
        <div class="preview-content">
          <div class="preview-img-div">
            <img v-if="this.selectedType === null" :src="defaultPre" alt="default Pre">
            <img v-if="this.selectedType === 'IMAGE'" :src="preImg" alt="preview img">
            <img v-if="this.selectedType === 'VIDEO'" :src="preVideo" alt="preview video">
            <img v-if="this.selectedType === 'LOCATION'" :src="preLocation" alt="preview location">
            <img v-if="this.selectedType === 'DOCUMENT'" :src="preDoc" alt="preview location">
          </div>
          <p v-if="templateHeader" class="header">{{ templateHeader }}</p>
          <p v-html="previewContent" class="format-content"></p>
          <span class="footer">{{ templateFooter }}</span>
          <div v-for="(name, index) in this.buttonData" :key="index" class="preview-btns">
            <button v-if="name.text">{{ name.text }}</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</template>

<script>
import axios from '@/utils/api.js';
import ISO6391 from "iso-639-1";
import ErrorComponent from '@/components/Error'
import ArrowIcon from '../vuechat/components/SvgIcon/arrow_icon.svg';
import PreImg from '../vuechat/components/SvgIcon/pre-img.svg';
import PreLocation from '../vuechat/components/SvgIcon/pre-location.svg';
import PreVideo from '../vuechat/components/SvgIcon/pre-video.svg';
import PreDoc from '../vuechat/components/SvgIcon/pre-doc.svg';
import DefaultPre from '../vuechat/components/SvgIcon/default-pre.svg';
import DefaultImgPre from '../vuechat/components/PngIcons/profile-placeholder.png';
import TempDefaultVideo from '../vuechat/components/SvgIcon/temp_default_video.mp4';
import {binary_pdf_data} from "../vuechat/utils/dummy-pdf-base64.js";

import { TEMPLATE_TYPE, TEMPLATE_CATEGORY, VARIABLE_TYPE } from '../vuechat/utils/constants';
import TemplateDynamicButton from '@/components/TemplateDynamicButton'
import SuccessComponent from '@/components/Success'

export default {
name: 'CreateTemplate',
components: { ErrorComponent, TemplateDynamicButton, SuccessComponent },
props: ['editData', 'tokens', 'updatingTemplate', 'error'],
emits: ['create-handler'],

data() {
return {
  arrowIcon: ArrowIcon,
  preImg: PreImg,
  preVideo: PreVideo,
  preLocation: PreLocation,
  preDoc: PreDoc,
  defaultPre: DefaultPre,
  defaultImgPre: DefaultImgPre,
  tempSampleVideo: TempDefaultVideo,
  preConvertedDocument: binary_pdf_data,
  templateCategory: TEMPLATE_CATEGORY,
  variableType: VARIABLE_TYPE,
  templateType: TEMPLATE_TYPE,
  name: this.editData ? this.editData.name : '',
  pattern: this.editData ? this.editData.pattern : '',
  selectedCategory: "",
  selectedVarType: "",
  showTokens: false,
  tokensData: this.tokens,
  selectedType: null,
  dropdownOpen: false,
  templateName: "",
  templateFooter: "",
  messageContent: "",
  sampleValues: {},
  previewContent: "",
  languages: ISO6391.getAllNames().map(name => ({
    code: ISO6391.getCode(name),
    name
  })),
  selectedLanguage: "",
  templateHeader: "",
  successMsg: '',
  errorMsg: '',
  templateNameError: '',
  expiryTime: false,
  timeValue: 0,
  securityRecommendation: false,
  buttonData: [],
  isTemplateSubmitRunning: false,
  variableError: '',
  numberEg: 'E.g: Hello {{ 1 }}, Please place your order today. {{ 2 }} is back in stock!',
  nameEg: 'E.g: Hello {{firstname}}, place your order today and get it delivered to {{location}} soon!',
  copiedMediaId: null,
}
},

computed: {
hasSampleValues() {
  return Object.keys(this.sampleValues).length > 0;
},
},

watch: {
securityRecommendation: "updateAuthMessageContent",
timeValue: "updateAuthMessageContent",

selectedCategory(newCategory) {
  // Store current button data
  const currentButtons = [...this.buttonData];

  // Only set default authentication values if switching TO authentication category
  // and there's no existing content
  if (newCategory === 'AUTHENTICATION' && !this.messageContent) {
    this.sampleValues = {
      '{{1}}': ''
    };
    this.messageContent = 'Your verification code is {{1}}';
    this.templateFooter = '';
  }
  // When switching FROM authentication, only clear if there's no custom content
  else if (newCategory !== 'AUTHENTICATION' && this.messageContent === 'Your verification code is {{1}}') {
    this.previewContent = '';
    this.sampleValues = {};
    this.messageContent = '';
  }

  // Restore button data after category change
  this.$nextTick(() => {
    this.buttonData = currentButtons;
  });
},

sampleValues: {
  handler(newVal) {
    if (this.selectedCategory === 'AUTHENTICATION') {
      this.updatePreview();
    }
  },
  deep: true
},    selectedType: {
  handler(newVal, oldVal) {
    // Keep header text unless explicitly cleared
    // Don't automatically clear the header text when changing types
  },
  deep: true
},

messageContent: {
  handler(newVal) {
    return newVal
  },
  deep: true
},

selectedVarType(newType) {
  // Validate variables in the messageContent when variable type changes
  let placeholders = this.messageContent.match(/{{\w+}}/g) || [];
  let variables = placeholders.map(p => p.replace(/[{}]/g, ""));

  const isValid = variables.every(variable => {
    if (newType === "Name") {
      return isNaN(variable); // Name variables should not be numbers
    } else if (newType === "Number") {
      return !isNaN(variable); // Number variables should only be numbers
    }
    return true;
  });

  if (!isValid) {
    this.variableError = `Existing variables in the message do not match the selected type (${newType}).`;
  } else {
    this.variableError = ''; // Clear error if valid
  }
},    editData: {
  immediate: true,
  handler(newData) {
    if (newData) {
      // When copying template, maintain original name format but add _copy
      this.templateName = (newData.name || "").endsWith('_copy') ? 
        newData.name : 
        `${newData.name}_copy`;
        
      // Important: Maintain the original category when copying
      this.selectedCategory = newData.category;
      this.selectedLanguage = newData.language || "";
      
      // Explicitly check for NAMED parameter format
      this.selectedVarType = (newData.parameter_format || "").toUpperCase() === "NAMED" ? "Name" : "Number";
      
      // Process components
      const headerComponent = newData.components.find(c => c.type === 'HEADER');
      const bodyComponent = newData.components.find(c => c.type === 'BODY');
      const footerComponent = newData.components.find(c => c.type === 'FOOTER');
      const buttonsComponent = newData.components.find(c => c.type === 'BUTTONS');
      
    if (headerComponent) {
  this.selectedType = headerComponent.format || null;

  if (headerComponent.format === 'TEXT') {
    this.templateHeader = headerComponent.text || '';
  } else if (headerComponent.text) {
    this.templateHeader = headerComponent.text;
  }

  // ⭐ Preserve copied media ID
  this.copiedMediaId = headerComponent.example?.header_handle?.[0] || null;
}

      // Set body content and process example values
      if (bodyComponent) {
        this.messageContent = bodyComponent.text || '';
        
        // Handle named parameters format
        if (bodyComponent.example?.body_text_named_params) {
          const sampleValues = {};
          bodyComponent.example.body_text_named_params.forEach(param => {
            sampleValues[param.param_name] = param.example;
          });
          this.sampleValues = sampleValues;
          this.selectedVarType = "Name"; // Force Name type for named parameters
        } 
        // Handle numbered parameters format
        else if (bodyComponent.example?.body_text?.[0]) {
          const sampleValues = {};
          const exampleValues = bodyComponent.example.body_text[0];
          
          // Extract variables from message content
          const variables = (bodyComponent.text.match(/{{\d+}}/g) || [])
            .map(match => parseInt(match.replace(/[{}]/g, "")));
          
          // Map example values to variables
          variables.forEach((varNum, index) => {
            if (index < exampleValues.length) {
              sampleValues[varNum] = exampleValues[index];
            }
          });
          this.sampleValues = sampleValues;
          this.selectedVarType = "Number"; // Force Number type for numbered parameters
        }
      }

      // Set footer
      if (footerComponent) {
        this.templateFooter = footerComponent.text || '';
      }

      // Handle authentication specific fields
      if (this.selectedCategory === 'AUTHENTICATION') {
        const bodyComp = newData.components.find(c => c.type === 'BODY');
        const footerComp = newData.components.find(c => c.type === 'FOOTER');
        
        if (bodyComp) {
          this.securityRecommendation = bodyComp.add_security_recommendation || false;
        }
        
        if (footerComp) {
          this.expiryTime = !!footerComp.code_expiration_minutes;
          this.timeValue = footerComp.code_expiration_minutes || 0;
        }
      }

      // Set button data
      if (buttonsComponent?.buttons) {
        this.buttonData = JSON.parse(JSON.stringify(buttonsComponent.buttons));
      }

      // Update preview
      this.$nextTick(() => {
        this.processTemplate();
        this.updatePreview();
      });
    }
  }
},
},

methods: {
updateAuthMessageContent() {
let baseMessage = "Your verification code is {{1}}.";
if (this.securityRecommendation) {
  baseMessage += " For your security, do not share this code.";
} else {
  baseMessage = "Your verification code is {{1}}";
}
if (this.expiryTime && this.timeValue > 0) {
  this.templateFooter = ` This code expires in ${this.timeValue} minute(s).`;
} else {
  this.templateFooter = "";
}
this.previewContent = baseMessage;
},

updateButtonData(newData) {
this.buttonData = JSON.parse(JSON.stringify(newData)); // Update the parent data
},

toggleDropdown() {
this.dropdownOpen = !this.dropdownOpen;
},

selectType(type) {
const previousType = this.selectedType;

// If changing from Text to another type
if (previousType === 'TEXT' && type && type !== 'TEXT') {
  this.templateHeader = ''; // Clear header text
}

// When changing to a media type, ensure proper handling
if (['IMAGE', 'VIDEO', 'DOCUMENT'].includes(type)) {
  // If we're copying a template, verify compatibility
  if (this.editData) {
    const originalHeader = this.editData.components.find(c => c.type === 'HEADER');
    const originalType = originalHeader?.format;
    
    // Inform user that changing media type will require new media to be uploaded
    if (originalType && originalType !== type.toUpperCase()) {
      // Just set a notification that doesn't block the change
      this.successMsg = "You've changed media type. A new file will be uploaded.";
      setTimeout(() => { this.successMsg = ''; }, 3000);
    }
  }
  
  // Allow the type change
  this.selectedType = type;
} else {
  this.selectedType = type;
}

this.dropdownOpen = false;
},

validateTemplateName() {
const regex = /^[a-z0-9_]+$/;
if (!regex.test(this.templateName)) {
  this.templateNameError = 'Special characters and white-space are not allowed.';
} else {
  this.templateNameError = '';
}
},

processTemplate() {
let updatedContent = this.messageContent;
let placeholders = updatedContent.match(/{{\w+}}/g) || [];
let variables = placeholders.map(p => p.replace(/[{}]/g, ""));

// Validate variables based on selected type
const isValid = variables.every(variable => {
  if (this.selectedVarType === "Name") {
    // For named variables, allow only lowercase letters, numbers and underscores
    return /^[a-z0-9_]+$/.test(variable);
  } else if (this.selectedVarType === "Number") {
    return !isNaN(variable); // Number variables should only be numbers
  }
  return true;
});

if (!isValid) {
  this.variableError = this.selectedVarType === "Name" ? 
    "Named variables can only contain lowercase letters, numbers and underscores" :
    "Only number variables are allowed";
} else {
  this.variableError = '';
}

// Only resequence variables if using Number type
if (this.selectedVarType === "Number") {
  let numberMap = {};
  let currentNumber = 1;
  let oldSampleValues = { ...this.sampleValues };
  
  // Create mapping of old to new numbers
  variables.forEach(variable => {
    if (!isNaN(variable)) {
      if (!numberMap[variable]) {
        numberMap[variable] = currentNumber++;
      }
    }
  });

  // Update content with sequential numbers
  updatedContent = updatedContent.replace(/{{\w+}}/g, (match) => {
    const variable = match.replace(/[{}]/g, "");
    return isNaN(variable) ? match : `{{${numberMap[variable]}}}`;
  });

  // Update sample values maintaining the examples
  let newSampleValues = {};
  Object.entries(numberMap).forEach(([oldNum, newNum]) => {
    newSampleValues[newNum] = oldSampleValues[oldNum] || 
                            oldSampleValues[newNum] || 
                            `Test ${newNum.toString().padStart(2, '0')}`;
  });
  this.sampleValues = newSampleValues;
} else {
  // For named variables, keep as is
  let uniqueVariables = [...new Set(variables)];
  let oldSampleValues = { ...this.sampleValues };
  let newSampleValues = {};
  
  uniqueVariables.forEach((variable) => {
    // Keep existing sample value or use a meaningful default
    newSampleValues[variable] = oldSampleValues[variable] || "";
  });
  
  this.sampleValues = newSampleValues;
}

this.messageContent = updatedContent;
this.updatePreview();
},

updateSampleValue(key, value) {
this.$set(this.sampleValues, key, value);
this.updatePreview();
},

updatePreview() {
let previewText = this.messageContent;

for (let key in this.sampleValues) {
  // Ensure we match {{key}} correctly in regex (escaping `{}`)
  let regex = new RegExp(`\\{\\{${key}\\}\\}`, "g");

  // Replace only if the value exists and is not empty
  if (this.sampleValues[key] && this.sampleValues[key].trim() !== "") {
    previewText = previewText.replace(regex, `[${this.sampleValues[key]}]`);
  }
}
// Preserve new lines in preview
this.previewContent = previewText.replace(/\n/g, "<br>");
},

async submitHandler(event) {
  event?.preventDefault();
  this.isTemplateSubmitRunning = true;

  try {
    let uploadedMediaId = null;
    console.log(this.selectedType);
    if (["IMAGE", "VIDEO", "DOCUMENT"].includes(this.selectedType)) {
      try {
        uploadedMediaId = await this.uploadFile();
        console.log("Uploaded new media for fresh template:", uploadedMediaId);
      } catch (error) {
        this.errorMsg = "Media upload failed: " + (error.message || "unknown error");
        this.isTemplateSubmitRunning = false;
        return;
      }
    }

    // ⭐ Fallback to copied media ID
    if (!uploadedMediaId && this.copiedMediaId) {
      uploadedMediaId = this.copiedMediaId;
      console.log("Preserved copiedMediaId used:", uploadedMediaId);
    }

    const constructHeaderComponent = (uploadedMediaId) => {
      if (!this.selectedType || this.selectedType === 'NONE') return false;

      if (this.selectedType === 'TEXT') {
        return {
          type: "HEADER",
          format: "TEXT",
          text: this.templateHeader
        };
      }

      if (["IMAGE", "VIDEO", "DOCUMENT"].includes(this.selectedType)) {
        if (!uploadedMediaId) {
          this.errorMsg = "Media upload is required for this template type";
          this.isTemplateSubmitRunning = false;
          return false;
        }

        return {
          type: "HEADER",
          format: this.selectedType.toUpperCase(),
          ...(this.templateHeader && { text: this.templateHeader }),
          example: {
            header_handle: [uploadedMediaId]
          }
        };
      }

      return false;
    };

    const headerComponent = constructHeaderComponent(uploadedMediaId);
    console.log("Header Component Returned:", headerComponent);

    const requestBody = {
      name: this.templateName,
      language: this.selectedLanguage,
      category: this.selectedCategory,
      ...(this.selectedVarType === "Name" && { parameter_format: "NAMED" }),
      components: [
        headerComponent,
        this.templateFooter ? { type: "FOOTER", text: this.templateFooter } : false,
        {
          type: "BODY",
          text: this.messageContent,
          ...(Object.keys(this.sampleValues).length > 0 && {
            example: this.formatExampleValues()
          })
        },
        this.buttonData.length > 0 ? {
          type: "BUTTONS",
          buttons: [...this.buttonData]
        } : false
      ].filter(Boolean)
    };

    const authRequestBody = {
      name: this.templateName,
      language: this.selectedLanguage,
      category: this.selectedCategory,
      components: [
        {
          type: "BODY",
          add_security_recommendation: this.securityRecommendation
        },
        this.timeValue ? {
          type: "FOOTER",
          code_expiration_minutes: this.timeValue
        } : false,
        {
          type: "BUTTONS",
          buttons: [
            {
              type: "OTP",
              otp_type: "COPY_CODE",
              text: "Copy Code"
            }
          ]
        }
      ].filter(Boolean)
    };

    const bodyPayload = this.selectedCategory === 'AUTHENTICATION' ? authRequestBody : requestBody;

    const apiPromise = axios.post(
      `api/whatsapp/template/create?user_id=${this.$store.state.userData.user_id}`,
      { template: bodyPayload }
    );
    const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error("API request timed out")), 30000));

    const response = await Promise.race([apiPromise, timeoutPromise]);
    const data = response;

    if (data?.data?.ok) {
      this.successMsg = "Template created successfully!";
      this.templateName = "";
      this.selectedCategory = "";
      this.selectedVarType = "";
      this.selectedLanguage = "";
      this.templateHeader = "";
      this.messageContent = "";
      this.templateFooter = "";
      this.selectedType = null;
      this.sampleValues = {};
      this.previewContent = "";
      this.buttonData = [];
      this.copiedMediaId = null; // clear cache after success
    } else {
      this.errorMsg = data?.data?.message || "Template creation failed";
    }
  } catch (error) {
    this.errorMsg = "Template submission failed: " + (error.message || "unknown error");
  } finally {
    this.isTemplateSubmitRunning = false;
    setTimeout(() => {
      this.successMsg = "";
      this.errorMsg = "";
    }, 3000);
  }
},

async uploadFile() {
// Select the appropriate file based on the selected type
let filePath = this.selectedType === "IMAGE" ? this.defaultImgPre :
  this.selectedType === "VIDEO" ? this.tempSampleVideo :
    this.selectedType === "DOCUMENT" ? null : null;

if (!filePath && this.selectedType !== "DOCUMENT") {
  this.errorMsg = `No default file found for ${this.selectedType} type`;
  setTimeout(() => { this.errorMsg = '' }, 3000);
  return null;
}

try {
  let formData = new FormData();

  if (this.selectedType === "DOCUMENT" && this.preConvertedDocument) {
    // Use Pre-Stored Base64 Document Directly
    const file = new Blob([this.preConvertedDocument], { type: "application/pdf" });
    formData.append("file", file);
  } else {
    // Convert Image/Video as needed
    const res = await fetch(filePath);
    if (!res.ok) {
      throw new Error(`Failed to fetch ${this.selectedType} file`);
    }
    const blob = await res.blob();
    const file = new File([blob], `default_${this.selectedType.toLowerCase()}`, { type: blob.type });
    formData.append("file", file);
  }

  // Send File to API
  const response = await axios.post(
    `api/whatsapp/template/upload?user_id=${this.$store.state.userData.user_id}`,
    formData,
    {
      headers: { "Accept": "application/json", "Content-Type": "multipart/form-data" }
    }
  );

  if (response?.data?.ok) {
    return response?.data?.data?.h; // Return uploaded media ID
  } else {
    this.errorMsg = response?.data?.message || `${this.selectedType} upload failed!`;
    setTimeout(() => { this.errorMsg = '' }, 3000);
    return null;
  }
} catch (error) {
  console.error("Upload API Error:", error);
  this.errorMsg = `${this.selectedType} upload failed: ${error.message}`;
  setTimeout(() => { this.errorMsg = '' }, 3000);
  return null;
}
},

formatExampleValues() {
if (this.selectedVarType === "Name") {
  return {
    "body_text_named_params": Object.entries(this.sampleValues).map(([key, value]) => ({
      "param_name": key.replace(/[{}]/g, ""),
      "example": value || key.replace(/[{}]/g, "")
    }))
  };
} else {
  const maxNumber = Math.max(...Object.keys(this.sampleValues).map(num => parseInt(num)));
  const orderedValues = Array.from({ length: maxNumber }, (_, index) => 
    this.sampleValues[index + 1] || `Test ${(index + 1).toString().padStart(2, '0')}`
  );
  return {
    "body_text": [orderedValues]
  };
}
}
},
mounted() {
if (this.editData) {
  // Process template data
  this.templateName = this.editData.name;
  this.selectedCategory = this.editData.category;
  this.selectedLanguage = this.editData.language;

  // Process components
  const headerComponent = this.editData.components.find(c => c.type === 'HEADER');
  const bodyComponent = this.editData.components.find(c => c.type === 'BODY');
  const footerComponent = this.editData.components.find(c => c.type === 'FOOTER');
  const buttonsComponent = this.editData.components.find(c => c.type === 'BUTTONS');

  // Set header type and text
  if (headerComponent) {
    this.selectedType = headerComponent.format || null;
    // Ensure header text is set regardless of format
    if (headerComponent.text) {
      this.templateHeader = headerComponent.text;
    }
  }

  // Set other template data
  this.messageContent = bodyComponent?.text || '';
  this.templateFooter = footerComponent?.text || '';
  
  // Handle button data
  if (buttonsComponent?.buttons) {
    this.$nextTick(() => {
      const templateDynamicButton = this.$children.find(child => child.$options.name === 'TemplateDynamicButton');
      if (templateDynamicButton) {
        const hasQuickReply = buttonsComponent.buttons.some(btn => btn.type === 'QUICK_REPLY');
        const hasCTA = buttonsComponent.buttons.some(btn => ['URL', 'PHONE_NUMBER'].includes(btn.type));
        
        if (hasQuickReply && hasCTA) {
          templateDynamicButton.selectedOption = 'all';
        } else if (hasQuickReply) {
          templateDynamicButton.selectedOption = 'quick';
        } else if (hasCTA) {
          templateDynamicButton.selectedOption = 'cta';
        }
        
        this.buttonData = buttonsComponent.buttons;
        templateDynamicButton.$emit('update-buttons', buttonsComponent.buttons);
      }
    });
  }

  this.$nextTick(() => {
    this.updatePreview();
  });
}
this.processTemplate();
},
}

</script>

<style lang="scss" scoped>
.preview-box {
background-image: url('../vuechat/components/PngIcons/preview.jpg') !important;
}
.screenshot-btn {
display: flex;
align-items: center;
gap: 5px;
}
</style>