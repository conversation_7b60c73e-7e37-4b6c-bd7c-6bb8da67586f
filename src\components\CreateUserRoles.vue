<template>
    <div class="create-user-roles">
        <div class="back-div">
            <img @click="handleBackClick" :src="backIcon" alt="back btn">
            <h3>Go Back</h3>
        </div>
        <div class="content">
            <div class="title">
                <p>Create User Roles</p>
            </div>
            <div class="add-role">
                <span>Please enter the user role you want to create</span>
                <div>
                    <input type="text" v-model="roleName" placeholder="Marketing">
                    <button @click="addRole(roleName)" class="btn btn-primary" :disabled="isButtonDisabled">Add
                        Role</button>
                </div>
            </div>
            <p class="created-list-title">List of user roles created:</p>
            <div class="created-list">
                <div v-if="roles.length === 0">
                    <p>No list found</p>
                </div>
                <div v-for="(roles, index) in roles" :key="index">
                    <p>{{ roles.name }}</p>
                    <button @click="removeRole(roles.id)">
                        <img :src="deletIcon" alt="delete">
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex'
import axios from '@/utils/api.js';
import DeletIcon from '@/assets/icons/delete_icon.svg';
import BackIcon from '@/assets/icons/back.svg';

export default {
    name: 'CreateUserRoles',
    data() {
        return {
            backIcon: BackIcon,
            deletIcon: DeletIcon,
            roleName: '',
            defaultUserRoles: ['marketing', 'sales', 'operations'],
            roles: [],
        }
    },
    props: {
        selectUserRole: {
            type: Function,
            required: true,
        },
    },
    computed: {
        ...mapState(['userData']),
        isButtonDisabled() {
            return this.roleName.length === 0;
        }
    },
    methods: {
        async userRoles(method, endpoint = '', data = {}) {

            try {
                const isGet = method.toUpperCase() === 'GET';
                const url = isGet && Object.keys(data).length
                    ? `api/roles${endpoint}?${new URLSearchParams(data).toString()}`
                    : `api/roles${endpoint}`;

                const response = await axios({
                    method,
                    url,
                    headers: { 'Content-Type': 'application/json' },
                    data: isGet ? undefined : data, // Only include data for non-GET requests
                });

                return response.data;
            } catch (error) {
                console.error('API Error:', error.response?.data || error.message);
                throw error;
            }
        },

        // Fetch roles
        async fetchRoles() {
            try {
                const data = await this.userRoles('GET', '', { user_id: decodeURIComponent(this.userData.user_id) });
                this.roles = JSON.parse(JSON.stringify(data.roles));
                this.$emit('apiDataReceived', JSON.parse(JSON.stringify(data.roles)));
            } catch (error) {
                console.error('Error fetching roles:', error);
            }
        },

        // Create a new role
        async addRole(roleName) {
            try {
                const roleData = {
                    user_id: decodeURIComponent(this.userData.user_id),
                    name: roleName,
                };
                const newRole = await this.userRoles('POST', '', roleData);
                this.roleName = ''
                this.roles.push(newRole.role);
            } catch (error) {
                console.error('Error creating role:', error);
            }
        },

        // Remove a role
        async removeRole(id) {
            try {
                const deletedRole = await this.userRoles('DELETE', `/${id}`, { user_id: decodeURIComponent(this.userData.user_id) });
                this.roles = this.roles.filter(role => role.id !== id);
                console.log('Role Deleted:', deletedRole);
            } catch (error) {
                console.error('Error deleting role:', error);
            }
        },

        handleBackClick() {
            this.selectUserRole();
            this.fetchRoles();
            this.$emit('reloadUsers');
        }

    },
    mounted() {
        // Fetchhing roles
        this.fetchRoles();
    },
}
</script>