<template>
    <div class="not-found-div">
        <img :src="dataNotFound" alt="data not found">
        <div>
            <h3>
                Oops! It looks like there's no data available for the selected period 
            </h3>
            <p>Please adjust your date range and try again</p>
        </div>
    </div>
</template>

<script>
import DataNotFound from '@/assets/icons/data_not_found.svg'

export default {
    name: 'DataNotFound',

    data() {
        return {
            dataNotFound: DataNotFound
        }
    }
}
</script>