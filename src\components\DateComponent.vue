<template>
  <div class="date-range">
    <img :src="calendar" class="calendar" alt="calendar" />
    <date-range-picker ref="datePicker" :ranges="customRanges" v-model="dateRange" opens="right" :showDropdowns="true"
      :maxDate="maxDate" @update="handleDateRangeSelect">
    </date-range-picker>
    <img :src="dropDown" class="dropdown-icon" alt="dropdown" />
  </div>
</template>

<script>
import DateRangePicker from 'vue2-daterange-picker/src/components/DateRangePicker'
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css'
import Calendar from '@/assets/icons/calendar.svg'
import DropDown from '@/assets/icons/dropdown_icon.svg'
import moment from 'moment';

export default {
  components: {
    DateRangePicker
  },
  props: [
    'startDate',
    'endDate',
  ],
  watch: {
    startDate: function (newVal, oldVal) {
      this.dateRange.startDate = !this.dateRange.startDate ? newVal : this.dateRange.startDate;
      
    },
    endDate: function (newVal, oldVal) {
      
      this.dateRange.endDate = !this.dateRange.endDate ? newVal : this.dateRange.endDate;
    }
  },
  data() {
    return {
      dateRange: {
        startDate: null,
        endDate: null,
      },
      maxDate: new Date(),
      calendar: Calendar,
      dropDown: DropDown,
      customRanges: {
        Today: [this.getToday(), this.getToday()],
        Yesterday: [this.getYesterday(), this.getYesterday()],
        'This week': [this.getThisWeekStart(), this.getToday()],
        'Last week': [this.getLastWeekStart(), this.getLastWeekEnd()],
        'This month': [this.getThisMonthStart(), this.getToday()],
        'Last month': [this.getLastMonthStart(), this.getLastMonthEnd()],
        'This year': [this.getThisYearStart(), this.getToday()],
        'Last year': [this.getLastYearStart(), this.getLastYearEnd()]
      }
    }
  },
  methods: {
    handleDateRangeSelect(value) {
      this.dateRange.startDate = value
        ? value?.startDate
        : this.dateRange?.startDate
      this.dateRange.endDate = value
        ? value?.endDate
        : this.dateRange?.endDate

      this.$emit('selectedDate', moment(this.dateRange.startDate).format('DD/MM/YYYY'), moment(this.dateRange.endDate).format('DD/MM/YYYY'))
    },
    // Custom range calendar options handling *** Start
    getToday() {
      let today = new Date()
      today.setHours(0, 0, 0, 0)
      return today
    },
    getYesterday() {
      let yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      yesterday.setHours(0, 0, 0, 0)
      return yesterday
    },
    getThisWeekStart() {
      let today = new Date()
      let thisWeekStart = new Date(today)
      thisWeekStart.setDate(today.getDate() - today.getDay())
      thisWeekStart.setHours(0, 0, 0, 0)
      return thisWeekStart
    },
    getLastWeekStart() {
      let lastWeekStart = new Date(this.getThisWeekStart())
      lastWeekStart.setDate(lastWeekStart.getDate() - 7)
      return lastWeekStart
    },
    getLastWeekEnd() {
      let lastWeekEnd = new Date(this.getThisWeekStart())
      lastWeekEnd.setDate(lastWeekEnd.getDate() - 1)
      return lastWeekEnd
    },
    getThisMonthStart() {
      let today = new Date()
      let thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1)
      thisMonthStart.setHours(0, 0, 0, 0)
      return thisMonthStart
    },
    getLastMonthStart() {
      let lastMonthStart = new Date(this.getThisMonthStart())
      lastMonthStart.setMonth(lastMonthStart.getMonth() - 1)
      return lastMonthStart
    },
    getLastMonthEnd() {
      let lastMonthEnd = new Date(this.getThisMonthStart())
      lastMonthEnd.setDate(lastMonthEnd.getDate() - 1)
      return lastMonthEnd
    },
    getThisYearStart() {
      let today = new Date()
      let thisYearStart = new Date(today.getFullYear(), 0, 1)
      thisYearStart.setHours(0, 0, 0, 0)
      return thisYearStart
    },
    getLastYearStart() {
      let lastYearStart = new Date(this.getThisYearStart())
      lastYearStart.setFullYear(lastYearStart.getFullYear() - 1)
      return lastYearStart
    },
    getLastYearEnd() {
      let lastYearEnd = new Date(this.getThisYearStart())
      lastYearEnd.setDate(lastYearEnd.getDate() - 1)
      return lastYearEnd
    }
    // Custom range calendar options handling *** End
  }
}
</script>
