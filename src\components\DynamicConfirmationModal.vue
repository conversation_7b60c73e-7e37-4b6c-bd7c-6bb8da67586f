<template>
    <div v-if="show">
        <div class="modal-overlay">
            <div class="modal-box">
                <p class="modal-message">{{ description }}</p>
                <div class="modal-actions">
                    <button class="btn btn-primary" @click="emitDeleteHandler">Confirm</button>
                    <button class="cancel-button" @click="emitCancelHandler">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'DynamicConfirmationModal',

    props: ['show', 'description'],

    methods: {
        emitDeleteHandler() {
            this.$emit('delete-handler');
        },
        emitCancelHandler() {
            this.$emit('cancel-handler');
        },
    },
};
</script>
