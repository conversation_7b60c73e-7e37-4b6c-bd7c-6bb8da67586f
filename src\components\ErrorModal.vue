<template>
  <div v-show="show" class="error-modal">
    <transition name="vac-bounce">
      <div v-if="show" class="error-modal_content">
        <div class="error-header">
          Error
          <button class="close-button" @click.prevent="toggle">&times;</button>
        </div>

        <div v-if="ErrorMessage" class="error-message">
          <p>{{ ErrorMessage }}</p>
        </div>

        <div v-else class="error-message">
          <p>Oops! Looks like you do not have access to initiate a cancellation.</p>
          <p>Please contact your admin for the same</p>
        </div>
      </div>
    </transition>
    <div class="error-modal_overlay" @click.prevent="toggle" />
  </div>
</template>

<script>
export default {
  name: 'ErrorModal',
  props: {
    show: { type: Boolean },
    toggle: { type: Function, default: () => ({}) },
    ErrorMessage: { type: String, default: '' }
  }
}
</script>
