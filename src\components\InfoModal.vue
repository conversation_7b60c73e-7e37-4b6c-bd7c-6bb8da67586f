<template>
    <div class="info-modal-main-container">
        <div class="info-modal-secondary-div">

            <div class="info-modal">
                <img @click="closeModal" :src="close" alt="close">
                <div class="row-1">
                    <h3>Metrics Definition</h3>
                    <div>
                        <span>Messages Sent: </span>
                        <span>No. of messages sent in a campaign. This number may or may not match the number of contacts in
                            your campaign list. But it will never exceed the number of contacts in your campaign list.
                        </span>
                    </div>
                    <div>
                        <span>Delivered Rate: </span>
                        <span>A percentage value of number of Messages Delivered out of the number of Messages Sent in a
                            campaign. The messages delivered number is provided by Meta.
                        </span>
                    </div>
                    <div>
                        <span>Read Rate: </span>
                        <span>A percentage value of number of Messages Read out of the number of Messages Sent in a campaign.
                            The messages read number is provided by Meta.
                        </span>
                    </div>
                    <div>
                        <span>Contacts who became Opportunity: </span>
                        <span>This metric is indicative. We calculate it in the following manner:</span>
                        <ol>
                            <li>We make a list of all contacts whose "Became an Opportunity Date" property value falls between
                                the reporting period.</li>
                            <li>From the above list we filter out the contacts who were sent atleast one of the campaigns that
                                falls in the reporting period.
                            </li>
                            <li>From the filtered out list in Step 2, we count the contact under the campaign whose Sent date
                                preceeds the contact's "Became an Opportunity Date" by the least difference.
                            </li>
                        </ol>
                        <div class="child-row">
                            <span> Illustration: </span>Reporting period is Jan 1, 2024 to Jan 15, 2024. Campaign A was sent on
                            Jan 5, 2024
                            and Campaign B was sent on Jan 10, 2024. Both campaigns were sent to Contact X, Y and Z. X became an
                            opportunity on Jan 7, Y became an Opportunity on Jan 9 and Z became an Opportunity on Jan 14. We'll
                            show 2 opportunities under Camapign A (Contact X and Y) and 1 opportunity under Campaign B (Contact
                            Z).
                        </div>
                    </div>
                    <div>
                        <span>Contacts who became Customers: </span>
                        <span>This metric is indicative. We calculate it in the following manner:</span>
                        <ol>
                            <li>We make a list of all contacts whose "Became a Customer Date" property value falls between the
                                reporting period.</li>
                            <li>From the above list we filter out the contacts who were sent atleast one of the campaigns
                                that
                                falls in the reporting period.
                            </li>
                            <li>From the filtered out list in Step 2, we count the contact under the campaign whose Sent
                                date
                                <span class="text-highlight">preceeds</span> the contact's "Became a Customer Date" by the least
                                difference.
                            </li>
                        </ol>
                        <div class="child-row"><span>Illustration: </span>Reporting period is Jan 1, 2024 to Jan 15, 2024.
                            Campaign A was sent on Jan 5, 2024
                            and Campaign B was sent on Jan 10, 2024. Both campaigns were sent to Contact X, Y and Z. X became a
                            Customer on Jan 7, Y became an Customer on Jan 9 and Z became an Customer on Jan 14. We'll show 2
                            customers under Camapign A (Contact X and Y) and 1 customer under Campaign B (Contact Z).
                        </div>
                    </div>
                    <div>
                        <span>
                            Deals Created: </span>
                        <span>This metric is indicative. We calculate it in the following manner:</span>
                        <ol>
                            <li>We make a list of all contacts who were sent atleast one campaign in the reporting
                                period.</li>
                            <li>From the contacts list, we create a list of all Deals associated with the above
                                contacts.</li>
                            <li>From the above Deals list, we keep the Deals whose Create Date fall in the reporting
                                period.</li>
                            <li>From this refined list in Step 3, we count the Deal under the campaign whose Sent date
                                preceeds
                                the Deal's "Create Date" by the least difference</li>
                        </ol>
                    </div>
                    <div>
                        <span>Deals Won: </span>
                        <span>This metric is indicative. We calculate it in the following manner:
                        </span>
                        <ol>
                            <li>We make a list of all contacts who were sent atleast one campaign in the reporting period.</li>
                            <li>From the contacts list, we create a list of all Closed Won Deals associated with the above contacts.</li>
                            <li>From the above Deals list, we keep the Deals whose Close Date fall in the reporting period. </li>
                            <li>From this refined list in Step 3, we count the Deal under the campaign whose Sent date preceeds the Deal's "Close Date" by the least difference.</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import close from '@/assets/icons/close_blue.svg';

export default {
    name: 'InfoModal',

    props: {
        toggleModal: {
            type: Function,
            required: true,
        },
    },

    data() {
        return {
            close: close
        }
    },

    methods: {
        closeModal() {
            // Call the toggleModal function passed from the parent component
            this.toggleModal();
        },
    },
}
</script>