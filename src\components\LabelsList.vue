<template>
  <div class="labels_list">
    <div v-for="label in modifiedLabels" :key="label.id" class="labels_list-item">
      <div class="list-item_left">
        <div class="label-icon" :style="{ background: label.color }"></div>
        <h4 class="item-name">{{ label.name }}</h4>
      </div>
      <div class="list-item_right">
        <span class="label-date">{{ label.date }}</span>
        <img :src="deleteIcon" alt="Delete Icon" class="delete-icon" @click.stop="setId(label.id)" />
      </div>
    </div>
    <div v-if="showConfirmation" class="error-modal">
      <transition name="vac-bounce">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">Confirm Delete</h5>
            </div>
            <div class="modal-body mb-5" style="font-size: 16px !important">
              Are you sure you want to delete this item?
            </div>
            <div class="modal-footer">
              <button
                type="button"
                class="btn btn-danger text-white"
                @click.stop="
                  $emit('delete-handler', id)
                  showConfirmation = false
                "
              >
                Yes, Delete
              </button>
              <button type="button" class="btn btn-secondary m-lg-2" @click="showConfirmation = false">Cancel</button>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import { convertDate } from '@/utils/utils.js'
import DeleteIcon from '@/assets/icons/delete_icon.png'

export default {
  name: 'LabelsList',
  props: ['labels'],
  emits: ['delete-handler'],

  data() {
    return {
      id: null,
      deleteIcon: DeleteIcon,
      showConfirmation: false
    }
  },

  computed: {
    modifiedLabels() {
      return this.labels.map(el => ({
        ...el,
        date: convertDate(el.created_at)
      }))
    }
  },
  methods: {
    setId(newId) {
      this.id = newId
      this.showConfirmation = true
      console.log(`Id is now set to ${this.id}`)
    }
  }
}
</script>
<style>
button.close {
  font-size: 34px;
}

button.btn.btn-danger,
.btn.btn-secondary {
  font-size: 13px;
}

.error-modal {
  position: fixed !important;
  background: #222222b3;
}
</style>
