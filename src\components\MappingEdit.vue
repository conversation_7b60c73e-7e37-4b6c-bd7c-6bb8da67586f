<template>
  <div class="mapping-edit">
    <div class="row">
      <div class="col-md-8">
        <h4 class="mb-3">Field Mapping</h4>
        <form @submit.prevent="$emit('edit-handler', data)">
          <ul v-for="(screen, screenIndex) in asset" :key="screenIndex" class="list-group mb-5 rounded-5">
            <li class="list-group-item active" aria-current="true">
              <div class="list-header d-flex justify-content-between py-4 px-5">
                <div>
                  Form Properties
                  <strong>(Screen: {{ (screenIndex = screenIndex + 1) }}, {{ screen.title }})</strong>
                </div>
                <div></div>
                <div>HubSpot Properties</div>
              </div>
            </li>

            <li v-for="(input, index) in screen.inputs" :key="index" class="list-group-item">
              <div class="list-content d-flex justify-content-between align-items-center py-4 px-5">
                <div>
                  {{ input.label }}
                </div>
                <div class="text-center">
                  <img :src="rightArrowIcon" />
                </div>
                <div class="form-group">
                  <v-select
                    :options="[
                      {
                        id: 'first_' + index,
                        payload_key: input.payload_key,
                        name: 'None',
                        value: 'none'
                      },
                      ...properties.map(item => ({
                        id: item.name,
                        name: item.label,
                        value: item.name,
                        property: item,
                        payload_label: input.label,
                        payload_key: input.payload_key
                      }))
                    ]" label="name"
                    :placeholder="placeholders[input.payload_key]  || 'Choose Property'"
                    class="custom-vue-select" @input="handleInput($event)" />
                </div>
              </div>
            </li>
          </ul>
          <button type="submit" class="btn btn-primary fw-bolder">Save</button>
        </form>
      </div>
      <div class="col-md-4">
        <div class="template-form">
          <h4 class="mb-3">Template Name</h4>
          <div class="form-group">
            <input disabled type="text" class="form-control disabled" :value="template.name" />
          </div>
          <h4 class="mb-3 mt-5">Template Message</h4>
          <div class="form-group">
            <textarea
              disabled
              class="form-control disabled"
              :value="template.components[0].text"
              cols="4"
              rows="8"
            ></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DeleteIcon from '@/assets/icons/delete_icon.png'
import RightArrowIcon from '@/assets/icons/right_arrow_icon.svg'
import vSelect from 'vue-select'
import 'vue-select/dist/vue-select.css'

export default {
  name: 'MappingEdit',
  props: ['template', 'properties', 'asset'],
  emits: ['edit-handler'],

  components: {
    'v-select': vSelect
  },

  data() {
    return {
      data: {
        mapping: {
          map: {},
          form: {},
          properties: {}
        }
      },
      placeholders: {},
      mapping: this.template?.mapping,
      deleteIcon: DeleteIcon,
      rightArrowIcon: RightArrowIcon,
      showConfirmation: false,
      selectedOptions: {},
    }
  },

  created() {
    this.buildMapping()
  },
  methods: {
    buildMapping() {
      if (!this.mapping) {
        return
      }
      this.data.mapping = this.mapping
      this.properties.forEach(item => {
        let propKey = Object.keys(this.mapping.map).find(key => this.mapping.map[key] === item.name)
        if (propKey) {
          this.placeholders[propKey] = item.label
        }
      })
    },

    setSelected(input) {
      console.log(input)
    },

    isObject(variable) {
      return variable !== null && typeof variable === 'object'
    },

    handleInput(input) {
      // get form label
      if (input?.payload_label) {
        this.data.mapping.form[input.payload_key] = input.payload_label
      }

      if (input?.value != 'none') {
        this.data.mapping.map[input.payload_key] = input.value
      } else {
        delete this.data.mapping.map[input.payload_key]
      }
      this.data.mapping.properties[input.value] = input.property


    }
  }
}
</script>
<style>
button.close {
  font-size: 34px;
}

button.btn.btn-danger,
.btn.btn-secondary {
  font-size: 13px;
}

.error-modal {
  position: fixed !important;
  background: #222222b3;
}

.item-name button {
  border: 2px solid;
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.v-select:not(.vs--open) .vs__selected-options > .vs__selected ~ .vs__search {
  display: none;
}

.vs__dropdown-option {
  white-space: normal;
  border-bottom: 1px solid #d2d2d2;
  padding-top: 2rem;
  padding-bottom: 2rem;
  font-size: 2.5rem;
}

.vs__dropdown-option--highlight {
  background: #a9e3fe;
  color: #000;
}

.mapping-edit .custom-vue-select .vs__dropdown-toggle {
  border: 1px solid #000;
  border-radius: 0;
}
</style>
