<template>
  <div class="mapping_list pb-50">
    <div class="mapping_list-item header">
      <div class="mapping-item_left">
        <span class="item-name">Template Name</span>
      </div>
      <div class="mapping-item_left">
        <span class="item-name">Category</span>
      </div>
      <div class="mapping-item_left">
        <span class="item-name">Language</span>
      </div>
      <div class="mapping-item_left">
        <span class="item-name">Last Updated</span>
      </div>
      <div class="mapping-item_left">
        <!-- <span class="item-name">Last Updated</span> -->
      </div>
    </div>
    <div v-for="template in modifiedMappings" :key="template.id" class="mapping_list-item">
      <div class="mapping-item_left">
        <h4 class="item-name template-name" :title="template.name">{{ template.name }}</h4>
      </div>
      <div class="mapping-item_left">
        <h4 class="item-name">{{ template.category }}</h4>
      </div>
      <div class="mapping-item_left">
        <span class="item-name">{{ template.language }}</span>
      </div>
      <div class="mapping-item_left">
        <span class="item-name">{{ template.date }}</span>
      </div>
      <div class="mapping-item_left">
        <span class="item-name">
          <button class="btn btn-outline-primary btn-sm rounded-0" @click="$emit('set-handler', template.id)">
            Edit
          </button>
        </span>
      </div>
    </div>
    <!-- <div v-if="showConfirmation" class="error-modal">
      <transition name="vac-bounce">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">Confirm Delete</h5>
            </div>
            <div class="modal-body mb-5" style="font-size: 16px !important">
              Are you sure you want to delete this item?
            </div>
            <div class="modal-footer">
              <button
                type="button"
                class="btn btn-danger text-white"
                @click.stop="
                  $emit('delete-handler', id)
                  showConfirmation = false
                "
              >
                Yes, Delete
              </button>
              <button type="button" class="btn btn-secondary m-lg-2" @click="showConfirmation = false">Cancel</button>
            </div>
          </div>
        </div>
      </transition>
    </div> -->
  </div>
</template>

<script>
import { convertDate } from '@/utils/utils.js'
import DeleteIcon from '@/assets/icons/delete_icon.png'

export default {
  name: 'MappingList',
  props: ['templates'],
  emits: ['set-handler'],

  data() {
    return {
      id: null,
      deleteIcon: DeleteIcon,
      showConfirmation: false
    }
  },

  computed: {
    modifiedMappings() {
      return this.templates.map(el => ({
        ...el,
        date: el.updated_at ? convertDate(el.updated_at) : 'N/A'
      }))
    }
  },
  methods: {
    setId(newId) {
      this.id = newId
      this.showConfirmation = true
      console.log(`Id is now set to ${this.id}`)
    }
  }
}
</script>
<style>
button.close {
  font-size: 34px;
}

button.btn.btn-danger,
.btn.btn-secondary {
  font-size: 13px;
}

.error-modal {
  position: fixed !important;
  background: #222222b3;
}

.item-name button {
  border: 2px solid;
  padding-top: 1rem;
  padding-bottom: 1rem;
}
</style>
