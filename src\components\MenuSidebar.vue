<template>
  <div>
    <nav id="menu-sidebar" :class="{ inactive: !active }">
      <button @click="toggleMenuBar" class="sidebar-close">&times;</button>
      <div class="d-flex align-items-center h-100 w-100">
        <ul class="nav flex-column">
          <li v-for="route in filteredRoutes" :key="route.name" class="nav-item">
            <!-- For Inner Routes -->
            <router-link :class="{ disabled: !hasPermission(route.path) }" class="nav-link"
              v-if="route.name !== 'Support'" :to="{ name: route.path }">
              <img :src="route.icon" alt="icon" class="nav-icon" />
              {{ route.name }}
            </router-link>
            <!-- For External Link -->
            <a v-else class="nav-link" :href="route.path" target="_blank">
              <img :src="route.icon" alt="icon" />
              {{ route.name }}
              <img :src="externalIcon" alt="External" />
            </a>
            <img v-show="!hasPermission(route.path)" @mouseover="showTooltip(route.name)" @mouseleave="hideTooltip"
              class="lock-icon" :src="LockIcon" alt="Locked" />

            <!-- Tooltip -->
            <div v-if="!hasPermission(route.path) && currentTooltip === route.name" class="tooltip">
              Ask admin for access
            </div>
          </li>
        </ul>
      </div>
      <!-- <button class="beamerTrigger">
        <img :src="notificationIcon" alt="Notification Icon" />
      </button> -->
    </nav>
  </div>
</template>

<script>
import { mapMutations } from 'vuex'
import AccountIcon from '@/assets/icons/account_icon.png'
import ActivityIcon from '@/assets/icons/activities_icon.png'
import ExternalIcon from '@/assets/icons/external_icon.png'
import LabelIcon from '@/assets/icons/label_icon.png'
import QueueIcon from '@/assets/icons/queue_icon.png'
import NotificationIcon from '@/assets/icons/notification_icon.png'
import SettingIcon from '@/assets/icons/setting_icon.png'
import TemplateIcon from '@/assets/icons/template_icon.png'
import SupportIcon from '@/assets/icons/support_icon.png'
import ReportIcon from '@/assets/icons/report.svg'
import AutomationIcon from '@/assets/icons/automation.svg'
import mappingIcon from '@/assets/icons/mapping.svg'
import LockIcon from '@/assets/icons/lock_icon.svg'
import UserRoleIcon from '@/assets/icons/user_role.svg'

export default {
  name: 'MenuSidebar',

  data() {
    return {
      queueIcon: QueueIcon,
      externalIcon: ExternalIcon,
      notificationIcon: NotificationIcon,
      LockIcon: LockIcon,
      currentTooltip: null,
      portalId: null,
      staticPortalId: ['7222284', '********'],
    }
  },
  computed: {
    active() {
      return this.$store.state.menuBarShow
    },
    combinedPermissions() {
      const defaultPermissions = ['https://share.hsforms.com/1Is0DNg6XS8yM6PgC4VVVuA1mb0n', 'Account', 'InstaChats', 'Templates'];

      let permission = this.$store.state.routePermissions.permissions.map(permission => permission.replace(/\s+/g, ''))
      return this.$store.state.routePermissions.active ? [...new Set([...permission, ...defaultPermissions])] : defaultPermissions;
    },

    //  for specific portal testing on prod 
    filteredRoutes() {
      let baseRoutes = [
        { name: "Chats", path: "Chats", icon: ActivityIcon },
        { name: "Campaigns", path: "Campaigns", icon: AutomationIcon },
        { name: 'Templates', path: 'Templates', icon: TemplateIcon },
        { name: "Flow Mapping", path: "FlowMapping", icon: mappingIcon },
        { name: "Settings", path: "Settings", icon: SettingIcon },
        { name: "Account", path: "Account", icon: AccountIcon },
        { name: "Report", path: "Report", icon: ReportIcon },
        { name: "Users & Roles", path: "UserRoles", icon: UserRoleIcon },
        {
          name: "Support",
          path: "https://share.hsforms.com/1Is0DNg6XS8yM6PgC4VVVuA1mb0n",
          icon: SupportIcon,
        },
      ];

      if (this.staticPortalId?.includes(this.portalId)) {
        baseRoutes.splice(1, 0, { name: "Instagram", path: "InstaChats", icon: ActivityIcon });
      }

      return baseRoutes.filter(route => !(route.name === "Users & Roles" && !this.$store.state.userExists));
    }
  },
  methods: {
    ...mapMutations(['toggleMenuBar']),
    hasPermission(routeName) {
      if (this.$store.state.routePermissions.admin || !this.$store.state.userExists) {
        return true;
      }
      return this.combinedPermissions.includes(routeName);
    },
    showTooltip(routeName) {
      this.currentTooltip = routeName;
    },
    hideTooltip() {
      this.currentTooltip = null;
    },
  },
  mounted() {
    var urlString = window.location.href
    var url = new URL(urlString)
    this.portalId = url.searchParams.get('portal_id')
  }
}
</script>
