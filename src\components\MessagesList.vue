<template>
  <div class="messages-list">
    <div
      v-for="message in messages"
      :key="message.id"
      class="messages-list_item"
      @click="$emit('open-conversation', message.userId)"
    >
      <!-- info -->
      <div class="message-info">
        <div class="user-avatar">
          <img :src="message.photoURL" alt="Avatar" />
        </div>
        <div class="message-wrapper">
          <div class="username">{{ message.name }}</div>
          <div class="message">{{ message.text }}</div>
        </div>
      </div>

      <!-- meta fields -->
      <div class="message-meta">
        <div class="message-date">{{ `Last Tried: ${message.date}` }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import DeleteIcon from '@/assets/icons/delete_icon.png'

export default {
  name: 'MessagesList',
  props: ['messages'],
  emits: ['delete-handler', 'open-conversation'],
  data() {
    return {
      deleteIcon: DeleteIcon
    }
  }
}
</script>

<style></style>
