<template>
    <div class="pagination-div">
        <button @click="goToPage(currentPage, 'decrement')" class="arrow-btn" :disabled="currentPage === 1">
            <img :src="leftArrow" alt="left arrow">
        </button>
        <button v-for="pageNumber in visiblePages" :key="pageNumber" @click="goToPage(pageNumber, 'directPage')"
            :class='{ "page-number": true, "select-page": currentPage === pageNumber }' :disabled="currentPage === pageNumber">
            {{ pageNumber }}
        </button>
        <button @click="goToPage(currentPage, 'increment')" class="arrow-btn" :disabled="currentPage === totalPages">
            <img :src="rightrrow" class="right-arrow" alt="right arrow">
        </button>
    </div>
</template>

<script>
import LeftArrow from '@/assets/icons/pagination_arrow_left.svg';
import RightArrow from '@/assets/icons/pagination_arrow_right.svg';

export default {
    name: "Pagination",

    data() {
        return {
            leftArrow: LeftArrow,
            rightrrow: RightArrow,
            currentPage: this.selectedPage || 1,
            itemsPerPage: 25, // Number of items per page
        }
    },
    props: [
        'totalCampaigns',
        'fetchPageData',
        'selectedPage'
    ],
    computed: {
        totalPages() {
            return Math.ceil(this.totalCampaigns / this.itemsPerPage);
        },
        visiblePages() {
            let startPage = 1;
            let endPage = Math.min(this.totalPages, 9); // Show 9 buttons at most
            if (this.totalPages > 9 && this.currentPage > 5) {
                startPage = Math.max(1, this.currentPage - 4);
                endPage = Math.min(this.totalPages, this.currentPage + 4);
                if (endPage === this.totalPages) {
                    startPage = endPage - 8; // Ensure that the last 9 buttons are displayed
                }
            }
            return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);
        }
    },
    methods: {
        goToPage(pageNumber, state) {
            if (state === 'increment') {
                this.currentPage = pageNumber + 1;
            } else if (state === 'decrement') {
                this.currentPage = pageNumber - 1;
            } else {
                this.currentPage = pageNumber;
            }
            this.fetchPageData(this.currentPage)
        }
    }
}
</script>