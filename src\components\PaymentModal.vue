<template>
  <div v-show="show" class="payment-modal">
    <transition name="vac-bounce">
      <div v-if="show" class="payment-modal_content">
        <div class="payment-header">
          <button class="close-button" @click.prevent="toggle">&times;</button>
        </div>
      </div>
    </transition>
    <div class="payment-modal_overlay" @click.prevent="toggle" />
  </div>
</template>

<script>
export default {
  name: 'PaymentModal',
  props: {
    show: { type: Boolean },
    toggle: { type: Function, default: () => ({}) }
  }
}
</script>
