<template>
    <div v-if="tableData?.length || labels" class="row-1">
        <Loader v-if="loaderState" />
        <div v-else class="child-col-1">
            <div class="sub-child-col-1">
                <div class="grand-child-1" ></div>
                <div class="grand-child-2">
                    <span>{{ labels?.title ?? '-' }}</span>
                </div>
                <div class="grand-child-3">
                    <span>{{ labels?.subtitle ?? '-' }}</span>
                </div>
            </div>
            <div class="sub-child-col-2">
                <div class="grand-child-1">
                    <span>{{ labels?.name ?? '-' }}</span>
                    <!-- <span v-if="labels?.name === 'Campaign Name'">{{ labels?.contactlist ?? '-' }}</span> -->

                </div>
                <div class="grand-child-2">
                    <span v-if="labels?.name === 'Campaign Name'">{{ labels?.metrics?.sent ?? '-' }}</span>
                    <span v-if="labels?.name === 'Campaign Name'">{{ labels?.metrics?.delivered ?? '-' }}</span>
                    <span v-if="labels?.name === 'Campaign Name'">{{ labels?.metrics?.read ?? '-' }}</span>
                    <span v-if="labels?.name === 'Template Name'">{{ labels?.metrics?.click_rate ?? '-' }}</span>
                    <span v-if="labels?.name === 'Template Name'">{{ labels?.metrics?.reply_rate ?? '-' }}</span>
                    <span v-if="labels?.name === 'Template Name'">{{ labels?.metrics?.delivery_rate ?? '-' }}</span>
                    <span v-if="labels?.name === 'Workflow Name'">{{ labels?.metrics?.sent ?? '-' }}</span>
                    <span v-if="labels?.name === 'Workflow Name'">{{ labels?.metrics?.delivered ?? '-' }}</span>
                    <span v-if="labels?.name === 'Workflow Name'">{{ labels?.metrics?.read ?? '-' }}</span>
                </div>
                <div class="grand-child-3">
                    <span>{{ labels?.conversions?.opportunities ?? '-' }}</span>
                    <span>{{ labels?.conversions?.customers ?? '-' }}</span>
                    <span>{{ labels?.conversions?.deals ?? '-' }}</span>
                    <span>{{ labels?.conversions?.won ?? '-' }}</span>
                </div>
            </div>
        </div>
        <div class="child-col-2">
            <div v-for="(item, index) in tableData" :key="index" class="sub-child-col-1">
                <div class="grand-child-1">
                    <span 
                    :style="{
                        color: currentReportName === 'Workflow' ? 'blue' : 'inherit',
                        cursor: currentReportName === 'Workflow' ? 'pointer' : 'default'
                      }" 
                      :title="item?.name" v-if="labels?.name === 'Template Name' || labels?.name === 'Workflow Name'" 
                      @click.prevent="handleDownload(item)">{{ item?.name ?? '-' }}</span>
                    <span :title="item?.campaign_name || item?.name" v-if="labels?.name === 'Campaign Name'">{{ item?.campaign_name ?? item?.name }}</span>

                </div>

                <!-- Campaign Table -->
                <div v-if="labels?.name === 'Campaign Name'" class="grand-child-2">
                    <div>
                        <span>{{ item?.sent ?? '-' }}</span>
                    </div>
                    <div>
                        <span>{{ item?.deliveryRate ?? '-' }}%</span>
                        <span>{{ item?.delivered ?? '-' }}</span>
                    </div>
                    <div>
                        <span>{{ item?.readRate ?? '-' }}%</span>
                        <span>{{ item?.read ?? '-' }}</span>
                    </div>
                </div>

                <!-- Template Table -->
                <div v-else-if="labels?.name === 'Template Name'" class="grand-child-2">
                    <div>
                        <span>{{ item?.click_rate ?? '-' }}%</span>
                        <span>{{ item?.clicked ?? '-' }}</span>
                    </div>
                    <div>
                        <span>{{ item?.reply_rate ?? '-' }}%</span>
                        <span>{{ item?.sent ?? '-' }}</span>
                    </div>
                    <div>
                        <span>{{ item?.delivery_rate ?? '-' }}%</span>
                        <span>{{ item?.delivered ?? '-' }}</span>
                    </div>
                </div>

                <!-- Workflow Table -->
                <div v-else class="grand-child-2">
                    <div>
                        <span>{{ item?.sent ?? '-' }}</span>
                    </div>
                    <div>
                        <span>{{ item?.deliveryRate ?? '-' }}%</span>
                        <span>{{ item?.delivered ?? '-' }}</span>
                    </div>
                    <div>
                        <span>{{ item?.readRate ?? '-' }}%</span>
                        <span>{{ item?.read ?? '-' }}</span>
                    </div>
                </div>

                <!-- Conversions Section (Same for Both) -->
                <div class="grand-child-3">
                    <span>{{ item?.opportunities ?? '-' }}</span>
                    <span>{{ item?.customers ?? '-' }}</span>
                    <span>{{ item?.deals ?? '-' }}</span>
                    <span>{{ item?.dealsWon ?? '-' }}</span>
                </div>
            </div>
            <DataHandler v-if="!tableData?.length"/>

       </div>
    </div>
</template>

<script>
import Loader from '@/components/ReportLoader.vue';
import DataHandler from '@/components/DataNotFound'


export default {
    name: "ReportTable",
    components: { Loader,DataHandler
    },
    props: {
        tableData: {
            type: Array,
            default: () => []
        },
        labels: {
            type: Object,
            default: () => ({})
        },
        loaderState: {
            type: Boolean,
            default: false
    },
    currentReportName: {
      type: String,
      required: true,
    },
  },
  methods: {
 
    handleDownload(item) {

        if ( this.currentReportName !== 'Workflow') return;

        const url = `${process.env.VUE_APP_API_URL}workflow/export?user_id=${this.$store.state.userData.user_id}&id=${item?.id}`;
        const fileName = `${item?.name || 'workflow'}.csv`;

        // Create and trigger a hidden download link
        const anchor = document.createElement('a');
        anchor.href = url;
        anchor.setAttribute('download', fileName);
        anchor.style.display = 'none';
        document.body.appendChild(anchor);
        anchor.click();
        document.body.removeChild(anchor);
        },
  },};
</script>

<style>
.campaign-performance .row-1 .child-col-2 .sub-child-col-1 .grand-child-1 {
    word-break: break-all;
    overflow-wrap: break-word;
    hyphens: auto;
    padding: 5px 10px;
    line-height: 1.2;
    max-height: 100%;
}

.no-data-report {
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2.5rem;
    color: #000;
    font-weight: 700;
}
</style>
