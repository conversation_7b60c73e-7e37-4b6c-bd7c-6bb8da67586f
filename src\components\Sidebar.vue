<template>
  <div class="app-sidebar" :class="{ inactive: !show }">
    <div class="app-sidebar_header">
      <div class="heading-wrapper">
        <p class="heading">{{ heading }}</p>
        <button @click.prevent="$emit('close')" class="sidebar-close">&times;</button>
      </div>
    </div>
    <div class="app-sidebar_content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Sidebar',
  props: ['show', 'heading'],
  emits: ['close']
}
</script>
