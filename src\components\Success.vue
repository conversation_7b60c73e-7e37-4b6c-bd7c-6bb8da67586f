<template>
  <div class="success-container">
    {{ successMessage ? successMessage : '&nbsp;' }}
    <span @click="$emit('close')">&times;</span>
  </div>
</template>

<script>
export default {
  name: 'SuccessComponent',
  props: ['successMessage', 'close'],
  emits: ['close']
}
</script>

<style lang="scss" scoped>
.success-container {
  font-size: 16px;
  background: #bbffbb;
  padding: 10px;
  margin-bottom: 10px;
  border-left: 3px solid #6aeb32;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  font-weight: 500;

  span {
    cursor: pointer;
    font-size: 5rem;
    top: 0;
    right: 10px;
    position: absolute;
  }
}
</style>
