<template>
  <div class="template-dynamic-button">
    <!-- Radio Buttons -->
    <div class="d-flex gap-5 mt-4">
      <label><input type="radio" v-model="selectedOption" value="none" @change="resetButtons"
          :disabled="this.selectedCategory === 'AUTHENTICATION'" /> None</label>
      <label><input type="radio" v-model="selectedOption" value="cta" @change="resetButtons"
          :disabled="this.selectedCategory === 'AUTHENTICATION'" /> Call to Actions</label>
      <label><input type="radio" v-model="selectedOption" value="quick" @change="resetButtons"
          :disabled="this.selectedCategory === 'AUTHENTICATION'" /> Quick Replies</label>
      <label><input type="radio" v-model="selectedOption" value="all" @change="resetButtons" /> All</label>
    </div>
    <!-- Call to action -->
    <div v-if="selectedOption === 'cta'" class="++">
      <div v-for="(btn, index) in ctaButtons" :key="index" class="d-flex align-items-center gap-2">
        <p class="m-0">Call to Action {{ index + 1 }} :</p>
        <div class="d-flex --">
          <div class="d-flex align-items-center gap-2 justify-content-between">
            <select v-model="btn.type" @change="resetSingleCTA(index)" name="cta type" class="btn-input w-50">
              <option value="" disabled>Select action type</option>
              <option v-if="!selectedTypes.includes('URL') || btn.type === 'URL'" value="URL">URL</option>
              <option v-if="!selectedTypes.includes('PHONE_NUMBER') || btn.type === 'PHONE_NUMBER'"
                value="PHONE_NUMBER">Phone</option>
            </select>
            <input v-model="btn.text" placeholder="Button Title" class="btn-input w-50" />
          </div>
          <div class="d-flex align-items-center gap-2 justify-content-between">
            <select v-model="selectedCountryCode" @change="updateCtaPhoneNumber(index)"
              v-if="getCallToActionButtonKey(btn.type) === 'phone_number'" name="country" id="country"
              class="btn-input w-50 country-dropdown">
              <option value="" disabled selected>Select a country code</option>
              <option v-for="(country, index) in countryCode" :key="index" :value="country.dialCode">
                {{ country.name }} {{ country.dialCode }}
              </option>
            </select>
            <input v-model="btn[getCallToActionButtonKey(btn.type)]" placeholder="Button value" maxlength="2000"
              class="btn-input w-75" />
            <button @click="removeCTA(index)" type="button" class="remove-btn"><img :src="closeIcon"
                alt="close icon"></button>
          </div>
        </div>
      </div>
      <button @click="addCTA" type="button" :disabled="ctaButtons.length >= 2" class="create-dynamic-btn">+ Add
        Button</button>
    </div>

    <!-- Quick reply -->
    <div v-if="selectedOption === 'quick'">
      <div v-for="(btn, index) in quickReplies" class="d-flex align-items-center gap-2" :key="index">
        <p class="m-0">Quick Reply {{ index + 1 }} :</p>
        <input v-model="btn.text" placeholder="Button Title" maxlength="25" class="btn-input" />
        <button @click="removeQuickReply(index)" type="button" class="remove-btn"><img :src="closeIcon"
            alt="close icon"></button>
      </div>
      <button @click="addQuickReply" type="button" :disabled="quickReplies.length >= 3" class="create-dynamic-btn">+
        Quick
        Reply</button>
    </div>

    <div v-if="selectedOption === 'all'">
      <div v-for="(btn, index) in quickReplies" class="d-flex align-items-center" :key="'quick-' + index">
        <p class="m-0">Quick Reply {{ index + 1 }} :</p>
        <input v-model="btn.text" placeholder="Quick Reply Title" maxlength="25" class="btn-input" />
        <button @click="removeQuickReply(index)" type="button" class="remove-btn"><img :src="closeIcon"
            alt="close icon"></button>
      </div>

      <div v-for="(btn, index) in urlButtons" class="d-flex align-items-center" :key="'url-' + index">
        <p class="m-0">Call to Action {{ index + 1 }} :</p>
        <input v-model="btn.text" placeholder="URL Title" maxlength="25" class="btn-input" />
        <input v-model="btn.url" placeholder="URL Value" class="btn-input" />
        <button @click="removeURL(index)" type="button" class="remove-btn"><img :src="closeIcon"
            alt="close icon"></button>
      </div>

      <div v-if="phoneButton" class="d-flex align-items-center">
        <p class="m-0">Call to Action {{ this.urlButtons.length + 1 }} :</p>
        <input v-model="phoneButton.text" placeholder="Phone Title" maxlength="25" class="btn-input" />
        <select v-model="selectedCountryCode" @change="updatePhoneNumber" name="country" id="country"
          class="btn-input w-50 country-dropdown">

          <option value="" disabled selected>Select a country code</option>
          <option v-for="(country, index) in countryCode" :key="index" :value="country.dialCode">
            {{ country.name }} {{ country.dialCode }}
          </option>
        </select>
        <input v-model="phoneButton.phone_number" placeholder="Phone Number" type="text" class="btn-input" />
        <button @click="removePhoneNumber" type="button" class="remove-btn"><img :src="closeIcon"
            alt="close icon"></button>
      </div>

      <div v-if="copyCodeButton" class="d-flex align-items-center">
        <p class="m-0">Copy Code : </p>
        <input placeholder="Button title" value="Copy Code" class="btn-input" readonly />
        <input v-model="otpValue" placeholder="Button value" maxlength="15" class="btn-input" />
        <button @click="removeCopyCode" type="button" class="remove-btn"><img :src="closeIcon"
            alt="close icon"></button>
      </div>

      <button @click="addQuickReply" type="button"
        :disabled="totalButtons >= 10 || this.selectedCategory === 'AUTHENTICATION'" class="create-dynamic-btn">+ Quick
        Reply ({{
          quickReplies.length }}/{{
          maxQuickReplies }})</button>
      <button @click="addURL" type="button"
        :disabled="urlButtons.length >= 2 || totalButtons >= 10 || this.selectedCategory === 'AUTHENTICATION'"
        class="create-dynamic-btn">+ URL
        ({{ urlButtons.length
        }}/2)</button>
      <button @click="addPhoneNumber" type="button"
        :disabled="phoneButton !== null || totalButtons >= 10 || this.selectedCategory === 'AUTHENTICATION'"
        class="create-dynamic-btn">+ Phone Number ({{
          phoneButton ? 1 : 0 }}/1)</button>
      <button @click="addCopyCode" class="create-dynamic-btn" type="button"
        :disabled="this.copyCodeButton !== null || totalButtons >= 10 || this.selectedCategory === 'UTILITY'">+ Copy
        Code</button>

    </div>
  </div>
</template>

<script>
import CloseIcon from "@/assets/icons/close-icon.svg";
import CountryCode from "../vuechat/utils/country.json";

export default {
  name: "TemplateDynamicButton",

  data() {
    return {
      closeIcon: CloseIcon,
      countryCode: CountryCode,
      selectedOption: "none",
      ctaButtons: [],
      quickReplies: [],
      urlButtons: [],
      phoneButton: null,
      copyCodeButton: null,
      maxQuickReplies: 10,
      otpValue: "",
      selectedCountryCode: "",
    };
  },
  props: {
    selectedCategory: {
      type: String,
      default: ""
    },
    initialButtons: {
      type: Array,
      default: () => []
    }
  },

  created() {
    // Initialize buttons if provided
    if (this.initialButtons && this.initialButtons.length > 0) {
      this.initializeButtonsFromProps();
    }
  },

  watch: {
    selectedCategory: {
      handler(newVal) {
        if (newVal === 'AUTHENTICATION') {
          // Save current buttons before resetting
          const currentButtons = [...this.quickReplies, ...this.urlButtons];
          if (this.phoneButton) currentButtons.push(this.phoneButton);
          if (this.copyCodeButton) currentButtons.push(this.copyCodeButton);
          
          this.selectedOption = 'all';
          this.addCopyCode();
          
          // Restore non-authentication buttons after adding copy code
          this.$nextTick(() => {
            if (currentButtons.length > 0) {
              currentButtons.forEach(button => {
                if (button.type !== 'OTP') {
                  switch (button.type) {
                    case 'QUICK_REPLY':
                      this.quickReplies.push(button);
                      break;
                    case 'URL':
                      this.urlButtons.push(button);
                      break;
                    case 'PHONE_NUMBER':
                      this.phoneButton = button;
                      break;
                  }
                }
              });
            }
          });
        }
      }
    },
    ctaButtons: {
      handler() {
        this.emitButtonData();
      },
      deep: true
    },
    quickReplies: {
      handler() {
        this.emitButtonData();
      },
      deep: true
    },
    urlButtons: {
      handler() {
        this.emitButtonData();
      },
      deep: true
    },
    phoneButton: {
      handler() {
        this.emitButtonData();
      },
      deep: true
    },
    copyCodeButton: {
      handler() {
        this.emitButtonData();
      },
      deep: true
    },
    editData: {
      immediate: true,
      handler(newData) {
        if (newData?.components) {
          const buttonsComponent = newData.components.find(c => c.type === 'BUTTONS');
          if (buttonsComponent?.buttons) {
            this.setPrefilledButtons(buttonsComponent.buttons);
          }
        }
      }
    }
  },
  computed: {
    totalButtons() {
      return this.quickReplies.length + this.urlButtons.length + (this.phoneButton ? 1 : 0) + (this.copyCodeButton ? 1 : 0);
    },

    selectedTypes() {
      return this.ctaButtons.map(btn => btn.type).filter(type => type !== '');
    }

  },
  methods: {
    initializeButtonsFromProps() {
      const hasQuickReply = this.initialButtons.some(btn => btn.type === 'QUICK_REPLY');
      const hasCTA = this.initialButtons.some(btn => ['URL', 'PHONE_NUMBER'].includes(btn.type));
      
      // Set the correct option based on button types
      if (hasQuickReply && hasCTA) {
        this.selectedOption = 'all';
      } else if (hasQuickReply) {
        this.selectedOption = 'quick';
      } else if (hasCTA) {
        this.selectedOption = 'cta';
      }

      // Initialize each button type
      this.initialButtons.forEach(button => {
        const buttonCopy = { ...button };
        switch (button.type) {
          case 'QUICK_REPLY':
            this.quickReplies.push(buttonCopy);
            break;
          case 'URL':
            if (this.selectedOption === 'cta') {
              this.ctaButtons.push({ 
                type: 'URL',
                text: buttonCopy.text,
                url: buttonCopy.url
              });
            } else {
              this.urlButtons.push(buttonCopy);
            }
            break;
          case 'PHONE_NUMBER':
            if (this.selectedOption === 'cta') {
              this.ctaButtons.push({
                type: 'PHONE_NUMBER',
                text: buttonCopy.text,
                phone_number: buttonCopy.phone_number
              });
            } else if (!this.phoneButton) {
              this.phoneButton = buttonCopy;
              // Extract country code from phone number
              if (buttonCopy.phone_number) {
                const countryCode = this.countryCode.find(c => 
                  buttonCopy.phone_number.startsWith(c.dialCode)
                );
                if (countryCode) {
                  this.selectedCountryCode = countryCode.dialCode;
                }
              }
            }
            break;
          case 'OTP':
            if (!this.copyCodeButton) {
              this.copyCodeButton = buttonCopy;
            }
            break;
        }
      });
      
      this.emitButtonData();
    },

    updatePhoneNumber() {
      if (!this.phoneButton.phone_number?.startsWith(this.selectedCountryCode)) { this.phoneButton.phone_number = this.selectedCountryCode; }
    },

    updateCtaPhoneNumber(index) {
      const button = this.ctaButtons[index];
      if (button?.type === "PHONE_NUMBER") {
        console.log("inside first if");

        if (!button.phone_number?.startsWith(this.selectedCountryCode)) {
          this.ctaButtons[index].phone_number = '';
          this.$set(this.ctaButtons, index, {
            ...button,
            phone_number: this.selectedCountryCode + (button.phone_number || ""),
          });
        }
      }
    },

    getCallToActionButtonKey(type) {
      return type === "URL" ? "url" : type === "PHONE_NUMBER" ? "phone_number" : "url";
    },

    emitButtonData() {
      const buttonData = [
        ...this.ctaButtons,
        ...this.quickReplies,
        ...this.urlButtons,
        this.phoneButton,
        this.copyCodeButton
      ].filter(Boolean);

      this.$emit("update-buttons", buttonData);
    },

    resetButtons() {
      this.ctaButtons = [];
      this.quickReplies = [];
      this.urlButtons = [];
      this.phoneButton = null;
      this.copyCodeButton = null;
      this.selectedCountryCode = '';
      this.emitButtonData();
    },
    addCTA() {
      if (this.ctaButtons.length < 2) {
        const newButton = { type: "" };
        if (newButton.type === 'phone_number') {
          newButton.phone_number = '';
        }
        this.ctaButtons.push(newButton);
        this.emitButtonData();
      }
    },

    resetSingleCTA(index) {      
      if (this.ctaButtons.length === 1) {
        this.$set(this.ctaButtons, index, { type: this.ctaButtons[index].type });
      }
    },


    removeCTA(index) {
      this.selectedCountryCode = '';
      this.ctaButtons.splice(index, 1);
      this.emitButtonData();
    },
    addQuickReply() {
      if (this.quickReplies.length < (this.selectedOption === "all" ? this.maxQuickReplies : 3)) {
        this.quickReplies.push({ text: '', type: 'QUICK_REPLY' });
        this.emitButtonData();
      }
    },
    removeQuickReply(index) {
      this.quickReplies.splice(index, 1);
      this.emitButtonData();
    },
    addURL() {
      if (this.urlButtons.length < 2) {
        this.urlButtons.push({ type: "URL", text: "", url: "" });
        this.emitButtonData();
      }
    },
    removeURL(index) {
      this.urlButtons.splice(index, 1);
      this.emitButtonData();
    },
    addPhoneNumber() {
      if (!this.phoneButton) {
        this.phoneButton = { type: "PHONE_NUMBER", phone_number: '' };
        this.emitButtonData();
      }
    },
    removePhoneNumber() {
      this.phoneButton = null;
      this.emitButtonData();
    },
    addCopyCode() {
      if (!this.copyCodeButton) {
        this.copyCodeButton = {
          "type": "OTP",
          "otp_type": "COPY_CODE",
          "text": "Copy Code"
        };
        this.emitButtonData();
      }
    },
    removeCopyCode() {
      this.copyCodeButton = null;
      this.emitButtonData();
    },
    getButtonModel(btn) {
      return btn.type === 'URL' ? 'btn.url' : 'btn.value';
    },

    setPrefilledButtons(buttons) {
      if (!buttons || !buttons.length) return;

      buttons.forEach(button => {
        switch (button.type) {
          case 'QUICK_REPLY':
            this.quickReplies.push({ ...button });
            break;
          case 'URL':
            this.urlButtons.push({ ...button });
            break;
          case 'PHONE_NUMBER':
            if (!this.phoneButton) {
              this.phoneButton = { ...button };
              if (button.phone_number) {
                // Extract country code from phone number
                const countryCode = this.countryCode.find(c => 
                  button.phone_number.startsWith(c.dialCode)
                );
                if (countryCode) {
                  this.selectedCountryCode = countryCode.dialCode;
                }
              }
            }
            break;
          case 'OTP':
            if (!this.copyCodeButton) {
              this.copyCodeButton = { ...button };
            }
            break;
        }
      });
      this.emitButtonData();
    }
  },
};
</script>

<style lang="scss">
.template-dynamic-button {

  .country-dropdown {
    max-width: 27rem;
  }

  button {
    margin: 5px;
    padding: 5px;
  }

  input,
  select {
    margin: 5px;
    padding: 5px;
    border: 1px solid #ccc;
    outline: none;
  }

  .create-dynamic-btn {
    background: #34B7F14D;
    border: 1px solid #34B7F1;
    border-radius: 4px;
    color: #2C3F51;
    font-size: 2rem;
    font-weight: 500;

    &:disabled {
      cursor: not-allowed !important;
      background: #96d5f14d !important;
    }
  }

  .btn-input {
    background: #FCFBFB;
    border: 1px solid #D2D2D2;
    border-radius: 4px;
    font-size: 2rem;
    font-weight: 400;
  }

  .remove-btn {
    all: unset;
    margin-left: 1rem;
    padding: 1rem;
    cursor: pointer;
  }
}
</style>