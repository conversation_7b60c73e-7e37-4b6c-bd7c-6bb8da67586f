<template>
  <div class="preview-modal-overlay" v-if="visible">
    <div class="preview-modal">
      <div class="template-preview">
        <div class="preview">
          <div class="preview-header">
            <h3>{{ template.name }}</h3> <!-- Dynamically display the template name -->
            <button class="btn btn-primary close-btn" @click="close">X</button></div>
          <div class="preview-box">
            <div class="preview-content">
              <div class="preview-img-div">
                <img v-if="template.templateType === null" :src="defaultPre" alt="default Pre">
                <img v-if="template.templateType === 'IMAGE'" :src="preImg" alt="preview img">
                <img v-if="template.templateType === 'VIDEO'" :src="preVideo" alt="preview video">
                <img v-if="template.templateType === 'LOCATION'" :src="preLocation" alt="preview location">
                <img v-if="template.templateType === 'DOCUMENT'" :src="preDoc" alt="preview location">
              </div>
              <p v-if="template.header" class="header">{{ template.header }}</p>
              <p v-html="template.body" class="format-content"></p>
              <span class="footer">{{ template.footer }}</span>
              <div v-for="(button, index) in template.buttons" :key="index" class="preview-btns">
                <button v-if="button.text">{{ button.text }}</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    
    </div>
  </div>
</template>

<script>
import PreImg from '../vuechat/components/SvgIcon/pre-img.svg';
import PreLocation from '../vuechat/components/SvgIcon/pre-location.svg';
import PreVideo from '../vuechat/components/SvgIcon/pre-video.svg';
import PreDoc from '../vuechat/components/SvgIcon/pre-doc.svg';
import DefaultPre from '../vuechat/components/SvgIcon/default-pre.svg';

export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    template: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
        defaultPre: DefaultPre,
        preImg: PreImg,
        preVideo: PreVideo,
        preLocation: PreLocation,
        preDoc: PreDoc,
        selectedType: null, 
    };
  },
  methods: {
    close() {
      this.$emit("close");
    },
    
  },
};
</script>

<style scoped>
.preview-modal-overlay {
  position: absolute;
  top: 200px;
  left: 37%;
  transform: translateX(10px); 
  background: none; 
  z-index: 1000;
}

.preview-modal {
  background: #fff;
  border-radius: 8px;
  width: 450px;
  max-width: 90%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid #ddd;
  max-height: 450px;
  overflow: scroll;
}

.preview-modal-overlay .preview {
    height: 100%;
    min-height: 35rem;
    max-height: 90%;
}

.preview-modal-overlay .preview .preview-header {
    background-color: #2c3f51;
    padding: 3rem;
    display: flex    ;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}

.preview-modal-overlay .preview .preview-header h3 {
    color: #fff;
    font-weight: 600;
    margin: 0;
    word-break: break-word;
}

.preview-modal-overlay .preview .preview-box {
    display: flex;
    flex-direction: column;
    max-height: 70%;
    min-height: 30rem;
    background-size: cover !important;
    background-size: contain;
    background: #fefefe;
    border: 1px dashed #ccc;
    padding: 0 2rem;
    color: #666;
}

.preview-modal-overlay .preview .preview-box .preview-content {
    background-color: #fff;
    border-radius: 1.5rem;
    overflow: auto;
    width: 100%;
    padding-block: 2rem 1rem;
    min-height: 12rem;
    max-height: 100%;
}

.preview-modal-overlay .preview .preview-box .preview-content .preview-img-div {
    width: 90%;
    margin-inline: auto;
    margin-bottom: 1rem;
}

.preview-modal-overlay .preview .preview-box .preview-content img {
    width: 100%;
    height: auto;
}

.preview-modal-overlay .preview-content .header{padding:0 2rem; margin-bottom: 3rem !important;font-weight: 600;}

.preview-modal-overlay .preview .preview-box .preview-content .format-content {
    font-size: 2.5rem !important;
    word-wrap: break-word;
    padding:0 2rem;
}

.preview-modal-overlay .preview .preview-box .preview-content span {
    font-size: 1.8rem;
    color: #9a9797;
    padding: 0rem 1rem;
    padding-inline: 2rem;
    word-wrap: break-word;
    margin-bottom: 1rem;
}

.preview-modal-overlay .preview-btns {
    display: flex;
    justify-content: center;
}

/* .preview-modal-overlay .preview-btns button {
  background: #34b7f1;
  color: #fff;
  border: none;
  padding: 10px 18px;
  border-radius: 4px;
  margin: 5px;
  cursor: pointer;
  font-size: 12px;
  width: max-content;
  display: flex;
  justify-content: center;
} */

.preview-modal-overlay .preview-btns button {
    all: unset;
    background-color: #fff;
    font-size: 2.5rem;
    font-weight: 500;
    color: #34b7f1;
    width: 100%;
    padding: 1rem 0;
    text-align: center;
    border-top: 1px solid rgba(0, 0, 0, 0.1019607843);
    cursor: pointer;
}


.preview-modal-overlay button.btn.btn-primary.close-btn{ font-size:13px; border-radius: 10px; font-weight:500;}

.preview-box {
    background-image: url('../vuechat/components/PngIcons/preview.jpg') !important;
  }
</style>