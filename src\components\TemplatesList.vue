<template>
  <dl class="accordion box" role="presentation">
    <templates-list-item
      v-for="item in data"
      :item="item"
      :groupId="groupId"
      :key="item.id"
      @delete-handler="deleteHandler"
      @update-handler="updateHandler"
    >
    </templates-list-item>
  </dl>
</template>

<script>
import TemplatesListItem from './TemplatesListItem'
export default {
  name: 'TemplatesList',
  props: ['data'],
  components: { TemplatesListItem },

  data() {
    return {
      groupId: null
    }
  },

  mounted() {
    this.groupId = this.$el.id
  },

  methods: {
    deleteHandler(id) {
      this.$emit('delete-handler', id)
    },
    updateHandler(id) {
      this.$emit('update-handler', id)
    }
  }
}
</script>

<style></style>
