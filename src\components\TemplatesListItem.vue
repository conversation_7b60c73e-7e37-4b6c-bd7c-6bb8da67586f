<template>
  <div :id="groupId + '-' + item.id" class="accordion-item" :class="{ 'is-active': item.active }">
    <dt class="accordion-item-title">
      <button @click="toggle" class="accordion-item-trigger">
        <h4 class="accordion-item-title-text">{{ item.name }}</h4>
        <div class="accordion-heading-right">
          <span class="title-date" :class="{ 'd-none': item.active }">{{ item.created_at }}</span>
          <img
            :src="deleteIcon"
            alt="Delete Icon"
            class="delete-icon"
            :class="{
              'd-none': item.active
            }"
            @click.stop="showConfirmation = true"
          />
        </div>
      </button>
      <div v-if="showConfirmation" class="error-modal">
        <transition name="vac-bounce">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
              </div>
              <div class="modal-body mb-5" style="font-size: 16px">Are you sure you want to delete this item?</div>
              <div class="modal-footer">
                <button
                  type="button"
                  class="btn btn-danger text-white"
                  @click.stop="
                    $emit('delete-handler', item.id)
                    showConfirmation = false
                  "
                >
                  Yes, Delete
                </button>
                <button type="button" class="btn btn-secondary m-lg-2" @click="showConfirmation = false">Cancel</button>
              </div>
            </div>
          </div>
        </transition>
      </div>
    </dt>
    <transition
      name="accordion-item"
      @enter="startTransition"
      @after-enter="endTransition"
      @before-leave="startTransition"
      @after-leave="endTransition"
    >
      <dd v-if="item.active" class="accordion-item-details">
        <div class="content">
          <p v-html="item.pattern" class="accordion-item-details-inner"></p>
          <button class="btn btn-primary" @click.stop="$emit('update-handler', item.id)">Edit</button>
        </div>
      </dd>
    </transition>
  </div>
</template>

<script>
import DeleteIcon from '@/assets/icons/delete_icon.png'
export default {
  name: 'TemplatesListItem',

  props: ['item', 'groupId'],

  data() {
    return {
      deleteIcon: DeleteIcon,
      showConfirmation: false
    }
  },

  emits: ['delete-handler', 'update-handler'],

  methods: {
    toggle(event) {
      this.$parent.$children.forEach(item => {
        if (item.$el.id === event.currentTarget.parentElement.parentElement.id) item.item.active = !item.item.active
        else item.item.active = false
      })
    },

    startTransition(el) {
      el.style.height = el.scrollHeight + 'px'
    },

    endTransition(el) {
      el.style.height = ''
    }
  }
}
</script>
<style>
button.close {
  font-size: 34px;
}

button.btn.btn-danger,
.btn.btn-secondary {
  font-size: 13px;
}

.error-modal {
  position: fixed !important;
  background: #222222b3;
}

.modal-body {
  font-weight: normal;
}
</style>
