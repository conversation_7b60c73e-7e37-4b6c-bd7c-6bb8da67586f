<template>
  <div class="upgrade-prompt" :class="{ 'overlay-mode': overlayMode }">
    <div class="upgrade-content">
      <div class="upgrade-icon">
        <img :src="lockIcon" alt="Locked Feature" />
      </div>
      
      <div class="upgrade-text">
        <h3>{{ title }}</h3>
        <p>{{ description }}</p>
        
        <div class="feature-list" v-if="features && features.length">
          <h4>Available in {{ targetPlan }} plan:</h4>
          <ul>
            <li v-for="feature in features" :key="feature">
              <img :src="checkIcon" alt="Check" />
              {{ feature }}
            </li>
          </ul>
        </div>
      </div>
      
      <div class="upgrade-actions">
        <button class="btn btn-primary upgrade-btn" @click="handleUpgrade">
          Upgrade to {{ targetPlan }}
        </button>
        <button v-if="!overlayMode" class="btn btn-secondary" @click="$emit('close')">
          Maybe Later
        </button>
      </div>
    </div>
    
    <div v-if="overlayMode" class="overlay-backdrop" @click="$emit('close')"></div>
  </div>
</template>

<script>
import LockIcon from '@/assets/icons/lock_icon.svg'
import CheckIcon from '@/assets/icons/info.svg'

export default {
  name: 'UpgradePrompt',
  
  props: {
    title: {
      type: String,
      default: 'Premium Feature'
    },
    description: {
      type: String,
      default: 'This feature is available in higher plans.'
    },
    features: {
      type: Array,
      default: () => []
    },
    targetPlan: {
      type: String,
      default: 'Professional'
    },
    overlayMode: {
      type: Boolean,
      default: false
    },
    upgradeUrl: {
      type: String,
      default: '#'
    }
  },
  
  data() {
    return {
      lockIcon: LockIcon,
      checkIcon: CheckIcon
    }
  },
  
  methods: {
    handleUpgrade() {
      if (this.upgradeUrl && this.upgradeUrl !== '#') {
        window.open(this.upgradeUrl, '_blank')
      }
      this.$emit('upgrade-clicked')
    }
  }
}
</script>

<style scoped>
.upgrade-prompt {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  margin: 1rem 0;
}

.upgrade-prompt.overlay-mode {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  padding: 2rem;
}

.overlay-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.upgrade-content {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 500px;
  width: 100%;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.upgrade-icon {
  margin-bottom: 1rem;
}

.upgrade-icon img {
  width: 48px;
  height: 48px;
  opacity: 0.6;
}

.upgrade-text h3 {
  color: #495057;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.upgrade-text p {
  color: #6c757d;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.feature-list {
  text-align: left;
  margin-bottom: 1.5rem;
}

.feature-list h4 {
  color: #495057;
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.feature-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  color: #495057;
}

.feature-list li img {
  width: 16px;
  height: 16px;
  margin-right: 0.5rem;
  filter: hue-rotate(120deg);
}

.upgrade-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.upgrade-btn {
  background: linear-gradient(135deg, #007bff, #0056b3);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upgrade-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
  background: #6c757d;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: #5a6268;
}

@media (max-width: 768px) {
  .upgrade-prompt.overlay-mode {
    padding: 1rem;
  }
  
  .upgrade-content {
    padding: 1.5rem;
  }
  
  .upgrade-actions {
    flex-direction: column;
  }
}
</style>
