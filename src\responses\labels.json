{"status": "success", "data": [{"id": 2, "name": "<PERSON><PERSON><PERSON>", "color": "#d38919", "dialogId": "2720327.1", "created_at": "2020-07-05 12:36:07"}, {"id": 3, "name": "Pooja", "color": "#eff4a1", "dialogId": "2720327.1", "created_at": "2020-07-05 12:36:16"}, {"id": 4, "name": "<PERSON><PERSON>", "color": "#5f02d1", "dialogId": "2720327.1", "created_at": "2020-07-05 12:36:28"}, {"id": 14, "name": "Trial", "color": "#a1b6ea", "dialogId": "2720327.1", "created_at": "2020-07-06 10:57:20"}, {"id": 15, "name": "Customer", "color": "#beea59", "dialogId": "2720327.1", "created_at": "2020-07-06 10:57:24"}, {"id": 16, "name": "Legacy Plan", "color": "#cbf72c", "dialogId": "2720327.1", "created_at": "2020-07-06 10:57:34"}, {"id": 17, "name": "New Plan", "color": "#218206", "dialogId": "2720327.1", "created_at": "2020-07-06 10:57:40"}, {"id": 124, "name": "Spanish", "color": "#55c62b", "dialogId": "2720327.1", "created_at": "2020-07-16 08:02:12"}, {"id": 125, "name": "Portugese", "color": "#c1710f", "dialogId": "2720327.1", "created_at": "2020-07-16 13:53:27"}, {"id": 163, "name": "Starter plan", "color": "#f9b8b3", "dialogId": "2720327.1", "created_at": "2020-07-24 05:50:34"}, {"id": 164, "name": "Professional plan", "color": "#839bea", "dialogId": "2720327.1", "created_at": "2020-07-24 05:50:41"}, {"id": 165, "name": "API plan", "color": "#2a69dd", "dialogId": "2720327.1", "created_at": "2020-07-24 05:50:47"}, {"id": 184, "name": "CSM Open Chats", "color": "#b431d8", "dialogId": "2720327.1", "created_at": "2020-07-29 05:09:34"}, {"id": 245, "name": "<PERSON>ed <PERSON><PERSON> for DP Time", "color": "#b6e2f9", "dialogId": "2720327.1", "created_at": "2020-08-13 05:04:41"}, {"id": 338, "name": "<PERSON><PERSON><PERSON>", "color": "#62fc4e", "dialogId": "2720327.1", "created_at": "2020-09-10 14:21:22"}, {"id": 869, "name": "new student", "color": "#a1ea7c", "dialogId": "2720327.1", "created_at": "2021-02-19 12:15:09"}, {"id": 1059, "name": "Product a", "color": "#a3f7e7", "dialogId": "2720327.1", "created_at": "2021-04-23 09:36:51"}, {"id": 1081, "name": "Course A", "color": "#f9f7a4", "dialogId": "2720327.1", "created_at": "2021-05-05 08:00:25"}, {"id": 1246, "name": "<PERSON>", "color": "#4d50f9", "dialogId": "2720327.1", "created_at": "2021-07-02 11:17:27"}, {"id": 1247, "name": "Hair transplant", "color": "#f46ea8", "dialogId": "2720327.1", "created_at": "2021-07-02 11:17:37"}, {"id": 1733, "name": "agents", "color": "#ce7056", "dialogId": "2720327.1", "created_at": "2021-11-18 10:39:36"}, {"id": 1734, "name": "Payments Pending", "color": "#88f798", "dialogId": "2720327.1", "created_at": "2021-11-19 09:07:02"}, {"id": 1738, "name": "Finance", "color": "#c9346d", "dialogId": "2720327.1", "created_at": "2021-11-23 13:40:46"}, {"id": 1751, "name": "Bill pending", "color": "#96c6e8", "dialogId": "2720327.1", "created_at": "2021-11-30 12:20:20"}]}