import Vue from 'vue'
import Vuex from 'vuex'
// import axios from '@/utils/api.js';

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    newMessage: null,
    userData: null,
    userPlan: null,
    menuBarShow: false,
    loggedOut: false,
    errorApp: false,
    errorMsgApp: '',
    showBanner: false,
    showConflict: false,
    bannerContent: null,
    unreadCounts: {},
    routePermissions: [],
    userExists: false,
    selectedFileData: true,
    instaUserName: null,
    labelsData: [],
    room: null,
    updateRoomLabelsFunction: null,
    createdLabelData: null,
    iframeMode: false,
  },

  mutations: {
    setNewMessage(state, data) {
      state.newMessage = data
    },
    setUserData: (state, data) => {
      state.userData = data
    },
    toggleMenuBar: state => {
      state.menuBarShow = !state.menuBarShow
    },
    logoutUser: state => {
      state.loggedOut = true
    },
    setErrorApp: (state, data) => {
      state.errorApp = data
    },
    setErrorMsgApp: (state, msg) => {
      state.errorMsgApp = msg
    },
    setBanner: (state, data) => {
      state.showBanner = data
    },
    setConflict: (state, data) => {
      state.showConflict = data
    },
    setBannerContent: (state, content) => {
      state.bannerContent = content
    },
    setUnreadCounts: (state, counts) => {
      state.unreadCounts = counts
    },
    setRoutePermissions(state, permissions) {
      state.routePermissions = permissions;
    },
    setUserExists(state, value) {      
      state.userExists = value;
    },
    setSelectedFileData(state, value) {            
      state.selectedFileData = value;
    },
    setInstaUserName(state, value) {                  
      state.instaUserName = value;
    },
    setLabelsData(state, data) { // New mutation      
      state.labelsData = data;
    },
    setRoomId: (state, id) => {      
      state.room = id
    },
    setUpdateRoomLabelsFunction(state, func) {      
      state.updateRoomLabelsFunction = func; // Store function reference
    },
    setCreatedLabelData(state, data) {
      state.createdLabelData = data;
    },
    setIframeMode(state, value) {
      state.iframeMode = value;
    },
    setUserPlan(state, plan) {
      state.userPlan = plan;
    }
  },

  getters: {
    // Plan-based feature access getters
    userPlan: state => state.userPlan,

    hasBasicReporting: state => {
      return ['starter', 'professional', 'enterprise', 'custom'].includes(state.userPlan?.toLowerCase());
    },

    hasAdvancedReporting: state => {
      return ['trail','professional', 'enterprise', 'custom'].includes(state.userPlan?.toLowerCase());
    },

    hasTemplateReporting: state => {
      return ['trail','professional', 'enterprise', 'custom'].includes(state.userPlan?.toLowerCase());
    },

    hasWorkflowDrillDown: state => {
      return ['trail','professional', 'enterprise', 'custom'].includes(state.userPlan?.toLowerCase());
    },

    hasExportFeatures: state => {
      return ['trail','professional', 'enterprise', 'custom'].includes(state.userPlan?.toLowerCase());
    },

    planDisplayName: state => {
      if (!state.userPlan) return 'Unknown';
      return state.userPlan.charAt(0).toUpperCase() + state.userPlan.slice(1).toLowerCase();
    }
  },

  actions: {
    async logout(ctx, user_id) {
      //	TODO: Enable API
      console.log(user_id)
      // const { data } = await axios.get(`api/logout?user_id=${user_id}`);
      // if (data.status === 'ok') {
      ctx.commit('logoutUser')
      // } else {
      //   throw new Error();
      // }
    },
    updateRoutePermissions({ commit }, permissions) {
      commit('setRoutePermissions', permissions);
    },
    saveLabelsData({ commit }, data) { // New action
      commit('setLabelsData', data);
    },
    callUpdateRoomLabels({ state }, idToRemove) {      
      if (state.updateRoomLabelsFunction) {
        state.updateRoomLabelsFunction({idToRemove}); // Call the function
      }
    },
    updateLabelData({ commit }, data) {
      commit("setCreatedLabelData", data);
    }
  }
})
