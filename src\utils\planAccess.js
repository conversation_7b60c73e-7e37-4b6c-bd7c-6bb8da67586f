/**
 * Plan-based access control 
 */

export const PLANS = {
  STARTER: 'starter',
  PROFESSIONAL: 'professional', 
  ENTERPRISE: 'enterprise',
  CUSTOM: 'custom'
}

export const PLAN_FEATURES = {
  [PLANS.STARTER]: {
    basicReporting: true,
    campaignUsageSummary: true,
    templatePerformance: false,
    workflowDrillDown: false,
    advancedFiltering: false,
    dataExport: false,
    templateReporting: false
  },
  [PLANS.PROFESSIONAL]: {
    basicReporting: true,
    campaignUsageSummary: true,
    templatePerformance: true,
    workflowDrillDown: true,
    advancedFiltering: true,
    dataExport: true,
    templateReporting: true
  },
  [PLANS.ENTERPRISE]: {
    basicReporting: true,
    campaignUsageSummary: true,
    templatePerformance: true,
    workflowDrillDown: true,
    advancedFiltering: true,
    dataExport: true,
    templateReporting: true
  },
  [PLANS.CUSTOM]: {
    basicReporting: true,
    campaignUsageSummary: true,
    templatePerformance: true,
    workflowDrillDown: true,
    advancedFiltering: true,
    dataExport: true,
    templateReporting: true
  }
}

export const UPGRADE_MESSAGES = {
  templateReporting: {
    title: 'Template Performance Analytics',
    description: 'Get detailed insights into your template usage and performance metrics.',
    features: [
      'Template usage statistics',
    ],
    targetPlan: 'Professional'
  },
  workflowDrillDown: {
    title: 'Advanced Workflow Analytics',
    description: 'Access detailed workflow-level reports and drill-down capabilities.',
    features: [
      'Workflow-level detailed reports',
    ],
    targetPlan: 'Professional'
  },
  dataExport: {
    title: 'Data Export',
    description: 'Export your data and access advanced reporting features.',
    features: [
      'Export reports to CSV/Excel',
    ],
    targetPlan: 'Professional'
  }
}

export function hasFeatureAccess(userPlan, feature) {
  if (!userPlan || !feature) return false
  
  const normalizedPlan = userPlan.toLowerCase()
  const planFeatures = PLAN_FEATURES[normalizedPlan]
  
  if (!planFeatures) return false
  
  return planFeatures[feature] || false
}

export function getUpgradeMessage(feature) {
  return UPGRADE_MESSAGES[feature] || {
    title: 'Premium Feature',
    description: 'This feature is available in higher plans.',
    features: [],
    targetPlan: 'Professional'
  }
}

/**
 * Check if user can access reporting features
 */
export function getReportingAccess(userPlan) {
  return {
    hasBasicReporting: hasFeatureAccess(userPlan, 'basicReporting'),
    hasTemplateReporting: hasFeatureAccess(userPlan, 'templateReporting'),
    hasWorkflowDrillDown: hasFeatureAccess(userPlan, 'workflowDrillDown'),
    hasDataExport: hasFeatureAccess(userPlan, 'dataExport'),
    hasAdvancedFiltering: hasFeatureAccess(userPlan, 'advancedFiltering')
  }
}

/**
 * Get plan display name
 * @param {string} plan - Plan identifier
 * @returns {string} - Formatted plan name
 */
export function getPlanDisplayName(plan) {
  if (!plan) return 'Unknown'
  return plan.charAt(0).toUpperCase() + plan.slice(1).toLowerCase()
}
