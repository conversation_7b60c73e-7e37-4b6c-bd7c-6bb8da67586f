<template>
  <div class="account">
    <!-- heading -->
    <div class="account__heading-wrapper">
      <h1 class="account__heading">Account</h1>

      <div class="account__heading-right">
        <!-- <button class="btn btn-secondary" @click="handleNewAccount">
          <span>+</span> Add another account
        </button> -->
      </div>
    </div>

    <!-- error -->
    <error-component v-show="errorMessage" :errorMessage="errorMessage" />

    <!-- success -->
    <transition name="fade">
      <success-component v-show="successMessage && !loading" :successMessage="successMessage" @close="closeToast" />
    </transition>

    <!-- spinner -->
    <spinner v-if="loading" onPage="true" />

    <!-- content -->
    <div v-else class="account__content">
      <!-- user info -->
      <div class="user__info-wrapper">
        <div class="profile__wrapper">
          <img :src="avatar" alt="User Avatar" />
          <div class="user__info">
            <div class="text-head">{{ accountUser }}</div>
            <div class="text-subhead">{{ accountData?.waba_phone }}</div>
          </div>
        </div>

        <div class="user__meta">
          <!-- <div class="user__meta-item">
            <div class="text-head">Country</div>
            <div class="text-subhead">{{ accountData.country }}</div>
          </div> -->

          <div class="user__meta-item">
            <div class="text-head">Portal</div>
            <div class="text-subhead">{{ accountData?.portal_id }}</div>
          </div>

          <div class="user__meta-item">
            <div class="text-head">Account</div>
            <div class="text-subhead">{{ accountData?.waba_name }}</div>
          </div>
        </div>
      </div>

      <!-- account info -->
      <div class="account__info-wrapper clearfix">
        <!-- left -->
        <div class="account__info-left">
          <!-- <div class="switchAccount">
            <div class="text-head">Switch Account</div>
            <form>
              <div class="form-group">
                <label for="switch-account" class="form-label">Switch Account</label>
                <v-select
                  id="switch-account"
                  :options="['ss']"
                  placeholder="Switch Account"
                  class="custom-vue-select"
                ></v-select>
              </div>
            </form>
          </div> -->

          <!-- screenshot -->
          <!-- <div class="screenshot">
            <div class="text-head">Logout your account</div>
            <div class="text-head">Screenshot of your account</div>
            <img
              class="img-fluid"
              :src="imageSrc"
              alt="App Screenshot"
              @click="openImage(imageSrc)"
            />

            <div class="buttons-wrapper">
              <button class="btn btn-secondary" @click="logoutHandler">
                Logout
              </button>
              <button class="btn btn-primary" @click.prevent="refreshHandler">
                Refresh
              </button>
            </div>
          </div> -->

          <!-- <div class="payment">
            <div class="text-head">Change Your Mode of Payment</div>
            <p>
              We have recently updated our app, to allow you to change or update
              your credit card details. If you would like to avail this feature,
              please fill this form.
            </p>
            <button class="btn btn-primary" @click="proceedPayment">
              Proceed
            </button>
          </div> -->

          <!-- subscription -->
          <div v-if="subscriptionData" class="subscription">
            <div class="text-head">Subscription</div>
            <p>
              If you wish to unsubscribe,please use the button below. You may also write to us on
              <a href="mailto:<EMAIL>"><EMAIL></a> for support.
            </p>
            <button class="btn btn-danger-custom" @click="cancelSubscription()">Cancel</button>
          </div>
        </div>
      </div>
    </div>
    <error-modal :toggle="toggleErrorModal" :show="showErrorModal" />
    <payment-modal :toggle="togglePaymentModal" :show="showPaymentModal" />
  </div>
</template>

<script>
// import { mapMutations } from 'vuex';
import axios from '@/utils/api.js'
import Spinner from '@/components/Spinner'
import ErrorComponent from '@/components/Error'
import MenuIcon from '@/assets/icons/hamburger_icon.png'
import Avatar from '@/assets/avatar.png'
import Screenshot from '@/assets/screenshot.png'
import SuccessComponent from '@/components/Success'
import API_CONFIG from '@/utils/config.js'
import ErrorModal from '@/components/ErrorModal.vue'
import PaymentModal from '@/components/PaymentModal.vue'
// import vSelect from 'vue-select'
// import 'vue-select/dist/vue-select.css'

export default {
  name: 'Account',

  components: {
    // 'v-select': vSelect,
    Spinner,
    ErrorComponent,
    SuccessComponent,
    ErrorModal,
    PaymentModal
  },

  data() {
    return {
      menuIcon: MenuIcon,
      screenshot: Screenshot,
      avatar: Avatar,
      user_id: null,
      accountUser: null,
      accountData: {},
      screenshotUrl: null,
      loading: true,
      errorMessage: '',
      successMessage: '',
      subscriptionData: null,
      ErrorMessage: '',
      showErrorModal: false,
      showPaymentModal: false,
      showConfirmation: false
    }
  },

  created() {
    const userData = this.$store.state.userData
    //  console.log(userData);
    this.user_id = userData.user_id
    this.accountUser = userData.accountUser

    this.getInitialData()
  },

  methods: {
    // ...mapMutations(['logoutUser']),

    async getInitialData() {
      const accountReq = axios.get(`api/account?user_id=${this.user_id}`)
      try {
        const responses = await Promise.all([accountReq])
        const accountData = responses[0].data.data

        this.accountData = accountData

        if (accountData.subscription) {
          this.subscriptionData = accountData.subscription
        }
        this.loading = false
      } catch (err) {
        this.loading = false
        this.errorMessage = 'Something went wrong!'
        console.log(err)
      }
    },

    async logoutHandler() {
      try {
        await this.$store.dispatch('logout', this.user_id)
        this.errorMessage = ''
      } catch (err) {
        this.errorMessage = 'Something went wrong!'
        console.log(err)
      }
    },

    async refreshHandler() {
      try {
        const { data } = await axios.get(`api/screenshot?user_id=${this.user_id}`)
        if (data.status === 'ok') {
          if (data.isNew) {
            this.screenshotUrl = data.data
            this.successMessage = 'Successfully updated!'
            setTimeout(() => (this.successMessage = ''), 3000)
          } else {
            this.successMessage = 'Already up to date!'
            setTimeout(() => (this.successMessage = ''), 3000)
          }
          this.errorMessage = ''
        } else {
          throw new Error()
        }
      } catch (err) {
        this.errorMessage = 'Something went wrong!'
        console.log(err)
      }
    },

    openImage(url) {
      window.open(url, '_blank')
    },

    closeToast() {
      this.successMessage = ''
    },

    handleNewAccount() {
      const url = 'https://www.niswey.com/hubspot-whatsapp-integration'
      window.open(url, '_blank')
    },

    cancelSubscription() {
      if (this.subscriptionData) {
        const url = API_CONFIG.baseURL + 'cancel_account?' + `user_id=${this.user_id}`
        window.open(url, '_blank')
      } else {
        this.showErrorModal = true
      }
    },
    toggleErrorModal() {
      this.showErrorModal = !this.showErrorModal
    },

    proceedPayment() {
      let url = 'https://billing.stripe.com/p/login/aEU8ycgAqcGL6Xu5kk'
      window.open(url, '_blank')
    },
    togglePaymentModal() {
      this.showPaymentModal = !this.showPaymentModal
    }
  },

  computed: {
    imageSrc() {
      return this.screenshotUrl || this.screenshot
    }
  }
}
</script>

<style>
.switchAccount {
  max-width: 300px;
}
</style>
