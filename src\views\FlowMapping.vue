<template>
  <div class="position-relative  d-flex flex-column h-100">
    <div class="mappings d-flex flex-column h-100">
      <!-- topbar -->
      <template v-if="!showEdit">
        <div class="mappings_top">
          <div class="heading-wrapper">
            <h1 class="mappings_heading">Flow Templates</h1>
          </div>
          <p class="mappings_info">
            The templates you edit here can be used by other HubSpot users of your portal, who have access to Vira.
          </p>
          <div class="mappings_list-header">
            <div class="input_wrapper">
              <img :src="searchIcon" alt="Search Icon" />
              <input type="text" placeholder="Search Templates" v-model="searchText" />
            </div>
          </div>
        </div>
      </template>
      <template v-if="showEdit">
        <div class="mappings_top">
          <div class="heading-wrapper">
            <img :src="backIcon" @click="setShowEdit()" />
          </div>
          <p class="mappings_info">
            Please map the following fields in the selected flow template with the corresponding Hubspot properties
            here.
          </p>
          <p>
            <strong>Note: </strong>
            <em>To delete mapping set property to None</em>
          </p>
        </div>
      </template>

      <div class="status_block">
        <!-- List -->
        <!-- success -->
        <transition name="fade">
          <success-component v-show="successMessage && !loading" :successMessage="successMessage" @close="closeToast" />
        </transition>

        <error-component v-if="errorMessage && !loading" :errorMessage="errorMessage" />

        <spinner v-if="loading" onPage="true" />
        <spinner v-if="changingMapping && !loading" onPage="false" />
      </div>
      <!-- <template v-if="!showEdit">

      </template> -->
      <mapping-list v-if="!loading && !showEdit" :templates="filteredData" @set-handler="setShowEdit" />

      <mapping-edit
        v-if="!loading && showEdit"
        :template="editingTemplate"
        :asset="templateAsset"
        :properties="properties"
        @edit-handler="editMapping"
      />
    </div>
  </div>
</template>

<script>
import { convertDate } from '@/utils/utils.js'
import axios from '@/utils/api.js'
import SearchIcon from '@/assets/icons/search_icon.png'
import MenuIcon from '@/assets/icons/hamburger_icon.png'
import BackIcon from '@/assets/icons/back_icon.svg'
import MappingList from '@/components/MappingList'
import MappingEdit from '@/components/MappingEdit'
import Spinner from '@/components/Spinner'
import ErrorComponent from '@/components/Error'
import SuccessComponent from '@/components/Success'

export default {
  name: 'FlowMapping',

  components: {
    MappingList,
    MappingEdit,
    Spinner,
    ErrorComponent,
    SuccessComponent
  },

  data() {
    return {
      showSidebar: false,
      searchIcon: SearchIcon,
      menuIcon: MenuIcon,
      backIcon: BackIcon,
      searchText: '',
      mappingData: [],
      properties: [],
      loading: true,
      errorMessage: '',
      successMessage: '',
      user_id: null,
      changingMapping: false,
      errorInAdding: false,
      showEdit: false,
      editingTemplate: null,
      templateAsset: null,
      hasFetchedProperties: false
    }
  },

  created() {
    const userData = this.$store.state.userData
    this.user_id = userData.user_id
    this.portal_id = userData.portal_id
    this.getInitialData()
  },

  methods: {
    async getInitialData() {
      await this.getFlowTemplates()
    },

    async getFlowTemplates() {
      try {
        const { data } = await axios.get(`api/whatsapp/flows/templates?user_id=${this.user_id}`)

        if (data.ok) {
          this.mappingData = [...data.data]

          this.loading = false
        } else {
          throw new Error()
        }
      } catch (err) {
        this.loading = false

        this.errorMessage = 'Something went wrong!'
      }
    },

    async getHubspotProperties() {
      if (this.hasFetchedProperties) {
        return; 
      }
      try {
        const { data } = await axios.get(`api/hubspot/properties?user_id=${this.user_id}&filtered=true`)

        if (data.ok) {
          this.properties = [...data.data]
          this.hasFetchedProperties = true;
        } else {
          throw new Error()
        }
      } catch (err) {
        console.log(err)
      }
    },

    async editMapping(reqData) {
      let flowId = this.editingTemplate.buttons[0].flow_id || null
      reqData.portal_id = this.portal_id
      reqData.portal_id = this.portal_id
      reqData.name = this.editingTemplate.name
      this.changingMapping = true

      try {
        const { data } = await axios.patch(`api/flows/${flowId}?user_id=${this.user_id}`, reqData)
        if (data.ok) {
          const mapping = {
            ...data.data
          }
          this.changingMapping = false
          this.successMessage = 'Successfully Updated!'
          setTimeout(() => (this.successMessage = ''), 3000)
          await await this.getFlowTemplates()
        } else {
          throw new Error()
        }
      } catch (err) {
        this.changingMapping = false
        console.log(err)
      }
    },

    async setShowEdit(templateId = null) {
      if (templateId) {
        this.loading = true
        this.editingTemplate = this.mappingData.find(template => template.id === templateId)
        await  this.getHubspotProperties()


        let flowId = this.editingTemplate.buttons[0].flow_id || null
        try {
          const { data } = await axios.get(`api/whatsapp/flows/asset/${flowId}?user_id=${this.user_id}`)

          if (data.ok) {
            this.templateAsset = [...data.data]

            this.loading = false
            this.showEdit = !this.showEdit
          } else {
            throw new Error()
          }
        } catch (err) {
          console.log(err)
          this.loading = false

          this.errorMessage = 'Something went wrong!'
        }
      } else {
        this.showEdit = !this.showEdit
      }
    },

    closeToast() {
      this.successMessage = ''
    }
  },

  computed: {
    filteredData() {
      return this.mappingData.filter(el => el.name.toLowerCase().includes(this.searchText))
    }
  }
}
</script>
