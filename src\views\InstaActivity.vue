<template>
  <div class="d-flex flex-column h-100 instagram">
    <ErrorComponent v-show="errorFetchUserMessage" :errorMessage="errorFetchUserMessage" />
    <button v-if="errorFetchUserMessage" @click="onBoardLink" class="btn btn-primary m-auto">Connect Instagram</button>
    <spinner v-else-if="!dataLoaded || errorFetchUserMessage" />
    <chat-window v-else :user-data="userData" :current-user-id="currentUserId" :rooms="rooms" :engagement="engagement"
      :participants="participants" :loadingTab="loadingTab" :showAddToHubspot="showAddToHubspot"
      :showParticipants="showParticipants" :setContactObjects="setContactObjects" :rooms-loaded="roomsLoaded"
      :messages="messages" :messages-loaded="messagesLoaded" :selected-room="selectedRoom" :requests="requests"
      :request="request" :labels="labels" :show-labels="showLabels" :room-labels="roomLabels"
      :assigning-label="assigningLabel" :templates="templates" :error-message="errorMessage"
      :success-message="successMessage" :save-key="saveKey" :show-error-modal="showErrorModal"
      :show-create-modal="showCreateModal" :show-success-modal="showSuccessModal" :toggle-error-modal="toggleErrorModal"
      :toggle-success-modal="toggleSuccessModal" :loading-rooms="loadingRooms" :unread-counts="unreadCounts"
      :messageInTransit="messageInTransit" :sidebar-visible="menuBarShow" :insta-page="isInstaPage"
      @chat-label-handler="chatLabelHandler" @room-action-handler="roomActionHandler" @send-message="sendMessage"
      @update-engagement="UpdateEngagement" @add-new-engagement="AddNewEngagement" @add-to-hubspot="addToHubspot"
      @get-group-participants="getGroupParticipants" @fetch-messages="fetchMessages" @open-file="openFile"
      @toggle-labels-modal="toggleLabelsModal" @fetch-more-rooms="fetchMoreRooms"
      @send-forward-message="sendForwardMessage" @close-sidebar="closeSideBar" @redirect-to-hubspot="redirectToHubspot"
      @add-template-msg="addTemplateMsg" />
  </div>
</template>

<style lang="scss">
.vac-message__media-loader {
  position: absolute;
  top: 38%;
  left: 40%;
  transform: translate(-50% -50%);
  background-color: rgba(235, 234, 234, 0.774);
  border-radius: 50%;
  padding: 0.6rem;

  .vac-message__progress-bar {
    border-radius: 50%;
    background: conic-gradient(green 3.6deg, gray 0deg);
    padding: 0.5rem;

    & div {
      background-color: rgb(235, 234, 234);
      border-radius: 50%;
    }
  }
}
</style>

<script>
import { mapState, mapMutations } from 'vuex'
import ChatWindow from '../vuechat/lib/ChatWindow.vue'
import Pusher from 'pusher-js'
import axios from '@/utils/api.js'
import config from '@/utils/config.js'
import Spinner from '@/components/Spinner'
import Avatar from '@/assets/avatar.png'
import { getTimestamp, randomId, isImageFile } from '@/utils/utils.js'
import { parseTimestamp, isSameDay, formatDate } from '@/utils/dates'
import ErrorComponent from '@/components/Error'

export default {
  name: 'InstaChat',

  components: {
    ChatWindow,
    Spinner,
    ErrorComponent
  },

  data() {
    return {
      saveKey: false,
      requests: [],
      request: null,
      showErrorModal: false,
      showSuccessModal: false,
      assigningLabel: false,
      showCreateModal: false,
      showAddToHubspot: false,
      showParticipants: true,
      setContactObjects: false,
      avatar: Avatar,
      templates: [],
      labels: [],
      contactNamedProperties: {},
      commonNamedProperties: {},
      getAllAssociated: {},
      engagement: {},
      participants: {},
      dealsPipeline: {},
      owners: {},
      hwProperties: {},
      loadingTab: false,
      roomLabels: [],
      showLabels: false,
      selectedRoom: {},
      roomsLoaded: false,
      loadingRooms: true,
      rooms: [],
      messages: [],
      dataLoaded: false,
      currentUserId: '',
      currentChatId: null,
      messagesLoaded: false,
      user_id: null,
      username: null,
      portal_id: null,
      accountPhone: null,
      origin: '',
      errorMessage: '',
      successMessage: {},
      messageInTransit: false,
      isInstaPage: true,
      errorFetchUserMessage: null,
      domain: process.env.VUE_APP_INSTA_API_URL,
      userId: null,
      faceBookAuth: null
    }
  },

  computed: {
    ...mapState(['newMessage', 'unreadCounts', 'menuBarShow', 'selectedFileData'])
  },

  watch: {
    newMessage(newVal, oldVal) {
      let newMessage = this.formatMessage(newVal)
      let combinedMessages = this.combineFiles([newMessage])
      this.messages.push(combinedMessages[0])

      this.resetCounts()
    }
  },

  created() {
    const roomId = this.$route.params.id
    const userData = this.$store.state.userData

    // this.userData = { username: userData.accountUser }
    this.userData = {}

    this.user_id = userData.user_id
    this.portal_id = userData.portal_id
    // this.accountPhone = userData.accountPhone
    this.username = userData.accountUser
    this.origin = userData.origin || 'https://app.hubspot.com'
    this.fetchuserId()
    // this.initialRequest(roomId)
  },

  methods: {
    ...mapMutations(['setErrorApp', 'setErrorMsgApp', 'setUnreadCounts', 'toggleMenuBar', 'setInstaUserName']),

    onBoardLink() {
      window.open(`${this.faceBookAuth}`, '_blank')
    },

    async fetchuserId() {
      var urlString = window.location.href
      var url = new URL(urlString)
      let portal_id = url.searchParams.get('portal_id')
      let username = url.searchParams.get('username')

      try {
        let data = await axios.get(`${this.domain}/auth/user?portalId=${portal_id}&username=${username}`)
        let instaId = data?.data?.account?.id
        this.userId = data?.data?.account?.id
        this.accountPhone = data?.data?.account?.id

        if (data.data.status == 'ok') {
          this.initialRequest(instaId)
          this.subscribe()
          this.setInstaUserName(data?.data?.account?.instagram_username)
        } else {
          this.faceBookAuth = data?.data?.facebookAuthUrl
          this.errorFetchUserMessage = data?.data?.message
        }
      } catch (error) {
        console.log(error)
        this.errorFetchUserMessage = error?.message
      }
    },

    subscribe() {
      const dialogId = `${this.portal_id}.${this.accountPhone}`
      let pusher = new Pusher('8a63ec74543a8782f031', {
        forceTLS: true,
        cluster: 'eu',
        authEndpoint: `${this.domain}/auth/pusher`,
        auth: {
          params: {
            user_id: `${this.userId}`
          }
        }
      })

      // pusher.connection.bind('connected', () => {
      //   console.log("connected");
      // })

      const channel = pusher.subscribe('private-' + dialogId)

      // New message
      channel.bind('instagram-new-message', payload => {
        const msg = payload.data

        if (this.currentChatId === msg.chatId) {
          let existingMessage = this.messages.find(m => m._id === msg._id)
          if (!existingMessage) {
            const message = this.formatMessage(msg, !msg.fromMe)
            this.messages.push(message)
            this.updateLastMessage(msg.chatId, true, msg)
          }
        } else {
          this.updateLastMessage(msg.chatId, msg.fromMe === false, msg)
        }
      })

      channel.bind('instagram-delete-message', payload => {
        
        const msg = payload.data
        if (!msg || !msg.id || !msg.chatId) return
        if (this.currentChatId === msg.chatId) {          
          this.messages = this.messages.filter(message => message._id !== msg.id && message.temp_id !== msg.id)
        }
      })

      channel.bind('instagram-read-message', payload => {
        const data = payload.data
        if (!data || !data.apiMessageId) return // Ensure data and ID exist

        // Find the index of the message that matches data.id
        const messageIndex = this.messages.findIndex(message => message._id == data._id || message?.temp_id == data._id)

        if (messageIndex !== -1) {
          // Loop through messages before the found index and mark them as seen
          this.messages = this.messages.map((message, index) => {
            if (index <= messageIndex) {
              return { ...message, seen: true, distributed: true } // Update status
            }
            return message // Keep unchanged
          })
        }
      })
    },

    async initialRequest(user_id) {
      try {
        const dialogsReq = await axios.get(`${this.domain}/api/dialogs?user_id=${user_id}`)

        this.dataLoaded = true

        // using timeout to enable fetch-more-rooms
        setTimeout(() => this.addInitialData(dialogsReq), 0)

        this.setErrorApp(false)
      } catch (err) {
        console.log(err)
        this.setErrorApp(true)
      }
    },

    addInitialData(res, roomId) {
      const rooms = res.data.data || []
      // const rooms = this.dummyData

      const labels = []
      const templates = []

      const roomCounts = {}
      const roomsArray = []
      const countInStorage = this.getCounts()

      rooms.forEach(el => {
        if (countInStorage[el.id]) {
          roomCounts[el.id] = {
            val: countInStorage[el.id]?.val || 0,
            msg_id: countInStorage[el.id]?.msg_id || null
          }
        } else {
          roomCounts[el.id] = { val: 0, msg_id: null }
        }

        const room = this.formatRoom(el, labels)

        roomsArray.push(room)
      })

      this.rooms = roomsArray

      this.setCounts(roomCounts)

      // to open the room after clicking on message queue
      if (roomId) {
        const room = this.rooms.find(el => el.roomId === roomId)
        if (room) this.selectedRoom = room
      }

      this.templates = templates

      this.labels = labels
      this.loadingRooms = false
    },

    formatRoom(room, labels) {
      return {
        did: room.id,
        roomId: room.chatId,
        object_id: room.object_id,
        phone: room.phone,
        roomName: room.name || room.phone,
        pinned: Boolean(room.pinned),
        avatar: room.profile || this.avatar,
        users: [], // don't skip this
        labelsString: room.labels,
        isGroup: false, //room.id.includes("g.us"),
        labels: room.labels ? this.getLabelsArray(room.labels, labels) : [],
        index: room.time,
        time: room.time,
        lastMessage: {
          content: room.name ? room.phone : '', // don't skip this
          timestamp: this.formatTimestamp(room.time)
        }
      }
    },

    getLabelsArray(labelsStr, labels) {
      if (!labelsStr) return []
      return labelsStr.split(',').map(ids => {
        const item = labels.findIndex(el => el.id === Number(ids))
        return {
          ...labels[item],
          selected: true
        }
      })
    },

    formatTimestamp(timestamp) {
      const date = new Date(timestamp * 1000)
      const timestampFormat = isSameDay(date, new Date()) ? 'HH:mm' : 'DD/MM/YY'
      const result = parseTimestamp(timestamp, timestampFormat)
      return timestampFormat === 'HH:mm' ? `Today, ${result}` : result
    },

    toggleLabelsModal(room) {
      if (room) {
        this.roomLabels = room.labels.map(el => el.id)
        this.labels = this.labels.map(el => ({
          ...el,
          roomId: room.roomId,
          selected: this.roomLabels.includes(el.id)
        }))
      } else {
        this.roomLabels = []
      }
      this.showLabels = !this.showLabels
    },

    roomActionHandler({ action, did }) {
      if (action.name === 'pinChat') {
        this.handlePin(did)
      }
      return
    },

    async handlePin(did) {
      const itemIndex = this.rooms.findIndex(el => el.did === did)
      const pinned = !this.rooms[itemIndex].pinned
      try {
        const req = `api/dialog/${did}?user_id=${this.user_id}`
        const { data } = await axios.post(req, { pinned })
        if (data.ok) {
          this.rooms[itemIndex].pinned = pinned
        } else {
          throw new Error()
        }
      } catch (err) {
        this.showErrorModal = true
        console.log(err)
      }
    },

    // Add template msg after successful sent
    addTemplateMsg(newItem) {
      this.messages.unshift(newItem.message)

      this.messages = this.messages.map(el => {
        return this.formatMessage(el)
      })

      const combinedMessages = this.combineFiles(this.messages)

      combinedMessages.forEach(msg => {
        this.messages.unshift(msg)
      })

      this.resetCounts()
    },

    async fetchMessages({ room, options = {}, source }) {
      // console.log(room.roomId,'room');

      const chatId = room.roomId
      this.currentChatId = room.roomId

      if (options.reset) {
        this.messages = []
        this.messagesLoaded = false
      }

      let currentTimeSeconds = Math.round(Date.now() / 1000)
      const time = this.messages.length > 0 ? this.messages[0].time : currentTimeSeconds

      if (time === currentTimeSeconds) {
        this.showAddToHubspot = false
        this.engagement = {}
      }

      this.request = {
        cancel: source.cancel,
        msg: 'Loading...',
        message: 'Request Cancelled'
      }
      const response = await axios
        .get(`${this.domain}/api/messages?user_id=${this.userId}&chatId=${room?.roomId}`, { cancelToken: source.token })
        .catch(this.logResponseErrors)

      if (response) {
        const { data } = response
        if (data.status === 'ok') {
          // update search count if search is opened
          const elems = document.getElementsByClassName('text__highlight')
          const elem = elems[0] || null
          setTimeout(() => {
            if (elem) {
              document.getElementById('count').innerHTML = elems.length
            }
          }, 350)

          this.clearOldRequest('Success')
          this.messagesLoaded = true

          if (data.messages.length === 0) {
            return
          }

          const messages = data.messages.map(el => {
            return this.formatMessage(el)
          })

          const combinedMessages = this.combineFiles(messages)

          combinedMessages.forEach(msg => {
            this.messages.unshift(msg)
          })

          this.resetCounts()
          this.setErrorApp(false)
        } else {
          console.log(data)
        }
      }
    },

    logResponseErrors(err) {
      console.log(err)
      console.log('Request cancelled')
    },

    clearOldRequest(msg) {
      this.request.msg = msg
      this.requests.push(this.request)
      this.request = null
    },
    combineFiles(messages) {
      const allMessages = []
      let message = {}

      for (let [i, msg] of messages.entries()) {
        if (!msg.files) {
          message = msg
        } else {
          const files = [
            {
              ...msg.files[0],
              timestamp: msg.timestamp,
              distributed: msg.distributed,
              seen: msg.seen,
              date: msg.date,
              username: msg.username,
              _id: msg._id
            }
          ]

          if (i === 0) {
            message = { ...msg, files }
          } else {
            const lastIndex = allMessages.length - 1
            const lastFileIndex = allMessages[lastIndex].files?.length - 1

            if (
              allMessages[lastIndex].files &&
              msg.fromMe === allMessages[lastIndex]?.fromMe &&
              msg.username === allMessages[lastIndex].username &&
              !allMessages[lastIndex].content &&
              isImageFile(msg.files[0]) &&
              isImageFile(allMessages[lastIndex].files[lastFileIndex]) &&
              !msg.content
            ) {
              allMessages[lastIndex].files.unshift(files[0])
              continue
            } else {
              message = { ...msg, files }
            }
          }
        }
        allMessages.push(message)
        message = {}
      }

      return allMessages
    },

    // inc = incoming message
    formatMessage(msg, inc = false) {
      const replyMessage = msg.quotedMsgId
        ? {
          content: msg.quotedFiles ? '' : msg.quotedMsgBody,
          _id: msg.id,
          username: msg.name || msg.senderName,
          files: msg.quotedFiles
        }
        : null
      return {
        _id: msg.id,
        content: msg?.caption ? msg.caption : msg.body,
        username: msg.name ? msg.name || msg.senderName : '',
        date: formatDate(msg.time * 1000),
        timestamp: getTimestamp(msg.time),
        failed: msg.status === 'failed',
        reason: msg.status_reason,
        saved: msg.status === 'sent', // single tick
        distributed: msg.status === 'read' || msg.status === 'sent', // double tick
        seen: msg.status === 'read', // blue tick
        files: msg.files,
        replyMessage: replyMessage,
        fromMe: inc ? 0 : msg.fromMe,
        time: msg.time
      }
    },

    getCounts() {
      const countInStorage = localStorage.getItem('hw-counts')
      return countInStorage ? JSON.parse(countInStorage) : {}
    },

    setCounts(counts) {
      this.setUnreadCounts(counts)
      const countsStr = JSON.stringify(counts)
      localStorage.setItem('hw-counts', countsStr)
    },

    resetCounts() {
      // update in localstorage
      const countInStorage = this.getCounts()
      const obj = {
        val: 0,
        msg_id: countInStorage[this.currentChatId]?.val === 0 ? null : countInStorage[this.currentChatId]?.msg_id
      }
      countInStorage[this.currentChatId] = obj
      this.setCounts(countInStorage)
    },

    sendMessage(message) {
      if (message.files) {
        this.sendFileLocal(message)
      } else {
        this.sendText(message)
      }
    },

    async getGroupParticipants(property) {
      this.participants = {}
      const idx = this.rooms.findIndex(el => el.roomId === property.roomId)
      if (this.rooms[idx].isGroup) {
        try {
          const response = await axios.get(`api/group/participants?user_id=${this.user_id}&chatId=${property.roomId}`)

          if (response.data.status === 'ok') {
            this.participants = this.formatParticipants(response.data.participants)
          }
        } catch (error) {
          console.log(error)
          this.showParticipants = false
        }
      } else {
        this.showParticipants = false
      }
    },

    formatParticipants(participants) {
      const data = {}
      for (const { id, contact } of participants) {
        if (contact) {
          data[id] = contact.name || contact.displayName
        } else {
          data[id] = id.split('@')[0]
        }
      }
      this.showParticipants = false
      return data
    },

    /****
     * getting company/deals/tickets and pipelines - notes/tasks
     *
     * **/
    async getEngagement(property, contact_id = '') {
      var contactId = contact_id

      if (contact_id == '') {
        const idx = this.rooms.findIndex(el => el.roomId === property.roomId)
        contactId = this.rooms[idx].object_id
        if (this.rooms[idx].isGroup) {
          this.showAddToHubspot = false
          this.setContactObjects = false
          return false
        }

        if (contactId == null) {
          // this.showErrorModal = true;
          // this.errorMessage = "Something went wrong!";
          this.loadingTab = false
          this.showAddToHubspot = true
          return false
        }
      }

      let labels = ''
      let properties = ''
      this.owners = {}
      this.loadingTab = true
      this.dealsPipeline = {}
      if (property.value === 'contacts' && Object.keys(this.engagement) === 0) {
        this.showAddToHubspot = true
        this.engagement = {}
      }

      this.request = {
        cancel: property.source.cancel,
        msg: 'Loading...',
        message: 'Request Cancelled'
      }
      try {
        await axios
          .get(`v1/getV3Properties?user_id=${this.user_id}&contact_id=${contactId}&object_type=${property.value}`, {
            cancelToken: property.source.token
          })
          .then(response => {
            if (response.data.status === 'success') {
              this.commonNamedProperties = response.data.properties
              this.hwProperties = response.data.callProperties
              if (response.data.owners) {
                this.owners = this.formatOwner(response.data.owners)
              }
              properties = this.hwProperties.values || ''
              labels = this.hwProperties.labels || ''
            } else {
              this.showErrorModal = true
              //this.loadingTab = false;
              this.errorMessage = 'Something went wrong!'
            }
          })

        this.clearOldRequest('Success')
      } catch (error) {
        console.log(error)
        //this.setErrorApp(true);
        this.showAddToHubspot = false
      }

      // only for tickets and deals
      if (property.value === 'tickets' || property.value === 'deals') {
        this.request = {
          cancel: property.source.cancel,
          msg: 'Loading...',
          message: 'Request Cancelled'
        }
        try {
          await axios
            .get(`v1/pipelines?user_id=${this.user_id}&object_type=${property.value}`, {
              cancelToken: property.source.token
            })
            .then(response => {
              if (response.data.status === 'success') {
                this.dealsPipeline = response.data.pipelines
              } else {
                this.showErrorModal = true
                //this.loadingTab = false;
                this.errorMessage = 'Something went wrong!'
              }
            })
            .finally(() => {
              this.clearOldRequest('Success')
              //this.loadingTab = false;
              this.showAddToHubspot = false
            })
        } catch (error) {
          console.log(error)
          //this.setErrorApp(true);
          this.showAddToHubspot = false
        }
      }

      if (property.value === 'notes') {
        this.request = {
          cancel: property.source.cancel,
          msg: 'Loading...',
          message: 'Request Cancelled'
        }
        var getProperty = ['id']
        try {
          await axios
            .get(`v1/getAllAssociatedIds?user_id=${this.user_id}&contact_id=${contactId}&properties=${getProperty}`, {
              cancelToken: property.source.token
            })
            .then(response => {
              if (response.data.status === 'success') {
                this.getAllAssociated = response.data.result
              }
            })
            .finally(() => {
              //this.loadingTab = false;
              this.clearOldRequest('Success')
              this.showAddToHubspot = false
            })
        } catch (error) {
          console.log(error)
          //this.setErrorApp(true);
          this.showAddToHubspot = false
        }
      }

      if (Object.keys(this.commonNamedProperties).length === 0) return

      this.loadingTab = true

      try {
        this.request = {
          cancel: property.source.cancel,
          msg: 'Loading...',
          message: 'Request Cancelled'
        }
        const response = await axios
          .get(`v1/getV3Objects?user_id=${this.user_id}&contact_id=${contactId}&engagement=${property.value}`, {
            cancelToken: property.source.token
          })
          .finally(() => {
            this.loadingTab = false
            this.showAddToHubspot = false
          })

        if (response.data.status === 'success') {
          this.engagement = property.value
            ? this.formatCommon(
              response.data[property.value],
              labels,
              properties,
              this.commonNamedProperties,
              property.value,
              this.owners,
              this.dealsPipeline
            )
            : this.formatCommon(
              {},
              labels,
              properties,
              this.commonNamedProperties,
              property.value,
              this.owners,
              this.dealsPipeline
            )

          if (property.value) {
            this.showCreateModal = false
          }
          this.showAddToHubspot = false
        } else if (response.data.message === 'Invalid contacts or not found') {
          this.showAddToHubspot = false
        } else {
          this.engagement = this.formatCommon(
            {},
            labels,
            properties,
            this.commonNamedProperties,
            property.value,
            this.owners,
            this.dealsPipeline
          )
          this.showAddToHubspot = response.data.contacts === 'Not Found'
          if (property.value === 'contacts') {
            if (response.data.message === 'User has available accounts') {
              //this.engagement = {};
              this.showErrorModal = true
            } else {
              this.showErrorModal = true
              this.errorMessage = 'Something went wrong!'
            }
          }
        }
        this.setContactObjects = true
        this.clearOldRequest('Success')
      } catch (error) {
        console.log(error)
        //this.setErrorApp(true);
        this.showAddToHubspot = false
      }
    },

    // for every engagement element, labels, properties, all properties, tab name, pipelines
    formatCommon(el, label, properties, commonNamedProperties, tab, owners = {}, dealsPipeline = {}) {
      try {
        const labels = label.length
        const files = {}
        let dropdownCheck = true
        const { ownerId, firstname, lastname } = JSON.parse(localStorage.getItem('hubspot_owner_id')) || {}

        if (Object.keys(el).length === 0) {
          // updating files array with properties
          files[0] = {
            id: '',
            tab: tab,
            pipelines: dealsPipeline,
            archived: '',
            ownerId: ownerId,
            firstname: firstname,
            lastname: lastname,
            selectedPipe: '',
            actual_stage: '',
            priority_options: { low: 'LOW', medium: 'MEDIUM', high: 'HIGH' },
            task_type_options: {
              Call: 'CALL',
              Email: 'EMAIL',
              'To-do': 'TODO'
            },
            task_status: {
              Completed: 'COMPLETED',
              Deferred: 'DEFERRED',
              'In Progress': 'IN_PROGRESS',
              'Not Started': 'NOT_STARTED',
              Waiting: 'WAITING'
            },
            edit_options: [],
            owner: owners,
            timestamp: ''
          }
          return files
        }
        // foreach
        el.forEach(function callback(value) {
          let pipeline = ''
          let dealStage = ''
          let actual_stage = ''
          let get_value = ''
          let field_type = ''
          let actual_value = ''
          let selectedStage = ''
          let options = ''
          const name = {}

          // if deal pipeline is specified
          if (Object.keys(dealsPipeline).length) {
            pipeline = value.properties.pipeline || value.properties.hs_pipeline
            dealStage = value.properties.dealstage || value.properties.hs_pipeline_stage
            if (pipeline && dealsPipeline[pipeline] && dealsPipeline[pipeline].stages) {
              actual_stage = dealsPipeline[pipeline].stages.findIndex(el2 => el2.id === dealStage)
            }
          }

          // storing name, owneid in local storage
          if (value.properties.firstname) {
            const setLocalStorage = {
              ownerId: value.properties.hubspot_owner_id,
              firstname: value.properties.firstname,
              lastname: value.properties.lastname
            }
            localStorage.setItem('hubspot_owner_id', JSON.stringify(setLocalStorage))
          }

          // loop with labels
          for (let index = 0; index < labels; index++) {
            dropdownCheck = true
            options = ''
            get_value = value.properties[properties[index]]
            field_type = commonNamedProperties[properties[index]].fieldType
            actual_value = get_value
            selectedStage = ''

            if (
              commonNamedProperties[properties[index]].options !== undefined &&
              commonNamedProperties[properties[index]].options !== ''
            ) {
              options = commonNamedProperties[properties[index]].options
              dropdownCheck = false
            }

            if (
              properties[index] === 'dealstage' ||
              properties[index] === 'hs_pipeline_stage' ||
              properties[index] === 'hs_pipeline'
            ) {
              get_value = dealsPipeline[pipeline].stages[actual_stage].label || ''
              actual_value = value.properties.dealstage || ''
              selectedStage = dealsPipeline[pipeline].stages
              dropdownCheck = false
            }

            if (properties[index] === 'pipeline' || properties[index] === 'hs_pipeline') {
              get_value = dealsPipeline[pipeline].label || ''
              dropdownCheck = false
              actual_value = pipeline || ''
            } else if (properties[index] === 'closedate' || properties[index] === 'createdate') {
              const d = new Date(get_value)
              var str = d.toISOString().slice(0, 10)
              get_value = str
            }

            name[properties[index]] = {
              label: label[index],
              actual_value: actual_value || '',
              actual_name: properties[index],
              value: get_value || '',
              options: options || '',
              field_type: field_type || '',
              ready_only: commonNamedProperties[properties[index]].ready_only,
              selectedStage: selectedStage,
              editIconButton: true,
              saveButton: false,
              dropdownCheck: dropdownCheck
            }
          }

          // updating files array with properties
          files[value.id] = {
            id: value.id || '',
            tab: tab,
            pipelines: dealsPipeline,
            archived: value.archived,
            ownerId: ownerId,
            firstname: firstname,
            lastname: lastname,
            selectedPipe: pipeline,
            actual_stage: actual_stage,
            priority_options: { low: 'LOW', medium: 'MEDIUM', high: 'HIGH' },
            task_type_options: {
              Call: 'CALL',
              Email: 'EMAIL',
              'To-do': 'TODO'
            },
            task_status: {
              Completed: 'COMPLETED',
              Deferred: 'DEFERRED',
              'In Progress': 'IN_PROGRESS',
              'Not Started': 'NOT_STARTED',
              Waiting: 'WAITING'
            },
            edit_options: name,
            owner: owners,
            timestamp: value.createdAt || ''
          }
        })

        return files
      } catch (error) {
        console.log(error.message)
        this.showErrorModal = true
        this.loadingTab = false
        this.errorMessage = 'Something went wrong!'
      }
    },

    formatOwner(el) {
      var files = {}
      el.forEach(function callback(value) {
        files[value.id] = {
          id: value.id || '',
          label: value.email + ' - ' + value.firstName + ' ' + value.lastName || ''
        }
      })

      return files
    },

    //updating task, notes, tickets, deals, contacts, company
    async UpdateEngagement(property) {
      let reqData = {
        user_id: this.user_id,
        property: property
      }

      this.saveKey = true
      const response = await axios.post('v1/update/engagement', reqData)
      this.saveKey = false
      if (response.data.status === 'ok') {
        this.showSuccessModal = true
        this.successMessage = {
          heading: 'Success',
          content: 'Your changes were updated successfully!'
        }
        setTimeout(() => (this.showSuccessModal = false), 3000)
      } else {
        this.showErrorModal = true
        this.loadingTab = false
        this.errorMessage = 'Something went wrong!'
      }
    },

    async addToHubspot(property) {
      const idx = this.rooms.findIndex(el => el.roomId === property.roomId)
      const name = this.rooms[idx].roomName.split(' ')

      let reqData = {
        user_id: this.user_id,
        engagement: { phone: property.phone, firstname: name[0] || '' },
        object: 'contacts'
      }

      await axios
        .post('v1/add/engagement', reqData)
        .then(response => {
          if (response.data.status === 'success') {
            this.getEngagement({ roomId: property.roomId, value: 'contacts' }, response.data.task.id)
            this.showAddToHubspot = false
            this.showSuccessModal = true
            this.successMessage = {
              heading: 'Success',
              content: 'Contact added to HubSpot successfully!'
            }
            setTimeout(() => (this.showSuccessModal = false), 3000)
          }
        })
        .catch(error => {
          console.error(error.response)
          this.showErrorModal = true
          this.errorMessage = 'Something went wrong!'
        })
    },

    async AddNewEngagement(property) {
      this.loadingTab = true
      const idx = this.rooms.findIndex(el => el.roomId === property.roomId)
      const contactId = this.rooms[idx].object_id
      const associations = []
      const setAssociations = property.associations
      const getAssociated = this.getAllAssociated
      let engagement_param = property.submitForm
      this.saveKey = true
      if (Object.entries(setAssociations).length !== 0) {
        setAssociations.forEach(el1 => {
          const association = {
            to: {
              id: null
            },
            types: [
              {
                associationCategory: 'HUBSPOT_DEFINED',
                associationTypeId: el1.value
              }
            ]
          }

          if (property.tab === 'notes' && el1.id !== 'contact') {
            engagement_param = {
              hs_note_body: property.submitForm.hs_note_body
            }
            for (let i = 0; i < getAssociated[el1.id].length; i++) {
              association.to.id = getAssociated[el1.id][i]
              associations.push(association)
            }
          } else {
            association.to.id = contactId
            associations.push(association)
          }
        })
      }

      const reqData = {
        user_id: this.user_id,
        contact_id: contactId,
        engagement: engagement_param,
        object: property.tab,
        ownerId: property.ownerId,
        associations: associations
      }

      await axios
        .post('v1/add/engagement', reqData)
        .then(response => {
          if (response.data.status === 'success') {
            this.saveKey = false
            this.showCreateModal = false
            this.getEngagement({
              roomId: property.roomId,
              value: property.tab
            })
            this.showSuccessModal = true
            this.successMessage = {
              heading: 'Success',
              content: 'Added successfully!'
            }
            setTimeout(() => (this.showSuccessModal = false), 3000)
          } else {
            this.showErrorModal = true
            this.errorMessage = 'Something went wrong!'
          }
        })
        .catch(error => {
          console.error(error.response)
          this.showErrorModal = true
          this.errorMessage = 'Something went wrong!'
        })
    },

    async sendText(msg) {
      //this.messages = [...this.messages, newMessage];
      const { content, phone, replyMessage, roomId } = msg
      try {
        const tempMsgId = randomId()
        const currentMsgIndex = this.messages.length
        const timestamp = getTimestamp(new Date().getTime() / 1000)
        let url = 'send'
        this.messages.push({
          _id: tempMsgId,
          content: content,
          username: this.username,
          date: formatDate(),
          timestamp,
          saved: false,
          fromMe: 1,
          replyMessage: replyMessage,
          distributed: 'wait'
        })

        const idx = this.rooms.findIndex(el => el.roomId === roomId)
        const objectId = this.rooms[idx]?.object_id || null
        //this.messages = [...this.messages, newMessage];
        let reqData = {
          user_id: `${this.userId}`,
          phone: roomId,
          message: content,
          timestamp: Date.now()
        }

        if (replyMessage) {
          reqData = {
            ...reqData,
            quotedMsgId: replyMessage._id,
            quotedMsgBody: replyMessage.content
          }
          url = 'reply'
        }

        this.messageInTransit = true
        const { data } = await axios.post(`${this.domain}/api/text`, reqData)
        const el = data.savedMessage
        if (data.ok) {
          this.messageInTransit = false

          this.$set(this.messages, currentMsgIndex, {
            ...this.messages[currentMsgIndex],
            content: data.savedMessage.body,
            username: el.name || el.senderName,
            date: formatDate(el.time * 1000),
            timestamp: getTimestamp(el.time),
            saved: true,
            fromMe: Number(el.fromMe),
            replyMessage: replyMessage,
            distributed: true,
            temp_id: el._id,
            // _id: data.savedMessage._id
          })

          this.updateLastMessage(roomId)
        } else {
          let newMessage = this.messages[currentMsgIndex]
          newMessage.failed = true
          this.messages[currentMsgIndex] = newMessage
          this.showErrorModal = true
          this.errorMessage = data.message || 'Unable to send message'
          this.messageInTransit = false
        }
      } catch (err) {
        this.messageInTransit = false
        this.showErrorModal = true
        console.log(err)
      }
    },

    sendFileLocal(msg) {
      const timestamp = getTimestamp(new Date().getTime() / 1000)

      msg.files.forEach((m, i) => {
        const content = i === 0 ? msg.content : ''
        const newMessage = {
          _id: randomId(),
          content: content,
          username: this.username,
          date: formatDate(),
          timestamp,
          distributed: 'wait',
          saved: true,
          fromMe: 1,
          files: [{ ...m, timestamp, saved: true }]
        }

        this.messages = [...this.messages, newMessage]
        this.sendFileApi(content, msg.roomId, newMessage)
        this.updateLastMessage(msg.roomId)
      })
    },

    async sendFileApi(content, roomId, msg) {
      const idx = this.rooms.findIndex(el => el.roomId === roomId)
      const objectId = this.rooms[idx]?.object_id || null

      let formData = new FormData()
      formData.append('file', msg.files[0].rawData)
      formData.append('phone', roomId)
      formData.append('user_id', `${this.userId}`)
      formData.append('timestamp', Date.now())

      try {
        const { data } = await axios.post(`${this.domain}/api/upload`, formData)
        if (data.ok) {
          let el = data?.savedMessage
          // this.messages[currentMsgIndex] = {...data?.savedMessage, saved: true}
          this.updateFileState(false, msg?._id, data?.savedMessage)
        } else {
          throw new Error()
        }
      } catch (err) {
        this.showErrorModal = true
        this.errorMessage = 'Unable to send file'
        this.updateFileState(true, msg._id)
        this.messages.pop()
        console.log(err)
      }
    },

    updateFileState(error, id, data) {
      const msg = this.messages.find(m => m._id === id || m?.temp_id === data?._id)
      console.log({ msg })
      if (!msg) return

      if (error) {
        msg.files[0].error = true
        msg.files[0].loading = false
      } else {
        msg.files[0].loading = false
        msg.files[0].url = data.url ? data.url : msg.files[0].url
        msg.temp_id = data._id
        msg.id = data._id
        msg.distributed = true
      }
    },

    updateLastMessage(roomId, incoming, newChat) {
      const updatedRooms = [...this.rooms]
      const time = Math.round(new Date().getTime() / 1000)
      const idx = this.rooms.findIndex(el => el.roomId === roomId)

      // room exists
      if (idx !== -1) {
        // console.log('found', idx)
        // console.log('found roomId', roomId)

        if (updatedRooms[idx]?.lastMessage?.timestamp) {
          updatedRooms[idx].lastMessage.timestamp = this.formatTimestamp(time)
        }
        if (updatedRooms[idx]?.index) {
          updatedRooms[idx].index = time
        }

        //  it's an incoming message coming via pusher
        if (incoming) {
          const countInStorage = this.getCounts()

          if (countInStorage[roomId]) {
            // exist
            countInStorage[roomId] = {
              val: countInStorage[roomId].val + 1,
              msg_id: countInStorage[roomId].msg_id ? countInStorage[roomId].msg_id : newChat.id
            }
          } else {
            // dont exist
            countInStorage[roomId] = {
              val: 1,
              msg_id: newChat.id
            }
          }
          this.setCounts(countInStorage)
        }

        this.rooms = updatedRooms
      } else {
        //   it's a new room
        const newRoom = {
          ...newChat,
          roomId: newChat.chatId,
          phone: newChat.phone,
          time: time,
          name: newChat.name,
          roomName: newChat.name || newChat.phone,
          pinned: false,
          users: [],
          index: time,
          labels: [],
          labelsString: '',
          avatar: Avatar,
          isGroup: newChat.chatId.includes('g.us'),
          lastMessage: {
            content: newChat.name ? newChat.phone : '',
            timestamp: this.formatTimestamp(time)
          }
        }

        if (newRoom.isGroup) {
          const countInStorage = this.getCounts()

          countInStorage[newRoom.roomId] = {
            val: 1,
            msg_id: newRoom.id
          }
          this.setCounts(countInStorage)
          delete newChat.body
          delete newChat.content
        }
        updatedRooms.unshift(newRoom)
        this.rooms = updatedRooms
      }
    },

    openFile({ file }) {
      window.open(file.file.url, '_blank')
    },

    async chatLabelHandler(ids, roomId) {
      this.assigningLabel = true

      try {
        const reqData = {
          user_id: this.user_id,
          chatId: roomId,
          phone: { labels: ids }
        }

        const { data } = await axios.post(`api/label/assign`, reqData)
        if (data.ok) {
          const params = { ids, roomId }
          this.updateRoomLabels(params)
          // } else {
          // throw new Error();
        }
      } catch (err) {
        this.showErrorModal = true
        this.assigningLabel = false
        this.showLabels = false
        this.errorMessage = 'Something went wrong!'
        console.log(err)
      }
    },

    updateRoomLabels({ ids, roomId }) {
      const updatedRooms = [...this.rooms]

      const roomIdx = this.rooms.findIndex(room => room.roomId === roomId)

      updatedRooms[roomIdx].labels = this.getLabelsArray(ids, this.labels)
      updatedRooms[roomIdx].labelsString = ids

      this.rooms = updatedRooms
      this.assigningLabel = false
      this.showLabels = false
      this.errorMessage = ''
      this.showSuccessModal = true
      this.successMessage = {
        heading: 'Success',
        content: 'Your changes were saved successfully!'
      }
      setTimeout(() => (this.showSuccessModal = false), 3000)
    },

    toggleErrorModal() {
      this.showErrorModal = !this.showErrorModal
      this.errorMessage = ''
    },

    toggleSuccessModal() {
      this.showSuccessModal = !this.showSuccessModal
      this.successMessage = {}
    },

    async fetchMoreRooms() {
      const time = this.rooms.length > 0 ? this.rooms[this.rooms.length - 1].time : null
      try {
        const { data } = await axios.get(`api/dialogs?user_id=${this.user_id}&time=${time}`)

        if (data.status === 'ok') {
          if (data.data.length === 0) {
            this.roomsLoaded = true
            return
          }
          const roomsArray = []

          data.data.forEach(el => {
            const room = this.formatRoom(el, this.labels)
            roomsArray.push(room)
          })

          this.rooms = this.rooms.concat(roomsArray)
        } else {
          throw new Error()
        }
      } catch (err) {
        console.log(err)
        this.setErrorApp(true)
      }
    },

    async sendForwardMessage(payload) {
      const chatIds = payload.chatIds.split(',')
      const updatedRooms = [...this.rooms]
      chatIds.forEach(async el => {
        try {
          const reqData = {
            phone: el,
            forwardMsgId: payload.msg._id,
            forwardMsgBody: payload.msg.content,
            forwardMsgSource: el
          }
          const { data } = await axios.post(`api/forward?user_id=${this.user_id}`, reqData)
          if (data.status === 'ok') {
            const time = Math.round(new Date().getTime() / 1000)
            const roomIdx = this.rooms.findIndex(room => room.roomId === el)
            updatedRooms[roomIdx].lastMessage.timestamp = this.formatTimestamp(time)
            updatedRooms[roomIdx].index = time

            this.rooms = updatedRooms
            this.showSuccessModal = true
            this.successMessage = {
              heading: 'Message forwarded',
              content: 'Your message has been successfully forwarded to the selected contacts.'
            }
            setTimeout(() => (this.showSuccessModal = false), 3000)
          } else {
            this.showErrorModal = true
            this.errorMessage = data.message

            setTimeout(() => {
              this.showErrorModal = false
            }, 3000)
            throw new Error(data.message)
          }
        } catch (err) {
          console.log(err)
        }
      })
    },

    closeSideBar() {
      this.toggleMenuBar()
    },

    redirectToHubspot(room) {
      const url = `${this.origin}/contacts/${this.portal_id}/contact/${room.object_id}/`
      window.open(url)
    }
  }
}
</script>
