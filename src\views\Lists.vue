<template>
  <div class="position-relative d-flex flex-column h-100">
    <div class="lists d-flex flex-column h-100">
      <div class="lists_top d-flex flex-column h-100">
        <div class="heading-wrapper">
          <h1 class="lists_heading">Send Messages to HubSpot Lists</h1>
          <button :disabled="loading" class="btn btn-primary" @click.prevent="openSidebar">Create campaign</button>
        </div>
        <p class="lists_info">Send a WhatsApp message campaign to your HubSpot contacts</p>

        <div v-if="successMessage && !loading" class="status_block">
          <transition name="fade">
            <success-component :successMessage="successMessage" @close="closeToast" />
          </transition>

          <error-component v-if="errorMessage && !loading" :errorMessage="errorMessage" />
          <!-- <spinner v-if="loading" onPage="true" /> -->
        </div>
        <!-- Later use end -->

        <div class="lists_body d-flex flex-column h-100">
          <div class="lists_list d-flex flex-column h-100">
            <div class="search_wrapper">
              <div class="input_wrapper">
                <img :src="searchIcon" class="search-icon" alt="Search Icon" />
                <input type="text" placeholder="Search campaigns here" v-model="searchText" />
              </div>
              <div v-if="selectedCampaignsCount !== 0" class="selected-campaigns-wrapper">
                <p class="campaign-counts">{{ selectedCampaignsCount }} Selected</p>
                <a :href="exportURL" target="_blank" class="download-campaigns-icon">
                  <img :src="downloadIcon" alt="download icon" />
                  <p>Download</p>
                </a>
              </div>
            </div>
            <div class="list_header">
              <div>
                <!-- <p></p> -->
              </div>
              <div>
                <p class="name">Campaign Name</p>
              </div>
              <div>
                <p class="name">Hubspot List Name</p>
              </div>
              <div>
                <p>Contacts</p>
              </div>
              <div>
                <p>Processed</p>
              </div>
              <div>
                <p>Sent</p>
              </div>
              <div>
                <p>Failed</p>
              </div>
              <div>
                <p>Scheduled for</p>
              </div>
              <div>
                <p>Last Processed</p>
              </div>
              <div>
                <p>Status</p>
              </div>
            </div>
            <div v-if="filteredData.length" class="lists_list-body">
              <div v-for="list in filteredData" :key="list.id" class="lists_list-item">
                <div class="list-row">
                  <div>
                    <input
                      @change="e => handleSelectCampaign(e, list)"
                      class="download-campaigns"
                      type="checkbox"
                      :disabled="selectedCampaignsCount === 1 && !list.selected"
                      v-model="list.selected"
                    />
                  </div>
                  <div>
                    <p :title="list.campaign_name || list.name " class="item-name">{{ list.campaign_name || list.name  }} </p>
                  </div>
                  <div>
                    <p :title="list.name" class="item-name">{{ list.name }}</p>
                  </div>
                  <div>
                    <p class="badge bg-primary rounded-pill">{{ list.size }}</p>
                  </div>
                  <div>
                    <p class="badge bg-primary rounded-pill">{{ list.processed }}</p>
                  </div>
                  <div>
                    <p class="badge bg-primary rounded-pill">{{ list.sent }}</p>
                  </div>
                  <div>
                    <p class="badge bg-primary rounded-pill">{{ list.failed }}</p>
                  </div>
                  <div>
                    <p class="badge bg-primary rounded-pill">{{ convertTo12Hour(list) || '-' }}</p>
                  </div>
                  <div>
                    <p class="badge bg-primary rounded-pill --">
                      {{
                        !list.last_processed
                          ? 'Not started'
                          : new Date(list.last_processed * 1000).toLocaleDateString() +
                            ' ' +
                            new Date(list.last_processed * 1000).toLocaleTimeString()
                      }}
                    </p>
                  </div>
                  <div>
                    <div class="form-check form-switch">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        role="button"
                        :checked="list.enabled"
                        @change="updateList($event, list.id)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="no-data-find" v-if="!filteredData.length && !loading">
              <h4>No existing Campaigns found</h4>
            </div>
            <div class="m-auto" v-if="this.lists && loading">
              <spinner v-if="loading" onPage="true" />
            </div>
          </div>
          <!-- end list of created lists -->
        </div>
      </div>
    </div>

    <sidebar heading="Create new Campaign" :show="showSidebar" @close="closeSidebar">
      <create-list
        v-if="showSidebar"
        :error="errorInAdding"
        :adding-list="addingList"
        :templates="templates"
        :hubspot-lists="hubspotLists"
        :campaign-lists="campaignLists"
        :properties="properties?.results"
        :loader="createEventDataLoader"
        @create-handler="addList"
        @fetch-initial-list="createCampaignHandler"
        @update-templates="updateTemplates"
      />
    </sidebar>
  </div>
</template>

<script>
import { convertDate } from '@/utils/utils.js'
import axios from '@/utils/api.js'
import SearchIcon from '@/assets/icons/search_icon.png'
import MenuIcon from '@/assets/icons/hamburger_icon.png'
import InfoIcon from '@/assets/icons/info_icon.png'
import DownloadIcon from '@/assets/icons/download_icon.svg'
import CreateList from '@/components/CreateList'
import Sidebar from '@/components/Sidebar'
import Spinner from '@/components/Spinner'
import ErrorComponent from '@/components/Error'
import SuccessComponent from '@/components/Success'

export default {
  name: 'Lists',

  components: {
    Spinner,
    Sidebar,
    CreateList,
    ErrorComponent,
    SuccessComponent
  },

  data() {
    return {
      showSidebar: false,
      searchIcon: SearchIcon,
      menuIcon: MenuIcon,
      infoIcon: InfoIcon,
      downloadIcon: DownloadIcon,
      searchText: '',
      loading: true,
      createEventDataLoader: false,
      errorMessage: '',
      successMessage: '',
      user_id: null,
      addingList: false,
      errorInAdding: false,
      lists: [], // Holds the list of enrolled lists
      templates: [], // Holds the list of Facebook templates
      properties: [], // Holds the list of hubspot properties
      hubspotLists: [], // Holds the list of HubSpot lists
      campaignLists: [], // Holds the list of campaign lists
      selectedTemplate: '', // Stores the selected template ID
      selectedList: '', // Stores the selected HubSpot list ID
      templateFields: {},
      selectedCampaignsCount: 0,
      exportURL: null,
      domain: process.env.VUE_APP_API_URL
    }
  },

  created() {
    const userData = this.$store.state.userData
    this.user_id = userData.user_id
    this.getInitialData()
  },
  // To update hubspot list
  watch: {
    hubspotLists: {
      handler(newVal) {
        this.hubspotLists = newVal
      },
      deep: true, // Ensures reactivity for nested changes
      immediate: true // Ensures watch runs on component mount
    }
  },

  methods: {
    handleSelectCampaign(event, list) {
      this.exportURL = `${this.domain}campaign/export?user_id=${this.user_id}&id=${list?.id}`
      const isChecked = event.target.checked
      if (isChecked) {
        this.selectedCampaignsCount += 1
      } else {
        this.selectedCampaignsCount -= 1
      }
    },
    updateTemplates(newTemplates) {
      // Concatenate new templates to existing ones
      const uniqueNewTemplates = newTemplates.filter(
        newTemplate => !this.templates.some(existingTemplate => existingTemplate.id === newTemplate.id)
      )

      // Only concatenate if there are actually unique templates to add
      if (uniqueNewTemplates.length > 0) {
        this.templates = this.templates.concat(uniqueNewTemplates)
      }
    },
    convertTo12Hour(item) {
      if (item?.schedule_time) {
        let [date, time] = item?.schedule_time.split(' ')
        let [hours, minutes, seconds] = time.split(':')

        hours = parseInt(hours)

        let period = hours >= 12 ? 'PM' : 'AM'

        hours = hours % 12 || 12

        let [year, month, day] = date.split('-')
        let formattedDate = `${day}-${month}-${year}` // Change date format to DD-MM-YYYY

        // Combine date and time for comparison
        let inputDateTime = new Date(`${date}T${time}`)

        // Get current date and time
        let currentDateTime = new Date()

        // Check if input dateTime is before current dateTime
        if (inputDateTime < currentDateTime && item?.last_processed) {
          return null // Return null if the input dateTime is in the past
        }

        return `${formattedDate} ${hours}:${minutes} ${period}`
      }
    },

    async getInitialData() {
      try {
        const responses = await axios.get(`api/lists?user_id=${this.user_id}`)

        this.lists = responses?.data?.data
        this.campaignLists = responses?.data?.data
        this.loading = false
      } catch (err) {
        this.loading = false
        this.errorMessage = 'Something went wrong!'
        console.log(err)
      }
    },

    async createCampaignHandler(refreshHubspotList = false) {
      this.createEventDataLoader = true

      try {
        if (refreshHubspotList) {
          // Refresh only HubSpot Lists API when button is clicked
          const hubspotListRes = await axios.get(`api/hubspot/lists?user_id=${this.user_id}`)
          this.hubspotLists = hubspotListRes.data.data
        } else {
          // Call all APIs initially
          const templatesReq = axios.get(`api/v1/whatsapp/templates?user_id=${this.user_id}`)
          const hubspotListReq = axios.get(`api/hubspot/lists?user_id=${this.user_id}`)
          const hubspotPropertiesReq = axios.get(`api/hubspot/properties?user_id=${this.user_id}`)

          const responses = await Promise.all([templatesReq, hubspotListReq, hubspotPropertiesReq])

          this.createEventDataLoader = false
          // Assign responses to respective variables
          this.templates = responses[0].data.data
          this.hubspotLists = responses[1].data.data
          this.properties = responses[2].data.data
        }
      } catch (err) {
        this.errorMessage = 'Something went wrong!'
        console.log(err)
      } finally {
        this.createEventDataLoader = false
      }
    },

    async enrollTemplate() {},
    async updateInputFields() {},

    closeSidebar() {
      this.showSidebar = false
      this.errorInAdding = false
    },

    openSidebar() {
      this.showSidebar = true
      this.createCampaignHandler()
    },

    async updateList(event, id) {
      let reqData = {
        enabled: event.target.checked
      }

      try {
        const { data } = await axios.post(`api/lists/${id}?user_id=` + this.user_id, reqData)
        if (data.ok) {
          console.log(data)
          this.successMessage = 'Successfully updated!'
          setTimeout(() => (this.successMessage = ''), 1000)
        } else {
          throw new Error()
        }
      } catch (err) {
        this.errorMessage = 'Unable to update'
        setTimeout(() => (this.errorMessage = ''), 1000)
        console.log(err)
      }
    },

    async addList(data) {
      // don't allow empty lists
      let listSize = parseInt(data.list.size)
      if (!listSize) {
        this.errorMessage = 'This list has no contacts, Please pick another list'
        console.log(data.list.size, 'in no contact list')
        setTimeout(e => {
          this.errorMessage = ''
        }, 1500)
        return
      }

      this.addingList = true

      const reqData = {
        user_id: this.user_id,
        ...data
      }

      try {
        const { data } = await axios.post(`api/lists`, reqData)
        if (data.ok) {
          const list = {
            ...data.data,
            created_at: convertDate(new Date())
          }
          this.lists.unshift(list)
          // this.labelsData.sort((a, b) => a.created_at - b.created_at);
          this.addingList = false
          this.errorInAdding = false
          this.showSidebar = false
          this.successMessage = 'Successfully enrolled!'
          setTimeout(() => (this.successMessage = ''), 2000)
        } else {
          throw new Error()
        }
      } catch (err) {
        this.errorInAdding = true
        this.addingList = false
        console.log(err)
      }
    },

    closeToast() {
      this.successMessage = ''
    }
  },

  computed: {
    filteredData() {
      return this.lists.filter(el => el.name.toLowerCase().includes(this.searchText.toLowerCase()))
    }
  }
}
</script>
