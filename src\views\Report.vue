<template>
  <div class="report">
    <header class="report-header">
      <div class="report-header-left">
        <img :src="reportName?.icon" alt="logo" />
        <h3>{{ reportName?.name }} Report</h3>
        <img @click="reportDropDownHandler" class="dropdown" :src="dropDownIcon" alt="dropdown icon" />
          <!-- v-show="this.staticPortalId?.includes(this.$store.state.userData.portal_id)" /> -->
        <div v-show="showReportDropDown" class="report-dropdown">
          <button v-if="reportName?.name === 'Workflow'" @click="handleReportType('campaign')">
            <img :src="viraIcon" alt="Vira icon" />
            <span>Vira Report</span>
          </button>

          <!-- <button v-else @click="handleReportType('workflow')">
            <img :src="hubspotIcon" alt="Workflow icon" />
            <span>Workflow Report</span>
          </button> -->
          
          <button v-else
            @click="handleReportType('workflow')"
            :disabled="!hasWorkflowDrillDown"
            :class="{ 'disabled-report': !hasWorkflowDrillDown }"
        >
            <img :src="hubspotIcon" alt="Workflow icon" />
            <span>Workflow Report</span>
            <span v-if="!hasWorkflowDrillDown"><img :src="lockIcon" alt="Locked" class="lock-icon" style="height: 25px;width: 25px;"/></span>
        </button>
        </div>
      </div>
    </header>
    <ReportViewer v-if="reportName?.name" :reportName="reportName.name" />
  </div>
</template>

<script>
import ReportViewer from '../vuechat/components/ReportViewer/ReportViewer.vue';
import WhatshiveLogo from '@/assets/icons/whatshive_logo.svg'
import DropDownIcon from '@/assets/icons/dropdown_icon.svg'
import HubspotIcon from '@/assets/icons/hubspot.svg'
import ViraIcon from '@/assets/icons/vira.svg'
import LockIcon from '@/assets/icons/lock_icon.svg';

export default {
  name: 'Report',

  components: {
    ReportViewer
  },

  data() {
    return {
      whatshiveLogo: WhatshiveLogo,
      lockIcon: LockIcon,
      dropDownIcon: DropDownIcon,
      hubspotIcon: HubspotIcon,
      viraIcon: ViraIcon,
      // reportType: 'vira',
      reportName: { name: 'Campaign', icon: ViraIcon },
      showReportDropDown: false,
      staticPortalId: ['7222284'],
    }
  },

  methods: {
    handleReportType(reportType) {
      this.reportName = reportType !== 'campaign' ? { name: 'Workflow', icon: HubspotIcon } : { name: 'Campaign', icon: ViraIcon };
      this.showReportDropDown = !this.showReportDropDown;
    },

    reportDropDownHandler() {
      this.showReportDropDown = !this.showReportDropDown;
    }
  },
  mounted() {
    console.log('Report mounted',this.$store.state.userData.portal_id)
  },
  computed: {
    hasWorkflowDrillDown() {
      return this.$store.getters.hasWorkflowDrillDown;
    }
  }
}
</script>
<style scoped>
.disabled-report {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    position: relative !important;
}

.disabled-report:hover {
    background-color: #f8f9fa !important;
}

.pro-badge {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
    font-weight: 600;
}

.template-restriction {
    margin: 2rem 0;
}

.report-dropdown button:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

.report-dropdown button:disabled:hover {
    background-color: transparent;
}
</style>