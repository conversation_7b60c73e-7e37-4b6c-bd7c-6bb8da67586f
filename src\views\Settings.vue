<template>
  <div class="settings d-flex flex-column h-100">
    <div class="settings__heading-wrapper">
      <h1 class="settings__heading">Settings</h1>
    </div>

    <!-- success -->
    <transition name="fade">
      <success-component v-show="successMessage && !loading" :successMessage="successMessage" @close="closeToast" />
    </transition>

    <!-- error -->
    <error-component v-show="errorMessage && !loading" :errorMessage="errorMessage" />

    <!-- spinner -->
    <spinner v-if="loading" onPage="true" />

    <!-- content -->
    <div v-else class="settings__content">
      <!-- left -->
      <div class="settings__content-left">
        <!-- add Country Code -->
        <div class="settings__form-group">
          <label for="country_code">Country Code</label>
          <p>
            When sending Automated message (from workflow or List) if contact's phone number doesn't have a country code
            then this country code will be added (assuming all contacts are from same country). Remove if not required.
          </p>
          <input
            type="text"
            name="country_code"
            :value="country_code"
            @input="handleNumberChange($event)"
            id="country_code"
            placeholder="Example :  +91"
          />
        </div>

        <!-- <div class="settings__toggle-box">
          <div class="heading">Notifications</div>

          <div class="toggle__item">
            <label class="toggle__icon">
              <input
                type="checkbox"
                name="notifications"
                :checked="notifications"
                @change="handleChange"
              />
              <span class="slider"></span>
            </label>
            <div class="toggle__text">Notifications for incoming messages</div>
          </div>

          <div class="toggle__item">
            <label class="toggle__icon">
              <input
                type="checkbox"
                name="offline_notifications"
                :checked="offline_notifications"
                @change="handleChange"
              />
              <span class="slider"></span>
            </label>
            <div class="toggle__text">
              Notifications from unassigned contacts
            </div>
          </div>
        </div> -->

        <div class="settings__toggle-box">
          <div class="heading">New Contact</div>

          <div class="toggle__item">
            <label class="toggle__icon">
              <input type="checkbox" name="create_user" :checked="create_user" @change="handleChange" />
              <span class="slider"></span>
            </label>
            <div class="toggle__text">Add user to HubSpot Contacts if it does not exist</div>
          </div>
        </div>

        <button class="btn btn-primary" @click="updateSetting">Save</button>
      </div>

      <!-- right -->
      <div class="settings__content-right">
        <!-- block contact -->
        <div class="settings__form-group">
          <label for="block">Block Number</label>
          <p>Please Add number with country code for it to work properly.</p>
          <input
            required
            id="block"
            type="text"
            name="numBlock"
            :value="numBlock"
            ref="blacklistInput"
            @input="handleNumberChange($event)"
            placeholder="Example : +919823xxxxxx"
            pattern="[+][0-9]*"
            title="Please enter valid phone number with country code"
          />
          <button class="btn btn-primary" :disabled="!numBlock.length" @click="blacklistUser">Add</button>
        </div>

        <!-- blacklist users list -->
        <template v-if="blacklistUsers.length">
          <div class="users-list_heading">Numbers added in Blacklist:</div>
          <ul class="users-list">
            <li class="users-list-item" v-for="user in blacklistUsers" :key="user.id">
              <span>{{ user.phone }}</span>
              <img :src="deleteIcon" alt="Delete Icon" class="delete-icon" @click.stop="setId(user.id)" />
            </li>
          </ul>
        </template>

        <div v-if="showConfirmation" class="error-modal">
          <transition name="vac-bounce">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">Confirm Delete</h5>
                </div>
                <div class="modal-body mb-5" style="font-size: 16px !important">
                  Are you sure you want to remove this number?
                </div>
                <div class="modal-footer">
                  <button
                    type="button"
                    class="btn btn-danger text-white"
                    @click.stop="
                      removeBlacklist()
                      showConfirmation = false
                    "
                  >
                    Yes, Delete
                  </button>
                  <button type="button" class="btn btn-secondary m-lg-2" @click="showConfirmation = false">
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </transition>
        </div>
      </div>

      <!-- Toggle -->
    </div>
  </div>
</template>

<script>
import MenuIcon from '@/assets/icons/hamburger_icon.png'
import DeleteIcon from '@/assets/icons/delete_icon.png'
import axios from '@/utils/api.js'
import Spinner from '@/components/Spinner'
import ErrorComponent from '@/components/Error'
import SuccessComponent from '@/components/Success'

export default {
  name: 'Settings',

  components: { Spinner, ErrorComponent, SuccessComponent },

  data() {
    return {
      id: null,
      deleteIcon: DeleteIcon,
      menuIcon: MenuIcon,
      blacklistUsers: [],
      country_code: '',
      numBlock: '',
      notifications: false,
      offline_notifications: false,
      create_user: false,
      user_id: null,
      uid: null,
      loading: true,
      errorMessage: '',
      successMessage: '',
      showConfirmation: false
    }
  },

  created() {
    const userData = this.$store.state.userData

    this.user_id = userData.user_id
    this.uid = userData.uid
    this.portal_id = userData.portal_id
    this.getInitialData()
  },

  methods: {
    setId(id) {
      this.id = id
      this.showConfirmation = true
    },

    handleChange({ target: { name, checked } }) {
      this[name] = checked
    },

    handleNumberChange({ target: { name, value } }) {
      if (Number(value) || value === '') {
        this[name] = value
      }
    },

    async getInitialData() {
      try {
        const reqSetting = axios.get(`api/settings?user_id=${this.user_id}`)

        const reqBlocks = axios.get(`api/blacklist?user_id=${this.user_id}`)
        const [settings, users] = await Promise.all([reqSetting, reqBlocks])
        const sData = settings.data
        const uData = users.data

        if (sData.ok && uData.ok) {
          const s = sData.data
          console.log(s)
          this.notifications = Boolean(s.notifications)
          this.offline_notifications = Boolean(s.offline_notifications)
          this.country_code = s.country_code
          this.create_user = Boolean(s.create_user)
          this.blacklistUsers = uData.data
          this.loading = false
        } else {
          throw new Error()
        }
      } catch (err) {
        this.loading = false
        this.errorMessage = 'Something went wrong!'
        console.log(err)
      }
    },

    async updateSetting() {
      try {
        const reqData = {
          uid: this.uid,
          user_id: this.user_id,
          notifications: Number(this.notifications),
          country_code: this.country_code ? this.country_code : null,
          create_user: Number(this.create_user),
          offline_notifications: Number(this.offline_notifications)
        }

        const { data } = await axios.post(`api/settings`, reqData)
        if (data.ok) {
          this.errorMessage = ''
          this.successMessage = 'Settings Updated Successfully!'
          setTimeout(() => (this.successMessage = ''), 3000)
        } else {
          throw new Error()
        }
      } catch (err) {
        this.errorMessage = 'Something went wrong!'
        console.log(err)
      }
    },

    async blacklistUser() {
      try {
        const reqData = {
          user_id: this.user_id,
          uid: this.uid,
          portal_id: this.portal_id,
          phone: this.numBlock
        }

        const { data } = await axios.post(`api/blacklist`, reqData)

        if (data.ok) {
          this.errorMessage = ''
          this.successMessage = 'Number Blacklisted Successfully!'
          setTimeout(() => (this.successMessage = ''), 3000)
          this.blacklistUsers.push(data.data)
          this.$refs.blacklistInput.value = ''
          this.numBlock = ''
        } else {
          throw new Error()
        }
      } catch (err) {
        this.errorMessage = 'Something went wrong!'
        console.log(err)
      }
    },

    async removeBlacklist() {
      let id = this.id
      if (!id) return

      try {
        const { data } = await axios.delete(`api/blacklist/${id}?user_id=${this.user_id}`)

        if (!data.ok) {
          throw new Error()
        } else {
          this.blacklistUsers = this.blacklistUsers.filter(el => el.id !== id)

          this.successMessage = 'Successfully removed!'
          setTimeout(() => (this.successMessage = ''), 3000)
          this.errorMessage = ''
        }
      } catch (err) {
        this.errorMessage = 'Something went wrong!'
        console.log(err)
      }
    },

    closeToast() {
      this.successMessage = ''
    }
  }
}
</script>

<style>
button.close {
  font-size: 34px;
}

button.btn.btn-danger,
.btn.btn-secondary {
  font-size: 13px;
}

.error-modal {
  position: fixed !important;
  background: #222222b3;
}
</style>
