<template>
  <div class="position-relative d-flex flex-column h-100 templates  templates-table">
    <div class="campaign-error-container">
      <success-component v-show="successMsg" :successMessage="successMsg" />
      <error-component v-show="errorMsg" :errorMessage="errorMsg" />
    </div>
    <div v-if="!showCreateTemplate" class="templates d-flex flex-column h-100">
      <div class="templates_top">
        <div class="heading-wrapper">
          <h1 class="templates_heading">Templates</h1>
          <button @click.prevent="createTemplateHandler" class="btn btn-primary" :disabled="loading">Create
            Template</button>
        </div>
        <p class="templates_info">
          Create, organize, and track the performance of your message templates here.
        </p>
      </div>
      <div class="table-container d-flex flex-column">
        <div class="table-header">
          <input type="text" placeholder="Search for your template" v-model="searchQuery"
            @input="debounceFetchTemplates" class="search-input" />
          <div class="template-actions">
            <div class="templates-selected">
              <p><span>{{ selectedCount }}</span> Selected</p>
            </div>
            <div class="template-delete" :class="{ disabled: selectedCount === 0 }" @click="openDeleteModal">
              <img src="@/assets/icons/delete-outline.svg" alt="delete" />
              <p>Delete</p>
            </div>
          </div>
          <div class="filters">
            <p>Filter By:</p>
            <select v-model="selectedCategory" @change="fetchTemplates" class="filter-select">
              <option value="">Category</option>
              <option value="Utility">Utility</option>
              <option value="Marketing">Marketing</option>
              <option value="AUTHENTICATION">Authentication</option>
            </select>
            <select v-model="selectedStatus" @change="fetchTemplates" class="filter-select">
              <option value="">Status</option>
              <option value="APPROVED">Approved</option>
              <option value="PENDING">Pending</option>
            </select>
          </div>
        </div>
        <div class="custom-table-body-height d-flex flex-column">
          <div v-if="loading" class="loading-container">
            <Spinner />
          </div>
          <div v-if="!loading && this.templates.length === 0" class="loading-container">
            <p colspan="6" class="no-data">No templates available</p>
          </div>
          <table class="custom-table d-flex flex-column" v-if="!loading && templates.length > 0">
            <thead>
              <tr>
                <th></th>
                <th>Template Name</th>
                <th>Category</th>
                <th>Language</th>
                <th>Status</th>
                <th>Reason for Failure</th>
              </tr>
            </thead>
            <tbody :class="{ 'single-record': isSingleRecord }">
              <tr v-for="(row, index) in templates" :key="index" style="position: relative;">
                <td class="select-checkbox">
                  <input type="checkbox" :checked="row.selected" :disabled="isAnySelected && !row.selected"
                    @change="toggleSelection(row)" />
                </td>
                <td>
                 <span class="row-name-title" :title="row.name">{{ row.name }}</span> 
                  <div>
                    <button class="btn btn-icon preview-button" @click="previewTemplate(row)" >
                      <img src="@/assets/icons/preview-outline.svg" alt="Preview" class="icon" />
                      <span class="tooltip">Preview</span>
                    </button>
                  <button class="btn btn-icon copy-button" @click="copyTemplate(row)" >
                    <img src="@/assets/icons/template_copy-outline.svg" alt="Copy Template" class="icon" />
                    <span class="tooltip">Copy Template</span>
                  </button>
                </div>
                </td>
                <td class="column-alignment">{{ row.category }}</td>
                <td class="column-alignment">{{ row.language }}</td>
                <td class="column-alignment">
                  <span class="badge" :class="statusClass(row.status)">
                    {{ row.status }}
                  </span>
                </td>
                <td class="column-alignment">{{ row.status === 'REJECTED' ? row.rejected_reason : ''}}</td>
              </tr>
            </tbody>
          </table>

          <!-- Fixed Pagination (Now Works Properly) -->
          <div v-if="!loading" class="pagination-controls">
            <button @click="loadPreviousPage" :disabled="totalCount <= 10 || !pagination.before">
              <svg :style="{ color: pagination.before ? '#34B7F1' : 'black', opacity: pagination.before ? '1' : '0.3' }"
                width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M16.75 17L9.24996 12L16.75 7.00002Z" />
              </svg>
            </button>

            <button @click="loadNextPage" :disabled="templates.length < 10 || !pagination.after">
              <svg :style="{ color: pagination.after ? '#34B7F1' : 'black', opacity: pagination.after ? '1' : '0.3' }"
                width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M7.25004 6.99998L14.75 12L7.25004 17Z" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Create New Template -->

    <CreateTemplate v-if="showCreateTemplate" :editData="copiedTemplateData" @showTemplateList="backBtnHandler" />

    <!-- Confirmation Modal -->
    <ConfirmationModal v-if="isConfirmationModalVisible" :content="'delete'" :templateName="selectedTemplate?.name"
      :loadText="this.loading"
      :warning="'If you delete this template, you will not be able to create it again with the same name and language for 4 weeks.'"
      @close="isConfirmationModalVisible = false" @deleteUser="deleteTemplate" />

      <TemplatePreviewModal
      v-if="showPreviewModal"
      :visible="showPreviewModal"
      :template="selectedTemplateData"
      @close="showPreviewModal = false"
      ref="previewModal"
    />
  </div>

</template>

<script>
import axios from "@/utils/api.js";
import ConfirmationModal from "../vuechat/components/ConfirmationModal/ConfirmationModal";
import CreateTemplate from "../components/CreateTemplate.vue";
import Spinner from '@/components/Spinner'
import SuccessComponent from '@/components/Success'
import ErrorComponent from '@/components/Error'
import TemplatePreviewModal from "@/components/TemplatePreviewModal.vue";

export default {
  components: {
    ConfirmationModal,
    CreateTemplate,
    Spinner,
    SuccessComponent,
    TemplatePreviewModal,
    ErrorComponent
  },
  data() {
    return {
      fetchedTemplateData: null, // Shared state to store fetched template data
      showCreateTemplate: false,
      debounceTimer: null,
      totalCount: 10,
      limit: 10,
      templates: [],
      loading: false,
      errorMsg: "",
      currentPage: 1,
      totalPages: 1,
      itemsPerPage: 10,
      searchQuery: "",
      selectedCategory: "",
      selectedStatus: "",
      isConfirmationModalVisible: false,
      selectedTemplate: null,
      sampleValues: null,
      previewContent: null,
      successMsg: '',
      pagination: {
        before: "",
        after: "",
      },
      copiedTemplateData: null,
      showPreviewModal: false, selectedTemplateData: {
        name: "",
        header: "",
        body: "",
        footer: "",
        buttons: [],
      },
    };
  },
  computed: {
    selectedCount() {
      return this.templates.filter((row) => row.selected).length;
    },
    isAnySelected() {
      return this.templates.some((row) => row.selected);
    },
    isSingleRecord() {
      return this.templates.length === 1;
    },
  },
  created() {
    this.fetchTemplates();
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    handleClickOutside(event) {
      const previewModal = this.$refs.previewModal?.$el;
      const previewButton = event.target.closest('.preview-button');

      if (previewModal && !previewModal.contains(event.target) && !previewButton) {
        this.showPreviewModal = false;
      }
    },
    debounce(func, delay) {
      let inDebounce;
      return function () {
        const context = this;
        const args = arguments;
        clearTimeout(inDebounce);
        inDebounce = setTimeout(() => func.apply(context, args), delay);
      }
    },

    backBtnHandler() {
      this.fetchTemplates();
      this.showCreateTemplate = !this.showCreateTemplate;
      this.copiedTemplateData = null; // Reset copied data when navigating back
      this.selectedTemplate = null;
    },

    createTemplateHandler() {
      this.showCreateTemplate = !this.showCreateTemplate;
    },
    async fetchTemplates(cursor = "") {
      this.loading = true;
      this.templates = []; // Table ko pehle empty karna
      try {
        const requestBody = {
          limit: this.limit,
          filters: {
            name: this.searchQuery || undefined,
            category: this.selectedCategory || undefined,
            status: this.selectedStatus || undefined,
          },
          before: cursor === "before" ? this.pagination.before : "",
          after: cursor === "after" ? this.pagination.after : "",
        };
        const response = await axios.post(
          `api/whatsapp/template?user_id=${this.$store.state.userData.user_id}`,
          requestBody
        );
       
        if (response.data.ok) {
          this.templates = response.data.templates.data.map((template) => ({
            ...template,
            selected: false,
          }));
          if (response.data.templates.paging) {
            this.pagination.before = response.data.templates.paging.cursors.before || "";
            this.pagination.after = response.data.templates.paging.cursors.after || "";
          }
          if (cursor === "after" && this.pagination.after) {
            this.totalCount += 10; // Next page → Increase count
          } else if (cursor === "before" && this.pagination.before) {
            this.totalCount -= 10; // Previous page → Decrease count
          }
          this.loading = false;
        } else {
          this.templates = [];
          this.errorMsg = "Something went wrong!";
          setTimeout(() => this.errorMsg = '', 3000);
        }
      } catch (error) {
        this.templates = [];
        this.errorMsg = "Something went wrong!";
        setTimeout(() => this.errorMsg = '', 3000);
        console.error("API Fetch Error:", error);
      } finally {
        this.loading = false;
      }
    },
    debounceFetchTemplates() {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => {
        this.fetchTemplates();
      }, 300); // 300ms debounce time
    },
    toggleSelection(selectedRow) {
      if (selectedRow.selected) {
        selectedRow.selected = false;
      } else {
        this.templates.forEach((row) => (row.selected = false));
        selectedRow.selected = true;
      }
    },

    loadPreviousPage() {
      if (this.pagination.before) {
        this.fetchTemplates("before");
      }
    },
    loadNextPage() {
      if (this.pagination.after) {
        this.fetchTemplates("after");
      }
    },

    openDeleteModal() {
      const selected = this.templates.find((row) => row.selected);
      if (selected) {
        this.selectedTemplate = selected;
        this.isConfirmationModalVisible = true;
      }
    },

    async deleteTemplate() {
      this.loading = true;
      let data = '';
      let config = {
        method: 'delete',
        maxBodyLength: Infinity,
        url: `api/whatsapp/template/delete/${this.selectedTemplate.name}?user_id=${this.$store.state.userData.user_id}`,
        headers: {},
        data: data
      };
      try {
        const response = await axios.request(config);
        if (response.data.ok) {
          this.isConfirmationModalVisible = false;
          this.successMsg = 'Template Deleted successfully';
          this.templates = this.templates.filter(template => template.name !== this.selectedTemplate.name);
          setTimeout(() => {
            this.successMsg = '';
          }, 3000)
          this.loading = false;
        } else {
          this.isConfirmationModalVisible = false;
          this.loading = false;
          this.errorMsg = "Something went wrong!";
          setTimeout(() => {
            this.errorMsg = '';
          }, 3000)
        }
      } catch (error) {
        console.log(error);
      }
    },
    statusClass(status) {
      return {
        "badge-approved": status === "APPROVED",
        "badge-pending": status === "PENDING",
        "badge-rejected": status === "REJECTED",
      };
    },
    async fetchTemplateDetails(templateId) {
      if (this.fetchedTemplateData && this.fetchedTemplateData.id === templateId) {
        return this.fetchedTemplateData;
      }

      try {
        const response = await axios.get(
          `api/whatsapp/template/get/${templateId}?user_id=${this.$store.state.userData.user_id}`
        );

        if (response.data.ok) {
          this.fetchedTemplateData = response.data.data; // Store the fetched data
          return this.fetchedTemplateData;
        } else {
          this.errorMsg = "Failed to fetch template details.";
          setTimeout(() => (this.errorMsg = ""), 3000);
          return null;
        }
      } catch (error) {
        console.error("Error fetching template details:", error);
        this.errorMsg = "Something went wrong!";
        setTimeout(() => (this.errorMsg = ""), 3000);
        return null;
      }
    },    async copyTemplate(row) {
      try {
        const templateData = await this.fetchTemplateDetails(row.id);
        if (templateData) {
          // Determine variable type from parameter_format
          const variableType = templateData.parameter_format === "NAMED" ? "Name" : "Number";
          
          // Process example values based on parameter format
          let sampleValues = {};
          const bodyComponent = templateData.components.find(c => c.type === "BODY");
          
          if (bodyComponent?.example) {
            if (templateData.parameter_format === "NAMED" && bodyComponent.example.body_text_named_params) {
              // Handle named parameters
              bodyComponent.example.body_text_named_params.forEach(param => {
                sampleValues[param.param_name] = param.example;
              });
            } else if (bodyComponent.example.body_text?.[0]) {
              // Handle numbered parameters
              bodyComponent.example.body_text[0].forEach((example, index) => {
                sampleValues[index + 1] = example;
              });
            }
          }

          // Create a deep copy of the template data with additional info
          this.copiedTemplateData = {
            name: templateData.name + "_copy",
            category: templateData.category,
            language: templateData.language,
            variableType: variableType, // Add variable type
            sampleValues: sampleValues, // Add sample values
            components: templateData.components.map(comp => {
              // Deep copy each component
              return JSON.parse(JSON.stringify(comp));
            })
          };
          
          this.showCreateTemplate = true;
        }
      } catch (error) {
        console.error("Error copying template:", error);
        this.errorMsg = "Failed to copy template";
        setTimeout(() => this.errorMsg = "", 3000);
      }
    },
    async previewTemplate(row) {
      
      if (row) {
        const headerComponent = row.components.find((c) => c.type === "HEADER");
        const bodyComponent = row.components.find((c) => c.type === "BODY");
        const footerComponent = row.components.find((c) => c.type === "FOOTER");
        const buttonsComponent = row.components.find((c) => c.type === "BUTTONS");

        this.replaceDynamicValues(bodyComponent?.text, bodyComponent?.example);
        
        this.selectedTemplateData = {
          name: row.name || "Untitled Template",
          header: headerComponent?.text || "",
          // body: bodyComponent?.text || "",
          body: this.previewContent || "",
          footer: footerComponent?.text || "",
          buttons: buttonsComponent?.buttons || [],
          templateType: headerComponent?.format || null,
        };

        this.showPreviewModal = true; 
      }
    },

    replaceDynamicValues(templateBody, dynamicValues) {
       // Handle named parameters format
       if (dynamicValues?.body_text_named_params) {
          const sampleValues = {};
          dynamicValues.body_text_named_params.forEach(param => {
            sampleValues[param.param_name] = param.example;
          });
          this.sampleValues = sampleValues;
          
        } 
        // Handle numbered parameters format
        else if (dynamicValues?.body_text?.[0]) {
          const sampleValues = {};
          const exampleValues = dynamicValues.body_text[0];
          
          // Extract variables from message content
          const variables = (templateBody.match(/{{\d+}}/g) || [])
            .map(match => parseInt(match.replace(/[{}]/g, "")));
          
          // Map example values to variables
          variables.forEach((varNum, index) => {
            if (index < exampleValues.length) {
              sampleValues[varNum] = exampleValues[index];
            }
          });
          this.sampleValues = sampleValues;
        }

        if(this.sampleValues){
          let previewText = templateBody;

          for (let key in this.sampleValues) {
            // Ensure we match {{key}} correctly in regex (escaping `{}`)
            let regex = new RegExp(`\\{\\{${key}\\}\\}`, "g");

            // Replace only if the value exists and is not empty
            if (this.sampleValues[key] && this.sampleValues[key].trim() !== "") {
              previewText = previewText.replace(regex, `[${this.sampleValues[key]}]`);
            }
          }
          // Preserve new lines in preview
          this.previewContent = previewText.replace(/\n/g, "<br>");

        }
    },
  },
};
</script>

<style scoped>
.select-checkbox{
  display: flex;
  align-items: center;
  justify-content: center;
}
.column-alignment{
  display: flex;
  align-items: center;
}

.copy-button {
  position: relative;
  display: inline-block;
}

.preview-button .tooltip,.copy-button .tooltip {
  visibility: hidden;
  background-color: #555;
  color: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 5px;
  position: absolute;
  z-index: 9999; 
  bottom: 100%; 
  left: 135%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 15px;
  width: 140px;
}

.preview-button .tooltip{
  width: 100px;
  bottom: 75%;
  left: 83%;
}

.preview-button .tooltip::after,.copy-button .tooltip::after {
  content: '';
  position: absolute;
  top: 100%; 
  left: 13%;
  transform: translateX(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: #555 transparent transparent transparent;
}

.preview-button:hover .tooltip,.copy-button:hover .tooltip {
  visibility: visible;
  opacity: 1;
}


.templates-table .table-container {
  overflow: visible !important;
}

.templates-table .custom-table {
  position: relative;
  z-index: 1;
}

.templates-table .custom-table tbody tr {
  overflow: visible !important;
  position: relative;
}

.templates-table .custom-table tbody tr td:nth-child(2) {
  overflow: visible !important;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.templates-table .custom-table tbody tr td:nth-child(2)::-webkit-scrollbar {
  width: 3px; 
  height:3px;
}

.templates-table .custom-table tbody tr td:nth-child(2)::-webkit-scrollbar-thumb {
  background-color: #34b7f1; 
  border-radius: 10px;
}

.templates-table .custom-table tbody tr td:nth-child(2)::-webkit-scrollbar-track {
  background-color: #f0f0f0;
}


.templates-table .custom-table tbody tr:first-child .copy-button .tooltip{
  bottom: auto; 
  top: 100%; 
}

.templates-table .custom-table tbody tr:first-child .preview-button .tooltip{
  bottom: auto;
  top: 80%;
}

.templates-table .custom-table tbody tr:first-child .copy-button .tooltip::after,
.templates-table .custom-table tbody tr:first-child .preview-button .tooltip::after {
  top: auto; 
  bottom: 100%; 
  border-color: transparent transparent #555 transparent; 
}

button.preview-button,button.copy-button {
  padding: 1.3rem 20px;
}

@media(min-width: 1900px){
  .copy-button .tooltip{
    left: 130%;
    bottom: 87%;
    font-size: 16px;    
  }
  .preview-button .tooltip{left:83%;bottom:68%;}
  
  .templates-table .custom-table tbody tr:first-child .copy-button .tooltip{top:93%;}
  .templates-table .custom-table tbody tr:first-child .preview-button .tooltip
  {top:74%;}
}

@media(max-width:1380px){
  .preview-button .tooltip{
    width: 100px;
    bottom: 75%;
    left: 80%;
  }  
}

@media(max-width:1680px){
  .preview-button .tooltip{
    width: 100px;
    bottom: 75%;
    left: 85%;
  }  
}

.row-name-title{
  text-overflow: ellipsis;
  width: 65%;
  display: block;
  white-space: nowrap;
  overflow: hidden;
}
.templates-table .table-container .custom-table thead tr,
.templates-table .table-container .custom-table tbody tr{
  grid-template-columns: 6rem minmax(200px, 2fr) minmax(150px, 1fr) minmax(120px, 1fr) minmax(120px, 1fr) minmax(120px, 1fr);
}

.templates-table .table-container .custom-table tbody {
  overflow: auto;
}

/* Add this rule to handle single record case */
.templates-table .table-container .custom-table tbody.single-record {
  overflow: visible;
}
</style>