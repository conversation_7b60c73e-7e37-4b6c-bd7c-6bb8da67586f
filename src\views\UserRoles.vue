<template>
    <div class="users-roles-template">
        <h1>User & Roles</h1>
        <div v-show="true" class="secondary">
            <div class="main-content" v-show="showMainContent">
                <div class="user-role-header">
                    <div class="description">
                        <h2>Manage User Dashboard</h2>
                    </div>
                    <div class="btn-div">
                        <button @click="selectUserRole('add-user', true)" class="btn btn-primary">Add User</button>
                        <button @click="selectUserRole('create-role')" class="btn btn-secondary">Create User Role</button>
                    </div>
                </div>
                <p class="section-title">
                    Create new users, customize user permissions, and remove users from your account.
                </p>
                <div class="user-role-permission-table">
                    <div class="search-div">
                        <input v-model="searchQuery" placeholder="<EMAIL>" type="text">
                        <img :src="searchIcon" alt="search icon">
                    </div>
                    <div class="table-title">
                        <p>User Email</p>
                        <p>Permissions</p>
                        <p>User Role</p>
                        <p>Status</p>
                    </div>
                    <Spinner v-if="loading" />
                    <div v-else class="table-value-column">
                        <div v-for="(user, index) in filteredUsers" :key="index" class="table-value">
                            <div :title="user.email" class="useremail-value">
                                <p>{{ user.email }}</p>
                            </div>
                            <div v-if="user.admin" class="permissions-value">
                                <h6>Admin has all the permissions of the dashboard</h6>
                            </div>
                            <div v-else class="permissions-value">
                                <button class="initial-permission" v-if="user.permissions.length === 0"
                                    @click="selectUserRole('add-user', false, user.email)" v-show="true"
                                    :disabled="!user.active">Edit
                                    Permissions</button>
                                <div v-else>
                                    <div v-for="(permission, index) in user.permissions" :key="index">
                                        <p>{{ permission }}</p>
                                    </div>
                                </div>
                                <button v-if="user.permissions.length !== 0"
                                    @click="selectUserRole('add-user', false, user.email, user.permissions)"
                                    class="edit-permissions" title="Edit Permissions" :disabled="!user.active">
                                    <img :src="editIcon" alt="edit icon">
                                </button>
                            </div>
                            <div class="user-role-div">
                                <h6 v-if="user.admin">Admin</h6>
                                <select v-else @change="handleRoleChange($event, user.email)" :disabled="!user.active">
                                    <option value="">{{ user.role || "Select User Role" }}</option>
                                    <option v-for="role in userRoles.filter(r => r.name !== user.role)" :key="role.id"
                                        :value="role.id">
                                        {{ role.name }}
                                    </option>
                                </select>
                            </div>
                            <div class="approovel-permission">
                                <div v-if="!user.approved">
                                    <button @click="showModal('approve', user.email)"><img :src="approoveIcon"
                                            alt="approve"></button>
                                    <button @click="showModal('decline', user.email)"><img :src="declineIcon"
                                            alt="decline"></button>
                                </div>
                                
                                <select v-else @change="handleUserStatus($event, user.email)" :disabled="user.admin">
                                    <option value="">{{ user.active ? "Active user" : "Deactivated user" }}</option>
                                    <option @click="showModal('deactivate', user.email)" v-show="user.active" value="remove">
                                        Deactivate user</option>
                                    <option @click="showModal('reactivate', user.email)" v-show="!user.active" value="reactivate">
                                        Reactivate user</option>
                                </select>
                            </div>
                        </div>
                        <div class="no-list-found" v-if="filteredUsers.length === 0">
                            <h3> No user Found</h3>
                        </div>
                    </div>
                </div>
            </div>
            <CreateUserRoles v-show="isCreateRole" :selectUserRole="selectUserRole" @apiDataReceived="fetchUserRoles"
                @reloadUsers="getAllUsers" />
            <AddUserDetails v-if="isAddUser" :selectUserRole="selectUserRole" :selectDropDown="isShowUserDetailDropDown"
                :selectedUserEmail="selectedUserMail" :permissionsAllowed="permissionsAllowed"
                :changesSaved="userPermissionChangesSaved" :errorWhileSave="errorWhileSave" @saveUserDetails="getAllUsers" @closeToast="handleCloseToast"
                 />
        </div>

        <!-- Updated ConfirmationModal usage -->
        <ConfirmationModal v-if="isConfirmationModalVisible"  :content="modalContent" :selectedUserEmail="selectedUserMail" :userId="this.userData.user_id"
         @close="showModal" @approveOrDeclineUser="assignPermission" @deleteUser="getAllUsers" />
    </div>
</template>

<script>
import { mapState } from 'vuex'
import DeleteIcon from '@/assets/icons/delete.svg'
import SearchIcon from '@/assets/icons/search_icon.svg'
import DeclineIcon from '@/assets/icons/cross_decline.svg'
import ApprooveIcon from '@/assets/icons/approove_icon.svg'
import EditIcon from '@/assets/icons/edit.svg'
import AddUserDetails from '@/components/AddUserDetails'
import CreateUserRoles from '@/components/CreateUserRoles'
import Spinner from '@/components/Spinner'
import ConfirmationModal from '../vuechat/components/ConfirmationModal/ConfirmationModal'
import axios from '@/utils/api.js'

export default {
    name: 'UserRoles',

    components: {
        AddUserDetails,
        CreateUserRoles,
        ConfirmationModal,
        Spinner,
    },

    data() {
        return {
            deleteIcon: DeleteIcon,
            editIcon: EditIcon,
            searchIcon: SearchIcon,
            declineIcon: DeclineIcon,
            approoveIcon: ApprooveIcon,
            show: false,
            showMainContent: true,
            isAddUser: false,
            isCreateRole: false,
            isShowUserDetailDropDown: true,
            isConfirmationModalVisible: false,
            modalContent: '',
            selectedUserMail: '',
            allUsers: [],
            searchQuery: "",
            permissionsAllowed: [],
            loading: false,
            userRoles: [],
            selectedRoles: {},
            userPermissionChangesSaved: false,
            errorWhileSave:false,
        }
    },
    computed: {
        ...mapState(['userData']),
        // Filter users based on the search query (case-insensitive)
        filteredUsers() {
            return this.allUsers.filter((user) =>
                user.email.toLowerCase().includes(this.searchQuery.toLowerCase())
            );
        },
    },
    methods: {
        // Handle different UI states (add user, user permissions, create user role)
        selectUserRole(value, clickedBtnValue = true, selectedEmail = '', userPermissionsAllowed = []) {
            this.selectedUserMail = selectedEmail

            this.permissionsAllowed = userPermissionsAllowed
            if (value === 'add-user') {
                this.isShowUserDetailDropDown = clickedBtnValue;
                this.isCreateRole = false;
                this.isAddUser = true;
                this.showMainContent = false;
            } else if (value === 'create-role') {
                this.isCreateRole = true;
                this.isAddUser = false;
                this.showMainContent = false;
            } else {
                this.isCreateRole = false;
                this.isAddUser = false;
                this.showMainContent = true;
            }
        },

        // Show confirmation modal ( delete, Approve, Decline user)
        showModal(content, email = '') {
            
            this.selectedUserMail = email;
            this.modalContent = content;
            this.isConfirmationModalVisible = !this.isConfirmationModalVisible;
        },

        fetchUserRoles(data) {
            this.userRoles = data;
        },

        handleUserStatus(event, email) {
            const selectedValue = event.target.value;

            switch (selectedValue) {
                case 'remove':
                    this.showModal('deactivate', email);
                    break;
                case 'reactivate':
                    this.showModal('reactivate', email);
                    break;
                default:
                    return;
            }

            event.target.value = '';
        },

        // Assign Role
        handleRoleChange(event, email) {
            const selectedRoleId = event.target.value;
            const body = {
                user_id: this.userData.user_id,
                email: email,
                role_id: selectedRoleId
            }

            if (selectedRoleId) {
                this.getAllUsers('put', body, email, false);
            }
        },

        // Get all users, Assign Permissions, Assign Roles
        async getAllUsers(method = 'get', body = {}, endpoint = '', isRole = true) {
            
            if (method === 'get') {
                this.loading = true;
            }
            try {
                let url = 'api/users';

                // Append specific endpoint if provided
                if (endpoint) {
                    url += `/${endpoint}`;
                }

                if (method === 'get') {
                    url += `?user_id=${this.userData.user_id}`;
                }

                const response = await axios({
                    method, // 'get', 'put', or 'post'
                    url,
                    data: method === 'get' ? null : body,
                });

                // For 'get' requests, update allUsers
                if (method === 'get') {
                    this.allUsers = response.data.users;
                    this.loading = false;
                }
                this.isConfirmationModalVisible = false;

                if ((method === 'put' || method === 'post') && isRole) {
                    this.userPermissionChangesSaved = true;
                    setTimeout(() => {
                        this.userPermissionChangesSaved = false;
                    }, 2000)
                    await this.getAllUsers('get');
                }

                return true;
            } catch (error) {
                if (method === 'post'){
                    this.errorWhileSave = true;
                    setTimeout(() => {
                        this.errorWhileSave = false;
                    }, 2000)
                }
                console.error('API Error:', error);
            }
        },
        handleCloseToast(){
            this.userPermissionChangesSaved = false;
        },

        // Approve or Decline a user
        async assignPermission(payloadKey = 'approve', value = 1) {
            payloadKey = ['reactivate', 'approve'].includes(payloadKey) ? 'approved' : 'declined';
            // payloadKey = payloadKey === ('reactivate' || 'approve') ? 'approved' : 'declined'
            const body = {
                user_id: this.userData.user_id,
                email: this.selectedUserMail,
                // permission_ids: [],
                [payloadKey]: value,
                ...(payloadKey === 'approved' && { declined: 0, active: 1 })
            }
            try {
                const response = await axios.put(`api/users/${this.selectedUserMail}`, body);
                this.allUsers = response.data.users;
                this.getAllUsers();
                this.isConfirmationModalVisible = false;
            } catch (error) {
                console.log(error);
            }
        }
    },
    mounted() {
        // Fetch all users
        this.getAllUsers();
    }
}
</script>
