.app-carousel {
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
  background: white;

  &-topbar {
    width: 100%;
    height: 10vh;
    border-bottom: 1px solid #e9e9e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;

    .image-details {
      display: flex;
      flex-direction: column;

      .image-name {
        font-size: 15px;
        font-weight: 500;
        margin-bottom: 2px;
      }

      .image-time {
        font-size: 11px;
        font-weight: 400;
      }
    }

    svg {
      cursor: pointer;
    }

    &_close {
      // position: absolute;
      // top: 10px;
      // right: 20px;
      font-size: 30px;
      color: #333;
      cursor: pointer;
      border: none;
      background: none;
      margin-left: 10px;
    }

    .forward-icon {
      margin-right: 15px;
    }
  }

  &-main {
    height: calc(90vh - 108px);
    margin-top: 8px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;

    .carousel-img {
      position: relative;
      margin-bottom: 15px;
    }

    .carousel-img > img {
      display: block;
      max-width: 100%;
      margin: 0 auto;
      max-height: calc(90vh - 108px);
    }
  }

  &-thumbnails {
    display: flex;
    justify-content: center;
    flex-direction: row;
    padding-top: 8px;
    padding-bottom: 8px;
    height: 100px;
    overflow: auto;

    .thumbnail-image {
      display: flex;
      align-items: center;
      cursor: pointer;
      overflow: hidden;
      width: 78px;
      min-width: 78px;
      height: 78px;
      object-fit: cover;
      margin: 0 3px;
      transition: all 0.2s;
    }

    .thumbnail-image > img {
      width: 100%;
      height: auto;
      transition: all 250ms;
    }

    .thumbnail-image:hover > img,
    .thumbnail-image.active > img {
      opacity: 0.6;
      box-shadow: 2px 2px 6px 1px rgba(0, 0, 0, 0.5);
    }

    .thumbnail-image:hover,
    .thumbnail-image.active {
      transform: scale(0.9);
    }
  }

  &-actions {
    font-size: 1.5em;
    height: 60px;
    position: absolute;
    top: 50%;
    margin-top: -40px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #ddd;
    z-index: 101;

    & > span {
      cursor: pointer;
      width: 46px;
      height: 46px;
      display: inline-block;
      text-align: center;
      border-radius: 50%;
      background: #585858;
      transition: all 250ms;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    & > span.prev {
      margin-left: 15px;
    }

    & > span.next {
      margin-right: 15px;
    }

    & > span:hover {
      color: #eee;
      transform: scale(1.1);
    }
  }
}
