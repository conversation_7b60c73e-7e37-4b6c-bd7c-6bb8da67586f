<template>
  <div v-if="show" id="carousel" class="app-carousel">
    <!-- topbar -->
    <div class="app-carousel-topbar">
      <div class="image-details">
        <span class="image-name">{{ senderName }}</span>
        <span class="image-time">{{ imageTime }}</span>
      </div>
      <div class="d-flex align-items-center">
        <div @click="$emit('open-forward-modal', { _id: images[activeImage].msg_id })">
          <svg viewBox="0 0 24 24" width="24" height="24" class="forward-icon">
            <path
              fill="currentColor"
              d="M14.278 4.813c0-.723.873-1.085 1.383-.574l6.045 6.051a.81.81 0 0 1 0 1.146l-6.045 6.051a.81.81 0 0 1-1.383-.574v-2.732c-5.096 0-8.829 1.455-11.604 4.611-.246.279-.702.042-.602-.316 1.43-5.173 4.925-10.004 12.206-11.045V4.813z"
            />
          </svg>
        </div>
        <div @click="openFile">
          <svg-icon name="document" />
        </div>
        <button class="app-carousel-topbar_close" @click="close">&times;</button>
      </div>
    </div>

    <div class="app-carousel-main">
      <div class="content">
        <div class="carousel-img">
          <img :src="currentImage" alt="imageName" />
        </div>
      </div>
    </div>

    <div class="app-carousel-thumbnails">
      <div
        v-for="(image, index) in images"
        :key="image.id"
        :class="['thumbnail-image', activeImage == index ? 'active' : '']"
        @click="activateImage(index)"
      >
        <img :src="image.thumb" />
      </div>
    </div>

    <div class="app-carousel-actions">
      <span class="prev" @click="prevImage">
        <svg viewBox="0 0 30 30" width="30" height="30" class="">
          <path fill="currentColor" d="M19.214 21.212L12.865 15l6.35-6.35-1.933-1.932L9 15l8.282 8.282 1.932-2.07z" />
        </svg>
      </span>
      <span class="next" @click="nextImage">
        <svg viewBox="0 0 30 30" width="30" height="30" class="">
          <path fill="currentColor" d="M11 21.212L17.35 15 11 8.65l1.932-1.932L21.215 15l-8.282 8.282L11 21.212z" />
        </svg>
      </span>
    </div>
  </div>
</template>

<script>
import SvgIcon from '../../components/SvgIcon/SvgIcon'
export default {
  name: 'Carousel',
  components: { SvgIcon },
  props: {
    show: { type: Boolean },
    close: { type: Function, default: () => ({}) },
    images: { type: Array, default: () => [] }
  },
  emits: ['open-forward-modal'],
  data() {
    return {
      activeImage: 0
    }
  },
  computed: {
    currentImage() {
      return this.images[this.activeImage]?.big
    },
    senderName() {
      return this.images[this.activeImage]?.username
    },
    imageName() {
      return this.images[this.activeImage]?.name
    },
    imageTime() {
      return this.images[this.activeImage]?.date + ' at ' + this.images[this.activeImage]?.timestamp
    }
  },
  methods: {
    nextImage() {
      let active = this.activeImage + 1
      if (active >= this.images.length) {
        active = 0
      }
      this.activateImage(active)
    },
    prevImage() {
      let active = this.activeImage - 1
      if (active < 0) {
        active = this.images.length - 1
      }
      this.activateImage(active)
    },
    activateImage(imageIndex) {
      this.activeImage = imageIndex
    },
    async openFile() {
      const image = await fetch(this.currentImage)
      const blob = await image.blob()
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = this.imageName
      link.click()
      URL.revokeObjectURL(link.href)
    }
  }
}
</script>

<style></style>
