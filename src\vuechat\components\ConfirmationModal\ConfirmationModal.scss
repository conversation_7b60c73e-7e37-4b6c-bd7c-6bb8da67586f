// .confirmation-modal {
//   position: fixed !important;
//   background: #222222b3;

//   .error-modal_content {
//     display: flex;
//     flex-direction: column;
//     justify-content: space-between;
//     height: 33.5rem !important;
//     width: 30rem;
//     position: relative;

//     .close-btn {
//       all: unset;
//       cursor: pointer;
//       position: absolute;
//       right: 3rem;
//       top: 3rem;

//       img {
//         width: 2.5rem;
//       }
//     }
//   }

//   p {
//     padding: 3rem;
//     color: #2f3e4b;
//     font-weight: 500;
//     font-size: 3.5rem;
//     margin-block-start: 3rem;
//   }

//   .btn-div {
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
//     padding: 3.5rem 4rem;
//     background-color: #eef3fb;
//     border-block-start: 1px solid #d2d2d2;
//   }
// }
.confirmation-modal {
  position: fixed !important;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(34, 34, 34, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;

  .modal-height {
    min-width: 30rem !important;
    width: fit-content !important
  }

  .error-modal_content {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    width: 30rem;
    height: max-content;
    background: white;
    border-radius: 10px;
    box-shadow: 4px 4px 26px rgba(213, 211, 211, 0.3);
    overflow: hidden;
    position: relative;
    text-align: center;

    .close-btn {
      all: unset;
      cursor: pointer;
      position: absolute;
      right: 1.5rem;
      top: 1.5rem;

      img {
        width: 2rem;
      }
    }

    .confirmation-text {
      padding: 3rem;
      color: #2f3e4b;
      font-weight: 500;
      font-size: 3.5rem;
      margin-block-start: 3rem;
    }

    .warning-text-content {
      color: #2f3e4b;
      font-weight: 500;
      font-size: 3.2rem;
      text-align: center;
      padding: 1rem;
      margin: 0;
    }

    .btn-div {
      display: flex;
      justify-content: space-between;
      padding: 3.5rem 4rem;
      background-color: #eef3fb;
      border-top: 1px solid #d2d2d2;
      width: 100%;

      .btn {
        padding: 0.8rem 1.5rem;
        font-size: 1.57rem;
        border-radius: 5px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
      }

    }
  }

  /* Warning Message - Dynamically Adjusted */
  .warning-container {
    width: 90%;
    background-color: #ffeded;
    color: #d9534f;
    padding: 10px;
    border-radius: 5px;
    margin: 30px 0 5px;
    text-align: center;
    display: flex;
    justify-content: center;

  }


  .warning-text {
    font-weight: bold;
    font-size: 2rem;
  }

  /* Overlay */
  .error-modal_overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}