    <template>
    <div class="error-modal confirmation-modal">
        <transition name="vac-bounce">
            <div class="error-modal_content" :class="warning ? 'modal-height' : ''">
                <button @click="$emit('close')" class="close-btn">
                    <img :src="closeIcon" alt="close icon">
                </button>
                <!-- Warning Message (Shown only if 'warning' prop is true) -->
                <div v-if="warning" class="warning-container">
                    <p class="warning-text">⚠ {{ warning }}</p>
                </div>
                <!-- Confirmation Message -->
                <p :class="warning ? 'warning-text-content' : 'confirmation-text'">Are you sure you want to {{ content
                    }} the {{ warning ? `template "${templateName}"` : 'user' }}?</p>
                <div class="btn-div">
                    <button v-if="content === 'deactivate' || content === 'reactivate ' || content=== 'delete'"
                        @click="deleteItem" class="btn btn-primary" :disabled="loadText"> {{ loadText ? "Deleting..." :
                        "Confirm" }}</button>
                    <button v-else @click="$emit('approveOrDeclineUser', content, 1)"
                        class="btn btn-primary">Confirm</button>
                    <button @click="$emit('close')" class="btn btn-secondary">Cancel</button>
                </div>
            </div>
        </transition>
        <div class="error-modal_overlay" />
    </div>
</template>
<script>
import axios from 'axios';
import CloseIcon from '../../../assets/icons/close-icon.svg';
export default {
    name: 'ConfirmationModal',
    props: {
        content: {
            type: String,
        },
        selectedUserEmail: {
            type: String,
        },
        userId: {
            type: String,
        },
        templateName: {
            type: String,
        },
        loadText: {
            type: Boolean,
            default: false
        },
        warning: {
            type: String,
            default: '' // If empty, warning message won't show
        },
    },
    data() {
        return {
            closeIcon: CloseIcon,
            loading: false
        };
    },
    methods: {
        async deleteItem() {
            const body = {
                "user_id": this.userId,
                "active": this.content === 'deactivate' ? 0 : 1
            }
            this.$emit('deleteUser', 'put', body, this.selectedUserEmail)
        }
    }
}
</script>
