.create-tab-modal {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 300;
  display: flex;
  align-items: center;
  justify-content: center;
}
.create-tab-modal_content {
  height: fit-content !important;
  position: relative;
  z-index: 999;
  background: #fff;
  width: 60%;
  box-shadow: #3434341a 0px 0px 2px 1px;
}
.notes form {
  display: inherit !important;
}
.create-tab-header {
  font-size: 36px !important;
  font-weight: 600 !important;
  padding: 15px 35px !important;
  line-height: 1.5 !important;
  display: grid;
  grid-template-columns: 1fr 7%;
  color: #fff;
  height: initial !important;
  background: linear-gradient(263.43deg, rgba(10, 160, 191, 0.2) 0.37%, rgba(0, 0, 0, 0) 48.78%),
    linear-gradient(96.51deg, #0aa0bf 2.85%, rgba(6, 179, 75, 0.2) 223.16%);
}
.close-button {
  position: initial !important;
  font-size: 65px !important;
  line-height: 1 !important;
  border: none;
  background: transparent;
  color: #fff;
}
.create-tab-message {
  text-align: initial !important;
}
.create-tab-message form {
  display: grid;
  grid-template-columns: repeat(2, 1fr) !important;
  column-gap: 6rem;
  padding: 15px 35px !important;
  padding-bottom: 16rem !important;
}
.form-group label {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 1rem;
  justify-content: space-between;
  align-items: center;
}
.form-control-popup {
  height: initial !important;
  font-size: 16px !important;
  width: 100%;
  padding: 7px 14px !important;
}
#pipeline,
#dealstage {
  padding: 9px 14px !important;
}
.create-button {
  position: absolute;
  bottom: 0%;
  left: 0%;
  padding: 3.3rem 35px;
  background: #f5f8fa;
  width: 100%;
}
.create-button button {
  background: #ff725e;
  border-radius: 4px;
  border: 1px solid #ff725e;
  text-transform: lowercase;
}
.create-button button:hover {
  color: #ff725e;
  border: 1px solid #ff725e;
}
.create-tab-modal_overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #3434341a;
  z-index: 315;
}
