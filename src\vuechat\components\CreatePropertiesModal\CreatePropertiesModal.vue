<template>
  <div v-show="show" class="create-tab-modal">
    <transition name="vac-bounce">
      <div v-if="show" class="create-tab-modal_content">
        <div class="create-tab-header">
          <div class="headRightSidePopup">
            <span class="headerRightSidePopup">
              {{ header }}
            </span>
            <span v-if="tab === 'notes'" class="headerNameRightSidePopup"> {{ firstname }} {{ lastname }} </span>
          </div>
          <button class="close-button" @click.prevent="toggle">&times;</button>
        </div>
        <div class="create-tab-message" :class="tab">
          <form ref="form" @submit.prevent="submit">
            <div
              v-for="(val, key, index) in properties.labels"
              :key="index"
              :class="{
                'notes-group': tab === 'notes' && properties.types[key] === 'checkbox',
                'form-group': properties.types[key] !== 'checkbox'
              }"
            >
              <label :for="properties.fields[key]">{{ val }}</label>
              <div
                v-if="
                  properties.types[key] === 'text' ||
                  properties.types[key] === 'date' ||
                  properties.types[key] === 'datetime-local' ||
                  properties.types[key] === 'number'
                "
              >
                <input
                  :id="properties.fields[key]"
                  v-model="form[properties.fields[key]]"
                  :type="properties.types[key]"
                  :name="properties.fields[key]"
                  class="form-control-popup"
                  aria-describedby="emailHelp"
                />
              </div>
              <div v-if="properties.types[key] === 'checkbox'">
                <input
                  :id="properties.fields[key]"
                  v-model="form[properties.fields[key]]"
                  :type="properties.types[key]"
                  :name="properties.fields[key]"
                  class="form-control-popup"
                  aria-describedby="emailHelp"
                  @change="checkboxValue(properties.fields[key], form[properties.fields[key]])"
                />
              </div>

              <div v-else-if="properties.types[key] === 'dropdown'">
                <div
                  v-if="
                    properties.fields[key] === 'hs_ticket_priority' || properties.fields[key] === 'hs_task_priority'
                  "
                >
                  <select
                    :id="properties.fields[key]"
                    v-model="form[properties.fields[key]]"
                    :name="properties.fields[key]"
                    class="form-control-popup"
                    aria-describedby="emailHelp"
                  >
                    <option
                      v-for="(valueStageOption, keyStageOption, indexStageOption) in hsTicketPriority"
                      :key="indexStageOption"
                      :value="valueStageOption"
                    >
                      {{ keyStageOption }}
                    </option>
                  </select>
                </div>

                <div v-if="properties.fields[key] === 'hs_task_status'">
                  <select
                    :id="properties.fields[key]"
                    v-model="form[properties.fields[key]]"
                    :name="properties.fields[key]"
                    class="form-control-popup"
                    aria-describedby="emailHelp"
                  >
                    <option
                      v-for="(valueTaskOption, keyTaskOption, indexTaskOption) in taskStatus"
                      :key="indexTaskOption"
                      :value="valueTaskOption"
                    >
                      {{ keyTaskOption }}
                    </option>
                  </select>
                </div>

                <div v-if="properties.fields[key] === 'hs_task_type'">
                  <select
                    :id="properties.fields[key]"
                    v-model="form[properties.fields[key]]"
                    :name="properties.fields[key]"
                    class="form-control-popup"
                    aria-describedby="emailHelp"
                  >
                    <option
                      v-for="(valueStatusOption, keyStatusOption, indexStatusOption) in taskType"
                      :key="indexStatusOption"
                      :value="valueStatusOption"
                    >
                      {{ keyStatusOption }}
                    </option>
                  </select>
                </div>

                <div v-if="properties.fields[key] === 'hs_pipeline' || properties.fields[key] === 'pipeline'">
                  <div class="select-dropdown">
                    <select
                      :id="properties.fields[key]"
                      v-model="form[properties.fields[key]]"
                      :name="properties.fields[key]"
                      class="form-control-popup"
                      aria-describedby="emailHelp"
                      @change="updatePipeline(form[properties.fields[key]])"
                    >
                      <option
                        v-for="(valuePipelineOption, keyPipelineOption, indexPipelineOption) in hsPipeline"
                        :key="indexPipelineOption"
                        :value="valuePipelineOption.id"
                      >
                        {{ valuePipelineOption.label }}
                      </option>
                    </select>
                  </div>
                </div>

                <div v-if="properties.fields[key] === 'hs_pipeline_stage' || properties.fields[key] === 'dealstage'">
                  <div class="select-dropdown">
                    <select
                      :id="properties.fields[key]"
                      v-model="form[properties.fields[key]]"
                      :name="properties.fields[key]"
                      class="form-control-popup"
                      aria-describedby="emailHelp"
                    >
                      <option
                        v-for="(valueStageOption, keyStageOption, indexStageOption) in currentForm"
                        :key="indexStageOption"
                        :value="valueStageOption.id"
                      >
                        {{ valueStageOption.label }}
                      </option>
                    </select>
                  </div>
                </div>

                <div v-if="properties.fields[key] === 'hubspot_owner_id'">
                  <div class="select-dropdown">
                    <select
                      :id="properties.fields[key]"
                      v-model="form[properties.fields[key]]"
                      :name="properties.fields[key]"
                      class="form-control-popup"
                      aria-describedby="emailHelp"
                    >
                      <option
                        v-for="(valueOwnerOption, keyOwnerOption, indexOwnerOption) in owner"
                        :key="indexOwnerOption"
                        :value="valueOwnerOption.id"
                      >
                        {{ valueOwnerOption.label }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>
              <div v-else-if="properties.types[key] === 'textarea'">
                <textarea
                  :id="properties.fields[key]"
                  v-model="form[properties.fields[key]]"
                  :name="properties.fields[key]"
                  class="form-control-popup"
                  aria-describedby="emailHelp"
                  rows="3"
                />
              </div>
            </div>
            <div class="create-button">
              <button type="submit" class="btn btn-primary">Create a {{ showTabName }}</button>
            </div>
          </form>
        </div>
      </div>
    </transition>
    <div class="create-tab-modal_overlay" @click.prevent="toggle" />
  </div>
</template>
<script>
import createHwProperties from '../../utils/create-hw-properties.json'

export default {
  name: 'CreatePropertiesModal',
  props: {
    show: { type: Boolean },
    tab: { type: String, default: '' },
    showTabName: { type: String, default: '' },
    firstname: { type: String, default: '' },
    lastname: { type: String, default: '' },
    toggle: { type: Function, default: () => ({}) },
    ownerId: { type: String, default: '' },
    hsPipeline: { type: Object, default: null },
    hsTicketPriority: { type: Object, default: null },
    taskType: { type: Object, default: null },
    taskStatus: { type: Object, default: null },
    owner: { type: Object, default: null }
  },
  data() {
    return {
      properties: {},
      form: {}, // create an object to hold all form values
      currentForm: {},
      associations: [],
      element: {}, //
      header: ''
    }
  },
  beforeMount() {
    this.getContactProperties()
  },
  methods: {
    submit: function () {
      if (this.tab === 'notes') {
        this.element = {
          id: 'contact',
          value: createHwProperties.associations[0].notes.contact
        }
        this.associations.push(this.element)
      }
      this.$emit('AddNewEngagement', this.form, this.tab, this.ownerId, this.associations)
    },
    getContactProperties() {
      this.header = createHwProperties.titles[0][this.tab].title
      this.properties = createHwProperties[this.tab][0]
      // this.associations = createHwProperties['associations'][this.tab]['contact']
      if (this.tab === 'tickets' || this.tab === 'deals' || this.tab === 'tasks') {
        this.element = {
          id: this.tab,
          value: createHwProperties.associations[0][this.tab].contact
        }
        this.associations.push(this.element)
      }
    },
    updatePipeline(selectedPipe) {
      try {
        this.currentForm = this.hsPipeline[selectedPipe].stages
      } catch (err) {
        console.log(err.message)
      }
    },
    checkboxValue(field, value) {
      if (value === true) {
        this.element = {
          id: field,
          value: createHwProperties.associations[0][this.tab][field]
        }
        this.associations.push(this.element)
      }
    }
  }
}
</script>
