<template>
  <div class="vac-emoji-wrapper">
    <div class="vac-svg-button emoji-icon" :class="{ 'vac-emoji-reaction': emojiReaction }" @click="openEmoji">
      <slot name="emoji-picker-icon">
        <svg
          width="35"
          height="36"
          viewBox="0 0 35 36"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          :param="emojiReaction ? 'reaction' : ''"
        >
          <rect y="0.729492" width="35" height="34.9353" fill="url(#pattern0)" />
          <defs>
            <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
              <use xlink:href="#image0" transform="translate(0 -0.000925904) scale(0.015625)" />
            </pattern>
            <image
              id="image0"
              width="64"
              height="64"
              xlink:href="data:image/png;base64,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"
            />
          </defs>
        </svg>
      </slot>
    </div>

    <template v-if="emojiOpened">
      <transition name="vac-slide-up" appear>
        <div
          class="vac-emoji-picker"
          :class="{ 'vac-picker-reaction': emojiReaction }"
          :style="{
            height: `${emojiPickerHeight}px`,
            top: positionTop ? emojiPickerHeight : `${emojiPickerTop}px`,
            right: emojiPickerRight,
            display: emojiPickerTop || !emojiReaction ? 'initial' : 'none'
          }"
        >
          <emoji-picker v-if="emojiOpened" ref="emojiPicker" />
        </div>
      </transition>
    </template>
  </div>
</template>

<script>
export default {
  name: 'EmojiPickerContainer',

  props: {
    emojiOpened: { type: Boolean, default: false },
    emojiReaction: { type: Boolean, default: false },
    roomFooterRef: { type: HTMLDivElement, default: null },
    positionTop: { type: Boolean, default: false },
    positionRight: { type: Boolean, default: false }
  },

  emits: ['add-emoji', 'open-emoji'],

  data() {
    return {
      emojiPickerHeight: 320,
      emojiPickerTop: 0,
      emojiPickerRight: ''
    }
  },

  watch: {
    emojiOpened(val) {
      if (val) {
        setTimeout(() => {
          this.addCustomStyling()

          document.querySelector('emoji-picker').addEventListener('emoji-click', ({ detail }) => {
            this.$emit('add-emoji', {
              unicode: detail.unicode
            })
          })
        }, 0)
      }
    }
  },

  methods: {
    addCustomStyling() {
      const picker = `.picker {
				border: none;
			}`

      const nav = `.nav {
				overflow-x: auto;
			}`

      const searchBox = `.search-wrapper {
				padding-right: 2px;
				padding-left: 2px;
			}`

      const search = `input.search {
				height: 32px;
				font-size: 14px;
				border-radius: 10rem;
				border: var(--chat-border-style);
				padding: 5px 10px;
				outline: none;
				background: var(--chat-bg-color-input);
				color: var(--chat-color);
			}`

      const style = document.createElement('style')
      style.textContent = picker + nav + searchBox + search
      this.$refs.emojiPicker.shadowRoot.appendChild(style)
    },
    openEmoji(ev) {
      this.$emit('open-emoji', !this.emojiOpened)
      this.setEmojiPickerPosition(ev.clientY, ev.view.innerWidth, ev.view.innerHeight)
    },
    setEmojiPickerPosition(clientY, innerWidth, innerHeight) {
      setTimeout(() => {
        const mobileSize = innerWidth < 500 || innerHeight < 700

        if (!this.roomFooterRef) {
          if (mobileSize) this.emojiPickerRight = '-50px'
          return
        }

        if (mobileSize) {
          this.emojiPickerRight = innerWidth / 2 - 150 + 'px'
          this.emojiPickerTop = 100
          this.emojiPickerHeight = innerHeight - 200
        } else {
          const roomFooterTop = this.roomFooterRef.getBoundingClientRect().top
          const pickerTopPosition = roomFooterTop - clientY > this.emojiPickerHeight - 50

          if (pickerTopPosition) this.emojiPickerTop = clientY + 10
          else this.emojiPickerTop = clientY - this.emojiPickerHeight - 10

          this.emojiPickerRight = this.positionTop ? '-50px' : this.positionRight ? '60px' : ''
        }
      })
    }
  }
}
</script>
