<template>
  <div v-show="show" class="error-modal">
    <transition name="vac-bounce">
      <div v-if="show" class="error-modal_content">
        <div class="error-header">
          Error
          <button class="close-button" @click.prevent="toggle">&times;</button>
        </div>

        <div v-if="errorMessage" class="error-message">
          <p>{{ errorMessage }}</p>
        </div>

        <div v-else class="error-message">
          <p>Make sure your phone is connected to the Internet.</p>
          <p>Check your message queue for unsent messages.</p>
        </div>
      </div>
    </transition>
    <div class="error-modal_overlay" @click.prevent="toggle" />
  </div>
</template>

<script>
export default {
  name: 'ErrorModal',
  props: {
    show: { type: Boolean },
    toggle: { type: Function, default: () => ({}) },
    errorMessage: { type: String, default: '' }
  }
}
</script>
