<template>
  <div class="vac-format-message-wrapper" :class="{ 'vac-text-ellipsis': singleLine }">
    <div v-if="textFormatting" :class="{ 'vac-text-ellipsis': singleLine }">
      <template v-if="textFormatting">
        <div v-for="(message, i) in filteredLinkifiedMessage" :key="i" class="vac-format-container">
          <!-- Group message -->
          <span v-if="message.type === 'group'" class="msg msg_box">
            <component v-for="(part, index) in message.parts" :key="index" :is="part.url ? 'a' : 'span'" :class="{
              'vac-text-ellipsis': singleLine,
              'vac-text-bold': part.bold,
              'vac-text-italic': deleted || part.italic,
              'vac-text-strike': part.strike,
              'vac-text-underline': part.underline,
              'vac-text-inline-code': !singleLine && part.inline,
              'vac-text-multiline-code': !singleLine && part.multiline,
              'vac-text-tag': !singleLine && !reply && part.tag
            }" :href="part.href" :target="part.href ? linkOptions.target : null"
              :rel="part.href ? linkOptions.rel : null">
              <text-highlight :queries="queries">{{ part.value }}</text-highlight>
            </component>
          </span>

          <!-- Single message -->
          <component v-else :is="message.url ? 'a' : 'span'" :class="{
            'vac-text-ellipsis': singleLine,
            'vac-text-bold': message.bold,
            'vac-text-italic': deleted || message.italic,
            'vac-text-strike': message.strike,
            'vac-text-underline': message.underline,
            'vac-text-inline-code': !singleLine && message.inline,
            'vac-text-multiline-code': !singleLine && message.multiline,
            'vac-text-tag': !singleLine && !reply && message.tag
          }" :href="message.href" :target="message.href ? linkOptions.target : null"
            :rel="message.href ? linkOptions.rel : null">
            <span class="msg msg_box">
              <text-highlight :queries="queries">{{ message.value }}</text-highlight>
            </span>
          </component>
        </div>
      </template>

    </div>
    <div v-else>
      {{ formattedContent }}
    </div>
  </div>
</template>
<script>
import SvgIcon from '../SvgIcon/SvgIcon'
import TextHighlight from 'vue-text-highlight'
import formatString from '../../utils/format-string'
import { IMAGE_TYPES } from '../../utils/constants'
export default {
  name: 'FormatMessage',
  components: { SvgIcon, TextHighlight },
  props: {
    content: { type: [String, Number], required: false },
    deleted: { type: Boolean, default: false },
    users: { type: Array, default: () => [] },
    linkify: { type: Boolean, default: true },
    singleLine: { type: Boolean, default: false },
    reply: { type: Boolean, default: false },
    textFormatting: { type: Boolean, required: true },
    linkOptions: { type: Object, required: true },
    msgSearchQuery: { type: String, default: () => '' },
    isGroup: { type: Boolean, default: true }
  },
  emits: ['open-user-tag'],
  computed: {
    queries() {
      return [this.msgSearchQuery]
    },
    linkifiedMessage() {
      if (!this.content) {
        return null
      }
      let message = formatString(
        this.formatTags(this.content),
        this.linkify && !this.linkOptions.disabled,
        this.linkOptions
      )

      message.forEach(m => {
        m.value = this.cleanText(m.value);
        m.url = this.checkType(m, 'url');
        m.bold = this.checkType(m, 'bold');
        m.italic = this.checkType(m, 'italic');
        m.strike = this.checkType(m, 'strike');
        m.underline = this.checkType(m, 'underline');
        m.inline = this.checkType(m, 'inline-code');
        m.multiline = this.checkType(m, 'multiline-code');
        m.tag = this.checkType(m, 'tag');
        m.image = this.checkImageType(m);
      });

      return this.groupInlineMessages(message);
    },
    filteredLinkifiedMessage() {
      if (!Array.isArray(this.linkifiedMessage)) return [];

      return this.linkifiedMessage.filter(m =>
        (m.type === 'group' && m.parts?.length) ||
        (m.value && m.value.trim())
      );
    }
  },
  methods: {
    groupInlineMessages(messages) {
      const grouped = [];
      let buffer = [];
      const flushBuffer = () => {
        if (buffer.length > 0) {
          grouped.push({
            type: 'group',
            parts: buffer.map(m => ({ ...m })), // keep individual formatting
          });
          buffer = [];
        }
      };
      for (const msg of messages) {
        if (msg.value === '\n' || msg.value === '\n\n' || /^\s*$/.test(msg.value)) {
          flushBuffer();
          grouped.push(msg); // preserve line break
        } else {
          buffer.push(msg);
        }
      }
      flushBuffer();
      return grouped;
    },
    cleanText(text) {
      // Removes zero-width spaces, non-breaking spaces, etc.
      return text.replace(/[\u200B-\u200D\uFEFF\u00A0]/g, '');
    },
    checkType(message, type) {
      return message.types.indexOf(type) !== -1
    },
    checkImageType(message) {
      let index = message.value.lastIndexOf('.')
      const slashIndex = message.value.lastIndexOf('/')
      if (slashIndex > index) index = -1
      const type = message.value.substring(index + 1, message.value.length)
      const isMedia = index > 0 && IMAGE_TYPES.some(t => type.toLowerCase().includes(t))
      if (isMedia) this.setImageSize(message)
      return isMedia
    },
    setImageSize(message) {
      const image = new Image()
      image.src = message.value
      image.addEventListener('load', onLoad)
      function onLoad(img) {
        if (!img?.path) {
          image.removeEventListener('load', onLoad)
          return
        }
        const ratio = img?.path[0].width / 150
        message.height = Math.round(img?.path[0]?.height / ratio) + 'px'
        image.removeEventListener('load', onLoad)
      }
    },
    formatTags(content) {
      if (!content) {
        return content
      }
      const firstTag = '<usertag>'
      const secondTag = '</usertag>'
      const usertags = [...content.matchAll(new RegExp(firstTag, 'gi'))].map(a => a.index)
      const initialContent = content
      usertags.forEach(index => {
        const userId = initialContent.substring(index + firstTag.length, initialContent.indexOf(secondTag, index))
        const user = this.users.find(user => user._id === userId)
        content = content.replaceAll(userId, `@${user?.username || 'unknown'}`)
      })
      return content
    },
    openTag(message) {
      if (!this.singleLine && this.checkType(message, 'tag')) {
        const user = this.users.find(u => message.value.indexOf(u.username) !== -1)
        this.$emit('open-user-tag', user)
      }
    }
  }
}
</script>
