.forward-modal {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 200;

  &_content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 90%;
    width: 68.7rem;
    height: 72rem;
    max-height: 90%;
    background-color: white;
    box-shadow: 4px 4px 26px rgba(213, 211, 211, 0.3);
    border-radius: 1rem;
    z-index: 225;

    .content-header {
      padding: 0 3rem 0 4.15rem;
      border-top-left-radius: 1rem;
      border-top-right-radius: 1rem;
      background-color: #34b7f1;
      height: 8.9rem;
      display: flex;
      flex: none;
      justify-content: space-between;
      align-items: center;
      color: white;

      .close-button {
        font-size: 5rem;
        opacity: 1;
        // margin-right: 15px;
        color: #fff;
        cursor: pointer;
        border: none;
        background: none;
      }

      .heading {
        font-size: 2.5rem;
        line-height: 3.7rem;
        font-weight: 500;
        color: #fff;
      }
    }

    .search-bar {
      // height: 79px;
      // background-color: #ededed;
      margin: 3rem;
      position: relative;

      input {
        width: 100%;
        height: 6rem;
        background: #fff;
        backdrop-filter: blur(50px);
        border-radius: 6rem;
        padding: 1.8rem 8.2rem 1.8rem 5.2rem;
        font-size: 2rem;
        line-height: 2.4rem;
        font-weight: 400;
        color: rgba(156, 166, 175, 0.6);
        // margin-right: 4.4rem;
        outline: none;
        border: 0.3rem solid #d2d2d2;
        box-shadow: none;

        &::placeholder {
          color: rgba(156, 166, 175, 0.6);
        }
      }

      img {
        position: absolute;
        width: 2.2rem;
        left: 2rem;
        top: 50%;
        z-index: 2;
        transform: translateY(-50%);
      }

      .clear-search-icon {
        cursor: pointer;
        color: rgba(134, 150, 160, 0.87);
        font-size: 30px;
        position: absolute;
        top: 50%;
        right: 3rem;
        transform: translateY(-50%);
      }
    }

    .chat-list {
      width: 100%;
      height: 50.1rem;
      overflow-y: scroll;
      padding: 0 5rem;
      position: relative;

      &::-webkit-scrollbar-thumb {
        background: #34b7f1;
        border-radius: 30px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: darken(#34b7f1, 10%);
      }

      .chat-item {
        padding: 2.5rem 0;
        display: flex;
        align-items: center;
        // border-bottom: 1px solid #f2f2f2;

        // &:first-of-type {
        // 	border-top: 1px solid #f2f2f2;
        // }

        input {
          margin-right: 5px;
        }

        .avatar-image {
          width: 7rem;
          height: 7rem;
          border-radius: 50%;
          margin: 0 3rem;
          background-color: #ddd;
          background-size: cover;
          background-position: 50%;
          background-repeat: no-repeat;
        }

        .username {
          font-weight: 400;
          font-size: 2.5rem;
          line-height: 3.7rem;

          color: #000;
        }
      }
    }

    .chat-list.with-footer {
      height: 41.2rem;
    }

    .footer {
      position: relative;
      height: 8.9rem;
      width: 100%;
      background-color: #34b7f1;
      display: flex;
      align-items: center;

      .name-list {
        padding-right: 10.8rem;
        padding-left: 2.4rem;
        color: #fff;
        overflow: hidden;
        font-size: 2.5rem;
        line-height: 3.7rem;
        font-weight: 400;
        color: #fff;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .send-icon {
        background: #6a6a6a;
        width: 8.9rem;
        height: 8.9rem;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        display: flex;
        position: absolute;
        right: 4rem;
        top: -4rem;
        cursor: pointer;
        transition: all 0.2s;
        z-index: 1000;

        &:hover {
          transform: scale(1.1);
        }

        img {
          width: 50%;
          transform: translate(2px, 1px);
        }
      }
    }
  }

  &_overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.85);
    z-index: 190;
  }

  &_warning {
    z-index: 225;
    font-size: 13px;
    background: rgba(0, 0, 0, 0.8);
    position: absolute;
    color: white;
    bottom: 10px;
    left: 10px;
    padding: 12px 15px;
    border-radius: 3px;
  }
}

@media only screen and (max-width: 1919px) {
  .forward-modal {
    &_content {
      .search-bar {
        input {
          height: 6rem;
          font-size: 14px;
        }
      }
    }
  }
}
