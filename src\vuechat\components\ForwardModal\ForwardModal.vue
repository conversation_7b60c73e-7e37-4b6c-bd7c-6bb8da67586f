<template>
  <div class="forward-modal">
    <div class="forward-modal_content">
      <div class="content-header">
        <span class="heading">Forward message to</span>
        <button class="close-button" @click.prevent="close">&times;</button>
      </div>
      <div class="search-bar">
        <input type="input" placeholder="Search..." :value="searchText" @input="handleSearch" />
        <img :src="searchIcon" alt="Search Icon" />
        <span v-if="searchText" class="clear-search-icon" @click.prevent="handleSearch({ target: { value: '' } })">
          &times;
        </span>
      </div>
      <div id="chats-list" class="chat-list" :class="{ 'with-footer': showFooter }">
        <div v-for="room in filteredRooms" :key="room.roomId" class="chat-item">
          <label class="custom-checkbox forward" :class="{ checked: room.selected }">
            <input type="checkbox" class="label-check" :checked="room.selected" @change="handleSelect(room)" />
            <span class="checkmark" />
          </label>
          <div
            class="avatar-image"
            :style="{
              'background-image': `url('${room.avatar || dummyAvatar}')`
            }"
          />

          <div class="username">
            {{ room.roomName }}
          </div>
        </div>

        <transition name="vac-fade-message">
          <div v-if="rooms.length && !loadingRooms" id="infinite-loader-chats" class="mt-4">
            <loader :show="showLoader" :infinite="true" />
          </div>
        </transition>
      </div>

      <div v-if="showFooter" class="footer">
        <div class="name-list">
          {{ chatString }}
        </div>
        <div class="send-icon" @click.prevent="sendForwardMessage">
          <slot name="send-icon">
            <img :src="sendIcon" alt="Send Icon" />
          </slot>
        </div>
      </div>
    </div>
    <div class="forward-modal_overlay" />
    <div v-if="warning" class="forward-modal_warning">You can only share with up to 5 chats</div>
  </div>
</template>

<script>
import Loader from '../Loader/Loader'
import DummyAvatar from '../../components/PngIcons/profile-placeholder.png'
import SearchIcon from '../PngIcons/search_icon.png'
// import SendIcon from '../../components/PngIcons/send_icon.png'
import SendIcon from '../../components/SvgIcon/send_icon.svg'
export default {
  name: 'ForwardModal',
  components: { Loader },
  props: {
    initialRooms: { type: Array, required: true },
    close: { type: Function, required: true },
    loadingRooms: { type: Boolean, required: true },
    roomsLoaded: { type: Boolean, required: true }
  },
  emits: ['send-forward-message', 'loading-more-rooms', 'fetch-more-rooms'],
  data() {
    return {
      sendIcon: SendIcon,
      searchIcon: SearchIcon,
      rooms: this.initialRooms.map(el => ({
        ...el,
        selected: false
      })),
      dummyAvatar: DummyAvatar,
      searchText: '',
      chats: [],
      chatIds: [],
      limit: 5,
      selectCount: 0,
      warning: false,
      showLoader: true,
      loadingMoreRooms: false,
      loadingRoomLocal: true,
      observer: null
    }
  },
  computed: {
    showFooter() {
      return this.rooms.some(el => el.selected)
    },
    filteredRooms() {
      return this.rooms.filter(room => {
        return (
          room.roomName.toLowerCase().includes(this.searchText) || room.phone.toLowerCase().includes(this.searchText)
        )
      })
    },
    chatString() {
      return this.chats.join(', ')
    }
  },

  watch: {
    initialRooms: {
      deep: true,
      handler(newVal, oldVal) {
        const rooms = [...this.rooms]
        newVal.forEach((el, i) => {
          if (i >= oldVal.length) {
            rooms.push({ ...el, selected: false })
          }
        })
        this.rooms = rooms
        if (newVal.length !== oldVal.length || this.roomsLoaded) {
          this.loadingMoreRooms = false
        }
      }
    },
    loadingRooms(val) {
      this.loadingRoomLocal = val
    },
    loadingRoomLocal(val) {
      if (!val) {
        setTimeout(() => this.initIntersectionObserver())
      }
    },
    loadingMoreRooms(val) {
      this.$emit('loading-more-rooms', val)
    },
    roomsLoaded: {
      immediate: true,
      handler(val) {
        if (val) {
          this.loadingMoreRooms = false
          if (!this.loadingRooms) {
            this.showLoader = false
          }
        }
      }
    }
  },

  created() {
    this.loadingRoomLocal = false
  },

  methods: {
    initIntersectionObserver() {
      if (this.observer) {
        this.showLoader = true
        this.observer.disconnect()
      }

      const loader = document.getElementById('infinite-loader-chats')
      if (loader) {
        const options = {
          root: document.getElementById('chats-list'),
          rootMargin: '60px',
          threshold: 0
        }

        this.observer = new IntersectionObserver(entries => {
          if (entries[0].isIntersecting) {
            this.loadMoreRooms()
          }
        }, options)

        this.observer.observe(loader)
      }
    },
    loadMoreRooms() {
      if (this.loadingMoreRooms || this.searchText) return

      if (this.roomsLoaded) {
        this.loadingMoreRooms = false
        return (this.showLoader = false)
      }

      this.$emit('fetch-more-rooms')
      this.loadingMoreRooms = true
    },
    handleSelect(room) {
      const limitExceed = this.selectCount >= this.limit
      // const index = this.rooms.findIndex(el => el.roomId === room.roomId)

      if (limitExceed) {
        if (room.selected) {
          room.selected = false
          this.removeChats(room)
        } else {
          this.showWarning()
        }

        return
      }

      room.selected = !room.selected
      if (!room.selected) {
        this.removeChats(room)
      } else {
        this.addChats(room)
      }
    },

    addChats(room) {
      this.selectCount++
      this.chats.unshift(room.roomName)
      this.chatIds.unshift(room.roomId)
    },

    removeChats(room) {
      this.selectCount--
      if (this.chats === room.roomName) {
        this.chats = []
        this.chatIds = []
        return
      }

      const chatsIdx = this.chats.findIndex(name => name === room.roomName)
      this.chats.splice(chatsIdx, 1)

      const idx = this.chatIds.findIndex(id => id === room.roomId)
      this.chatIds.splice(idx, 1)
    },

    showWarning() {
      if (!this.warning) {
        this.warning = true

        setTimeout(() => (this.warning = false), 3000)
      }
    },

    sendForwardMessage() {
      const ids = this.chatIds.join(',')
      this.$emit('send-forward-message', ids)
    },

    handleSearch({ target: { value } }) {
      this.searchText = value

      if (value) {
        this.showLoader = false
      } else {
        if (!this.roomsLoaded) {
          this.showLoader = true
        }
      }
    }
  }
}
</script>
