.profile_header {
  height: 10rem !important;
  padding: 0 30px !important;
}

#hubspotContent {
  background: #fff;
}

.hs-icon-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid rgb(210, 209, 209);
}

.view-contact {
  font-size: 20px;
  font-weight: 500;
  cursor: pointer;
}

.hs-icon-container img {
  width: 48px;
  height: 48px;
  cursor: pointer;
}

.iconsList {
  padding: 18px 0;
}

.nav-justified-right-sidebar {
  list-style: none;
  display: flex;
  padding: 0;
  justify-content: space-between;
}

#tabContainer .container {
  padding: 0;
}

.nav-link-right-sidebar {
  padding: 17px 9px;
  border-radius: 50%;
}

.tab-content-group-value-email > div {
  display: flex;
  gap: 2.9rem;
}

.heading-number {
  margin: 0%;
  text-align: center;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 23px;
}

.activeRightSidebar {
  background: rgba(2, 168, 132, 0.2);
  padding: 19px 9px !important;
}

.create-tab-modal_overlay {
  background-color: #3434341a;
}

.tab-body {
  background: #f0f2f4;
  padding: 11px 23px 11px 18px;
  height: calc(100vh - 24rem - 150px);
  overflow-y: scroll;
}

.tab-content-group-left {
  width: 100%;
  position: relative;
}

.name-right-sidebar {
  font-size: 20px;
  font-weight: 400;
}

.phone-right-sidebar {
  font-size: 18px;
}

.content-email {
  color: #1587ba !important;
  font-size: 18px !important;
}

.tab-content-group {
  display: flex;
  align-items: center;
  padding: 12px 0;
}

.heading-div,
.tab-content-group-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* position: relative; */
}

.heading-div {
  width: 100%;
}

.tab-content-group-value span,
.heading-span {
  font-size: 13px;
  font-weight: 700;
  color: #50616c;
  cursor: pointer;
  margin-right: 30px;
}

.content-value-right-sidebar {
  font-size: 14px !important;
  font-weight: 400 !important;
}

.pencil-right-bar {
  width: 12.15px;
  height: 12.15px;
}

.save-right-bar {
  position: absolute !important;
  right: 3px !important;
  width: 15px;
  height: 15px;
  margin-top: 16px;
  cursor: pointer;
}

.profile-heading {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
}

.add-contact-hubspot {
  text-align: center;
  margin-top: 32px;
}

.add-contact-to-hubspot {
  font-size: 20px;
  font-weight: 600;
  text-decoration: none;
  background: #ff725e;
  padding: 12px 34px;
  border-radius: 4px;
  color: #fff !important;
}

.add-contact-to-hubspot:hover {
  text-decoration: none;
}

.readOnlyInput {
  width: 85% !important;
  font-size: 16px !important;
  padding: 7px 12px !important;
  margin-top: 9px;
  border: 2px solid gray !important;
}

.dropdown-right-sidebar {
  font-size: 16px !important;
  color: #86a0bc !important;
  padding: 7px 9px;
  font-weight: 500;
  width: 85% !important;
  margin-top: 8px !important;
}

.heading-span img {
  width: 31.5px;
  height: 31.5px;
}

::-webkit-scrollbar-thumb {
  background: #007bff;
}

.notes-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 50%;
}

.notes-group label {
  font-size: 16px;
  font-weight: 500;
}

.profile-heading img {
  width: 36px !important;
  height: 36px !important;
  cursor: pointer;
}

.select-dropdown {
  width: 100%;
}

.inner-loader {
  position: initial;
}

.content-box {
  background: white;
  border-radius: 5px;
  padding: 5px 15px;
  margin: 5px 0;
  box-shadow: #dddddd 0px 1px 3px 1px;
}

.expand-button {
  border: 1px solid gray;
  color: white;
  background-color: #d1cbcb;
  font-size: 15px;
  border-radius: 50%;
  padding: 0 7px;
}

.tab-content-group-title {
  margin-bottom: 10px;
}

.tab-content-group-value-email {
  /* position: relative; */
}

img.cross-right-bar {
  width: 15px;
  height: 15px;
  position: absolute;
  right: 29px;
  margin-top: 16px;
  cursor: pointer;
}

.update-loader .loader {
  background: none !important;
  position: absolute;
}

.update-loader .loader .loader-inner {
  position: absolute;
  right: 0;
}

.val-tab-name {
  font-size: 13px;
  font-weight: 700;
  margin-left: 1rem;
}

.view-contact img {
  width: 12px;
  height: 12px;
  margin-left: 5px;
}
