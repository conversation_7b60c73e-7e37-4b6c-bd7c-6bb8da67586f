<template>
  <div class="rightsidebar">
    <section id="hubspotContent">
      <div class="hs-icon-container rightSideHeading">
        <img
          :src="hubspotIcon"
          class="hs-right-sidebar"
          alt="Hubspot Icon"
          height="30"
          title="Open Details"
          @click.stop="openHubspot"
        />
        <div @click.stop="openHubspot" class="view-contact">
          <span>View contact in HubSpot</span>
          <img :src="externalIcon" alt="" />
        </div>
      </div>
      <div class="iconsList">
        <div id="tabContainer" class="container">
          <ul id="myTabs" class="nav-justified-right-sidebar" role="tablist" data-bs-tabs="tabs">
            <li class="nav-item-right-sidebar" role="presentation">
              <a
                href="#contacts"
                data-bs-toggle="tab"
                class="nav-link-right-sidebar"
                :class="{ activeRightSidebar: defaultActive }"
                @click="openTab('contacts')"
              >
                <img
                  :src="profileIcon"
                  class="icon-right-sidebar"
                  alt="Hubspot Icon"
                  height="30"
                  title="Contact Information"
                />
              </a>
            </li>
            <li class="nav-item-right-sidebar" role="presentation">
              <a
                href="#companies"
                data-bs-toggle="tab"
                class="nav-link-right-sidebar"
                :class="{ activeRightSidebar: isCompanyActive }"
                @click="openTab('companies')"
              >
                <img
                  :src="companyIcon"
                  class="icon-right-sidebar"
                  alt="Hubspot Icon"
                  height="30"
                  title="Company Information"
                />
              </a>
            </li>
            <li class="nav-item-right-sidebar" role="presentation">
              <a
                href="#tasks"
                data-bs-toggle="tab"
                class="nav-link-right-sidebar"
                :class="{ activeRightSidebar: isTaskActive }"
                @click="openTab('tasks')"
              >
                <img :src="taskIcon" class="icon-right-sidebar" alt="Hubspot Icon" height="30" title="Tasks" />
              </a>
            </li>
            <li class="nav-item-right-sidebar" role="presentation">
              <a
                href="#notes"
                data-bs-toggle="tab"
                class="nav-link-right-sidebar"
                :class="{ activeRightSidebar: isActive }"
                @click="openTab('notes')"
              >
                <img :src="notesIcon" class="icon-right-sidebar" alt="Hubspot Icon" height="30" title="Notes" />
              </a>
            </li>
            <li class="nav-item-right-sidebar" role="presentation">
              <a
                href="#bag"
                data-bs-toggle="tab"
                class="nav-link-right-sidebar"
                :class="{ activeRightSidebar: isDealsActive }"
                @click="openTab('deals')"
              >
                <img :src="bagIcon" class="icon-right-sidebar" alt="Hubspot Icon" height="30" title="Deals" />
              </a>
            </li>
            <li class="nav-item-right-sidebar" role="presentation">
              <a
                href="#ticket"
                data-bs-toggle="tab"
                class="nav-link-right-sidebar"
                :class="{ activeRightSidebar: isTicketsActive }"
                @click="openTab('tickets')"
              >
                <img :src="ticketIcon" class="icon-right-sidebar" alt="Hubspot Icon" height="30" title="Tickets" />
              </a>
            </li>
          </ul>
        </div>
      </div>
    </section>
    <div class="tab-body">
      <loader :show="loadingTab" />
      <div v-if="!loadingTab" class="tab-content">
        <!-- Common Deals/Tickets/Company/Notes/Task -->
        <div id="common" role="tabpanel" class="tab-pane-right-sidebar">
          <error-modal :toggle="toggleErrorModal" :show="showError" :error-message="errorWithMessage" />
          <div class="right-side-bar-loader">
            <loader :show="loadingTab" />
          </div>
          <success-modal :show="successModel" :toggle="toggleSuccessModal" :success-message="successModelMessage" />
          <!-- started loop for values -->
          <div v-for="(val, key, index) in engagement" :key="index">
            <div v-if="index === 0" class="tab-content-group-title">
              <div class="heading-div">
                <span class="heading-span">{{ val.tab.toUpperCase() }}</span>
                <span
                  v-if="val.tab === 'notes' || val.tab === 'tickets' || val.tab === 'tasks' || val.tab === 'deals'"
                  class="heading-span"
                >
                  <img
                    :src="addButtonIcon"
                    alt="Add Button"
                    height="30"
                    title="Add button"
                    @click="addButtonTab(val.tab)"
                  />
                </span>
              </div>
            </div>
            <div
              :class="{
                'content-box':
                  (val.tab === 'notes' || val.tab === 'tickets' || val.tab === 'tasks' || val.tab === 'deals') &&
                  val.edit_options.length !== 0
              }"
            >
              <div v-for="(valOptions, keyOptions, indexOptions) in val.edit_options" :key="indexOptions">
                <div class="tab-content-group">
                  <div class="tab-content-group-left">
                    <div
                      v-if="index === 0 && indexOptions === 0 && val.tab === 'contacts'"
                      class="username-right-sidebar"
                    >
                      <span class="name-right-sidebar"
                        >{{ val.edit_options['firstname'].value }} {{ val.edit_options['lastname'].value }}</span
                      >
                      <span class="phone-right-sidebar"> ( {{ phone }} ) </span>
                    </div>
                    <div v-if="valOptions.label !== 'Email'" class="tab-content-group-value">
                      <span>{{ valOptions.label }}</span>
                    </div>
                    <div class="tab-content-group-value">
                      <span
                        v-if="valOptions.editIconButton && valOptions.label === selectedDropDown.label"
                        class="content-value-right-sidebar"
                      >
                        {{ selectedDropDown.value || valOptions.value }}
                      </span>

                      <span
                        v-else-if="valOptions.editIconButton && valOptions.label === PipelineDropDown.label"
                        class="content-value-right-sidebar"
                      >
                        {{ PipelineDropDown.value || valOptions.value }}
                      </span>

                      <span
                        v-else-if="valOptions.editIconButton"
                        v-html="valOptions.value"
                        class="content-value-right-sidebar"
                        :class="{
                          'content-email': valOptions.label === 'Email'
                        }"
                      />
                      <div class="tab-content-right edit">
                        <img
                          v-if="
                            valOptions.editIconButton === true &&
                            valOptions.ready_only === false &&
                            (saveKey === false || setKey !== keyOptions)
                          "
                          :src="editIcon"
                          alt=""
                          class="pencil-right-bar"
                          @click.prevent="editField(key, false, true, val.tab, keyOptions)"
                        />

                        <img
                          v-if="valOptions.saveButton === true && valOptions.dropdownCheck === true"
                          :src="saveIcon"
                          data-targetInput="userName"
                          alt="save icon"
                          class="save-right-bar"
                          width="18"
                          height="18"
                          @click="
                            UpdateEngagement(val.id, valOptions.actual_name, valOptions.value, key, val.tab, keyOptions)
                          "
                        />
                        <!-- <div class="update-loader"> -->

                        <div
                          v-if="saveKey === true && setKey === keyOptions && setMainKey === key"
                          class="update-loader"
                        >
                          <loader :show="true" />
                        </div>
                      </div>
                    </div>
                    <div class="tab-content-group-value-email">
                      <div class="edit-container">
                        <div v-if="valOptions.label === 'Pipeline' && valOptions.saveButton" class="select-dropdown">
                          <select
                            v-model="PipelineDropDown"
                            class="dropdown-right-sidebar"
                            @change="
                              UpdateEngagement(
                                val.id,
                                valOptions.actual_name,
                                PipelineDropDown.id,
                                key,
                                val.tab,
                                keyOptions
                              )
                            "
                          >
                            <option
                              v-for="(valuePipelineOption, keyPipelineOption, indexPipelineOption) in val.pipelines"
                              :key="indexPipelineOption"
                              :value="{
                                id: valuePipelineOption.id,
                                value: valuePipelineOption.label,
                                label: 'Pipeline'
                              }"
                            >
                              {{ valuePipelineOption.label }}
                            </option>
                          </select>
                        </div>

                        <div
                          v-else-if="valOptions.label === 'DealStage' && valOptions.saveButton"
                          class="select-dropdown"
                        >
                          <select
                            v-model="selectedDropDown"
                            class="dropdown-right-sidebar"
                            @change="
                              UpdateEngagement(
                                val.id,
                                valOptions.actual_name,
                                selectedDropDown.id,
                                key,
                                val.tab,
                                keyOptions
                              )
                            "
                          >
                            <option
                              v-for="(valueStageOption, keyStageOption, indexStageOption) in valOptions.selectedStage"
                              :key="indexStageOption"
                              :value="{
                                id: valueStageOption.id,
                                value: valueStageOption.label,
                                label: 'DealStage'
                              }"
                            >
                              {{ valueStageOption.label }}
                            </option>
                          </select>
                        </div>

                        <textarea
                          v-else-if="
                            valOptions.saveButton === true &&
                            valOptions.field_type === 'textarea' &&
                            valOptions.dropdownCheck === true
                          "
                          id="userName"
                          v-model="valOptions.value"
                          :type="valOptions.field_type"
                          placeholder="body"
                          class="form-control readOnlyInput editable"
                          @keydown.enter="
                            UpdateEngagement(val.id, valOptions.actual_name, valOptions.value, key, val.tab, keyOptions)
                          "
                        />

                        <input
                          v-else-if="valOptions.saveButton === true && valOptions.dropdownCheck === true"
                          id="userName"
                          v-model="valOptions.value"
                          :type="valOptions.field_type"
                          placeholder="body"
                          class="form-control readOnlyInput editable"
                          @keydown.enter="
                            UpdateEngagement(val.id, valOptions.actual_name, valOptions.value, key, val.tab, keyOptions)
                          "
                        />

                        <div v-else-if="valOptions.options !== '' && valOptions.saveButton" class="select-dropdown">
                          <select
                            v-model="valOptions.value"
                            class="dropdown-right-sidebar"
                            @change="
                              UpdateEngagement(
                                val.id,
                                valOptions.actual_name,
                                valOptions.value,
                                key,
                                val.tab,
                                keyOptions
                              )
                            "
                          >
                            <option
                              v-for="(valueDropOption, keyDropOption, indexDropOption) in valOptions.options"
                              :key="indexDropOption"
                              :value="valueDropOption"
                            >
                              {{ keyDropOption }}
                            </option>
                          </select>
                        </div>

                        <img
                          v-if="valOptions.saveButton === true"
                          :src="crossIcon"
                          data-targetInput="userName"
                          alt="cancel"
                          class="cross-right-bar"
                          width="18"
                          height="18"
                          @click="CloseEdit(key, val.tab, keyOptions)"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-if="
                  (index === 0 && val.tab === 'notes') ||
                  val.tab === 'tickets' ||
                  val.tab === 'tasks' ||
                  val.tab === 'deals'
                "
              >
                <create-properties-modal
                  :toggle="toggleCreateModal"
                  :show="showModal"
                  :tab="val.tab"
                  :show-tab-name="showTabName"
                  :owner-id="val.ownerId"
                  :first-name="val.firstname"
                  :last-name="val.lastname"
                  :hs-pipeline="val.pipelines"
                  :hs-ticket-priority="val.priority_options"
                  :task-type="val.task_type_options"
                  :task-status="val.task_status"
                  :owner="val.owner"
                  @AddNewEngagement="AddNewEngagement"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Loader from '../../components/Loader/InnerLoader'
import Profile from '../../components/PngIcons/information_icon.svg'
import Notes from '../../components/PngIcons/note_edit.svg'
import Ticket from '../../components/PngIcons/agent.svg'
import SuccessModal from '../../components/SuccessModal/SuccessModal'
import Task from '../../components/PngIcons/carbon_task.svg'
import Bag from '../../components/PngIcons/suitcase.svg'
import HubspotIcon from '../../components/PngIcons/hubspot_icon.png'
import SaveIcon from '../../components/PngIcons/save.png'
import EditIcon from '../../components/PngIcons/edit_icon.png'
import CrossIcon from '../../components/PngIcons/cross_icon.png'
import AddButton from '../../components/PngIcons/add_button_icon.png'
import Company from '../../components/PngIcons/company_icon.png'
import externalIcon from '../../components/PngIcons/external_icon1.svg'
import ErrorModal from '../../components/ErrorModal/ErrorModal'
import CreatePropertiesModal from '../../components/CreatePropertiesModal/CreatePropertiesModal'
import './HubspotSideBar.css'

export default {
  name: 'HubspotSideBar',
  components: {
    Loader,
    ErrorModal,
    CreatePropertiesModal,
    SuccessModal
  },
  props: {
    phone: { type: String, required: true },
    engagement: { type: Object, default: null },
    loadingTab: { type: Boolean, required: false },
    showErrorModal: { type: Boolean, required: true },
    showCreateModal: { type: Boolean, required: false },
    errorMessage: { type: String, default: '' },
    toggleSuccessModal: { type: Function, required: true },
    showSuccessModal: { type: Boolean, required: true },
    room: { type: Object, required: true },
    saveKey: { type: Boolean, required: false },
    successMessage: {
      type: Object,
      default: () => ({ heading: 'Success', content: 'Successful!' })
    }
  },
  emits: ['redirect-to-hubspot'],
  data() {
    return {
      externalIcon: externalIcon,
      hubspotIcon: HubspotIcon,
      addButtonIcon: AddButton,
      profileIcon: Profile,
      companyIcon: Company,
      ticketIcon: Ticket,
      notesIcon: Notes,
      taskIcon: Task,
      bagIcon: Bag,
      editIcon: EditIcon,
      crossIcon: CrossIcon,
      saveIcon: SaveIcon,
      isActive: false,
      isCompanyActive: false,
      isDealsActive: false,
      isTicketsActive: false,
      isTaskActive: false,
      defaultActive: true,
      loader: this.loadingTab,
      selectedTab: false,
      showError: this.showErrorModal,
      selectedDropDown: {},
      PipelineDropDown: {},
      showModal: this.showCreateModal,
      errorWithMessage: this.errorMessage,
      successModel: this.showSuccessModal,
      successModelMessage: this.successMessage,
      showTabName: '',
      setKey: '',
      setMainKey: ''
    }
  },
  beforeMount() {
    this.getContactProperties()
  },
  methods: {
    toggleErrorModal() {
      this.showError = !this.showError
    },
    toggleCreateModal() {
      this.showModal = !this.showModal
    },
    getContactProperties() {
      this.$emit('getEngagement', 'contacts')
    },
    editField(key, setTrue = '', setFalse = '', tab = '', childKey = '') {
      try {
        this.$set(this.engagement[key]['edit_options'], childKey, {
          ...this.engagement[key]['edit_options'][childKey],
          editIconButton: setTrue || false,
          saveButton: setFalse === '' ? true : setFalse
        })
      } catch (err) {
        console.log(err.message)
      }
    },
    UpdateEngagement(field, actualName, variable, key, tab, childKey = '') {
      // id, pipeline, 21321, 0, deals, 4
      try {
        this.setKey = childKey
        this.setMainKey = key
        // set the dropdown value for deal option
        if (actualName === 'pipeline' || actualName === 'hs_pipeline') {
          console.log(variable + ' ' + key)
          this.setDropDown(variable, key)
        }
        // emitting
        this.$emit('UpdateEngagement', variable, field, actualName, tab)
        this.editField(key, true, false, tab, childKey)
      } catch (err) {
        console.log(err.message)
      }
    },
    CloseEdit(key, tab, childKey = '') {
      try {
        this.editField(key, true, false, tab, childKey)
      } catch (err) {
        console.log(err.message)
      }
    },
    // setting dropdown with pipeline, stage changes
    setDropDown(variable, key) {
      try {
        this.currentForm = this.engagement[key].pipelines[variable].stages

        //	setting up the second dropdown
        if (this.engagement[key]['edit_options']['dealstage']) {
          this.$set(this.engagement[key]['edit_options'], 'dealstage', {
            ...this.engagement[key]['edit_options']['dealstage'],
            selectedStage: this.currentForm
          })
        } else {
          this.$set(this.engagement[key]['edit_options'], 'hs_pipeline_stage', {
            ...this.engagement[key]['edit_options']['hs_pipeline_stage'],
            selectedStage: this.currentForm
          })
        }
      } catch (err) {
        console.log(err.message)
      }
    },
    AddNewEngagement(submitForm, tab, ownerId, associations) {
      this.showModal = false
      this.$emit('AddNewEngagement', submitForm, tab, ownerId, associations)
    },
    openTab(tab) {
      this.defaultActive = false
      this.isActive = false
      this.isDealsActive = false
      this.isTicketsActive = false
      this.isTaskActive = false
      this.loader = true
      this.selectedTab = false
      this.isCompanyActive = false
      this.selectedDropDown = {}
      this.PipelineDropDown = {}

      this.$emit('getEngagement', tab)
      // tab active state
      if (tab === 'contacts') {
        this.defaultActive = true
      } else if (tab === 'notes') {
        this.isActive = true
      } else if (tab === 'deals') {
        this.isDealsActive = true
      } else if (tab === 'tickets') {
        this.isTicketsActive = true
      } else if (tab === 'tasks') {
        this.isTaskActive = true
      } else if (tab === 'companies') {
        this.isCompanyActive = true
      }
      tab = tab.substring(0, tab.length - 1)
      this.showTabName = tab.toUpperCase()
    },
    addButtonTab(tab) {
      this.showModal = true
    },
    openHubspot() {
      this.$emit('redirect-to-hubspot', this.room)
    }
  }
}
</script>
