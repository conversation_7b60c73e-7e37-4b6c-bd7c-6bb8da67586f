.image-preview {
  width: 100%;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
  background: white;

  &-topbar {
    width: 100%;
    height: 10vh;
    border-bottom: 1px solid #e9e9e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;

    .image-details {
      display: flex;
      flex-direction: column;

      .image-name {
        font-size: 15px;
        font-weight: 500;
        margin-bottom: 2px;
      }

      .image-time {
        font-size: 11px;
        font-weight: 400;
      }
    }

    svg {
      cursor: pointer;
    }

    &_close {
      //  position: relative;
      // top: 10px;
      // right: 20px;
      margin-left: 10px;
    }
    .popover__content {
      opacity: 0;
      visibility: hidden;
      position: absolute;

      transform: translate(-0.8rem, 0px);
      /* background-color: #f1f1f1; */
      /* padding: 0.8rem; */
      /* box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26); */
      width: 10rem;
      border-radius: 3px;
      padding-top: 0px;
      border: 1px solid #dedede;
      padding: 0.5rem 0.8rem;
    }

    &_close:hover .popover__content {
      z-index: 10;
      opacity: 1;
      right: 0px;
      visibility: visible;
      transform: translate(-0.8rem, 0.7rem);
      transition: opacity 0.5s cubic-bezier(0.75, -0.02, 0.2, 0.97);
    }
    .popover__message {
      font-size: 1.8rem;
      color: #040000;
      font-weight: 500;
      margin-bottom: 0px;
    }

    .forward-icon {
      margin-right: 15px;
    }
    .forward-icon:hover .popover__content {
      z-index: 10;
      opacity: 1;
      margin-top: 5px;
      visibility: visible;
      transform: translate(-0.8rem, 0.7rem);
      transition: opacity 0.5s cubic-bezier(0.75, -0.02, 0.2, 0.97);
    }
    .download-icon:hover .popover__content {
      z-index: 10;
      opacity: 1;
      margin-top: 5px;
      visibility: visible;
      transform: translate(-0.8rem, 0.7rem);
      transition: opacity 0.5s cubic-bezier(0.75, -0.02, 0.2, 0.97);
    }
  }

  &-main {
    padding: 10px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;

    .carousel-img {
      position: relative;
    }

    .carousel-img > img {
      display: block;
      max-width: 90%;
      margin: 0 auto;
    }
  }
}
