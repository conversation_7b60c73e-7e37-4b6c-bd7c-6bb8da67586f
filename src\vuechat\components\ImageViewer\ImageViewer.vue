<template>
  <div v-if="show" class="image-preview">
    <!-- topbar -->
    <div class="image-preview-topbar">
      <div class="image-details">
        <span class="image-name">{{ msg.username }}</span>
        <span class="image-time">{{ imageTime }}</span>
      </div>
      <div class="d-flex align-items-center">
        <!-- <div class="forward-icon" @click="$emit('open-forward-modal', msg)">
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path
              fill="currentColor"
              d="M14.278 4.813c0-.723.873-1.085 1.383-.574l6.045 6.051a.81.81 0 0 1 0 1.146l-6.045 6.051a.81.81 0 0 1-1.383-.574v-2.732c-5.096 0-8.829 1.455-11.604 4.611-.246.279-.702.042-.602-.316 1.43-5.173 4.925-10.004 12.206-11.045V4.813z"
            />
          </svg>
          <div class="popover__content">
            <p class="popover__message">Forward</p>
          </div>
        </div> -->
        <div class="download-icon" @click="openFile">
          <svg-icon name="document" />
          <div class="popover__content">
            <p class="popover__message">Download</p>
          </div>
        </div>
        <div class="image-preview-topbar_close" @click="close">
          <svg-icon name="close" />
          <div class="popover__content">
            <p class="popover__message">Close</p>
          </div>
        </div>
      </div>
    </div>

    <div class="image-preview-main">
      <div class="content">
        <div class="carousel-img">
          <img :src="msg.url" :alt="msg.name" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SvgIcon from '../../components/SvgIcon/SvgIcon'
export default {
  name: 'ImageViewer',
  components: { SvgIcon },
  props: {
    show: { type: Boolean },
    close: { type: Function, default: () => ({}) },
    msg: { type: Object, required: true }
  },
  emits: ['open-forward-modal'],
  computed: {
    imageTime() {
      return this.msg.date + ' at ' + this.msg.timestamp
    }
  },

  methods: {
    async openFile() {
      const a = document.createElement('a')
      a.href = this.msg.url
      a.target = '_blank'
      a.download = this.msg.url.split('/').pop()
      console.log(a.download)
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)

      // let msg = this.msg;
      // const image = await fetch(this.msg.url, { mode: "no-cors" })
      //   .then((response) => {
      //     response.blob().then(function (myBlob) {
      //       var objectURL = URL.createObjectURL(myBlob);
      //       const link = document.createElement("a");
      //       link.href = URL.createObjectURL(myBlob);
      //       link.download = msg.url.split("/").pop();
      //       link.click();
      //       URL.revokeObjectURL(link.href);
      //     });
      //   })
      //   .catch((error) => console.log(error));

      // const blob = await image.blob();
      // const link = document.createElement("a");
      // link.href = URL.createObjectURL(blob);
      // link.download = this.msg.name + "." + this.msg.extension;
      // link.click();
      // URL.revokeObjectURL(link.href);
    }
  }
}
</script>

<style lang="scss" scoped>
@import './ImageViewer.scss';
</style>
