.info-modal {
  position: absolute;
  top: 20rem;
  transform: translate(50%, -50%);
  right: 37%;
  z-index: 1;
  width: 60%;
  cursor: context-menu;

  .info-modal__container {
    border: 1px solid #126cd6;
    background: #dae9fb;
    border-radius: 3px;
    padding: 3rem;
    display: flex;
    gap: 2rem;
    align-items: flex-start;
  }

  img {
    width: 4rem;
    height: 4rem;
  }
}

.room-container {
  position: relative;
}
