.labels-modal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 68.7rem;
  // height: 70rem;
  max-height: 70vh;
  background-color: white;
  // padding: 3.5rem 5rem;
  border-radius: 1rem;
  // box-shadow: 0px 4px 20px rgba(169, 170, 181, 0.25);
  box-shadow: 4px 4px 26px rgba(213, 211, 211, 0.3);
  z-index: 100;

  &_close {
    position: absolute;
    top: 0;
    right: 2rem;
    font-size: 3rem;
    color: #333;
    cursor: pointer;
    border: none;
    background: none;
  }

  &_overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    // backdrop-filter: blur(2px);
    z-index: 99;
  }
}

.label-search-modal {
  // form
  .search-group.custom {
    position: relative;
    margin: 3rem 3rem 3.5rem 3rem;
    // margin-right: 0;

    input {
      width: 100%;
      height: 6rem;
      background: #fff;
      backdrop-filter: blur(50px);
      border-radius: 6rem;
      padding: 1.8rem 8.2rem 1.8rem 5.2rem;
      font-size: 2rem;
      line-height: 2.4rem;
      font-weight: 400;
      color: rgba(156, 166, 175, 0.6);
      // margin-right: 4.4rem;
      outline: none;
      border: 0.3rem solid #d2d2d2;
      box-shadow: none;

      &::placeholder {
        color: rgba(156, 166, 175, 0.6);
      }
    }

    img {
      position: absolute;
      width: 2.2rem;
      left: 2rem;
      top: 50%;
      z-index: 2;
      transform: translateY(-50%);
    }

    .clear-search-icon {
      cursor: pointer;
      color: rgba(134, 150, 160, 0.87);
      font-size: 30px;
      position: absolute;
      top: 50%;
      right: 3rem;
      transform: translateY(-50%);
    }
  }

  // list
  .labels-list {
    height: 35rem;
    overflow-y: auto;
    padding: 0 5rem 0 6rem;
    margin-bottom: 2rem;

    &::-webkit-scrollbar-thumb {
      background: #f7892f;
      border-radius: 30px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: darken(#f7892f, 10%);
    }

    .list-group-item {
      border: none;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1.5rem 0.4rem;

      .label-text {
        font-weight: 500;
        font-size: 2.5rem;
        line-height: 3.7rem;
        color: #000000;
      }

      .label-icon {
        display: inline-block;
        width: 5rem;
        height: 3rem;
        clip-path: polygon(65% 0, 99% 50%, 65% 100%, 0 100%, 0 0);
        background: red;
        margin-right: 5rem;
      }

      .label-check {
        display: inline-flex;
        border: 1px solid #919192;

        &:hover {
          outline: none;
          box-shadow: none;
          cursor: pointer;
        }
      }
    }
  }

  .btn-container {
    padding: 0 6rem;
    margin-bottom: 5rem;
  }
}

.block-ui {
  background: rgba(0, 0, 0, 0.15);
  z-index: 2;
  cursor: wait;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 1rem;
}
// .hwa-error-container {
// 	font-size: 2.2rem;
// 	font-weight: 500;
// 	height: 5rem;
// 	line-height: 5rem;
// 	color: #eb3232;
// 	padding: 0 10px;
// 	text-align: center;
// 	text-align: center;
// 	visibility: hidden;

// 	&.show-error {
// 		visibility: visible;
// 	}
// }

@media only screen and (max-width: 1919px) {
  .label-search-modal {
    .search-group.custom {
      input {
        height: 6rem;
      }
    }
  }
}
