<template>
  <div v-if="show" class="label-search-modal">
    <div class="labels-modal">
      <!-- loader -->
      <div v-if="loading" class="block-ui" />

      <div class="search-group custom">
        <input :value="searchValue" type="text" class="form-control" placeholder="Search labels"
          @input="handleInput($event)" />
        <img :src="searchIcon" alt="Search Icon" />
        <span v-if="searchValue" class="clear-search-icon" @click.prevent="handleInput({ target: { value: '' } })">
          &times;
        </span>
      </div>

      <ul class="list-group labels-list">
        <li v-for="label in filteredLabels" :key="label.id" class="list-group-item">
          <div class="d-flex justify-content-between align-items-center">
            <span class="label-icon" :style="{ background: `${label.color}` }" />
            <span class="label-text">{{ label.name }}</span>
          </div>
          <label class="custom-checkbox forward" :class="{ checked: label.selected }">
            <input type="checkbox" :name="label.name" class="label-check" :checked="label.selected"
              @change="handleCheck($event, label)" />
            <span class="checkmark" />
          </label>
        </li>
      </ul>

      <div class="btn-container">
        <button class="btn btn-primary" @click="handleAddLabel">
          {{ buttontext }}
        </button>
      </div>
    </div>
    <div class="labels-modal_overlay" @click.prevent="closeModal" />
  </div>
</template>

<script>
import SearchIcon from '../PngIcons/search_icon.png'
import { mapState } from 'vuex';

export default {
  name: 'LabelsModal',
  props: {
    show: { type: Boolean },
    loading: { type: Boolean },
    toggle: { type: Function, default: () => ({}) }
  },
  emits: ['chat-label-handler'],
  data() {
    return {
      searchIcon: SearchIcon,
      searchValue: '',
      updatedLabelsData: [],
      buttontext: 'Save Changes'
    }
  },

  computed: {
    ...mapState(['labelsData', 'room']),

    filteredLabels() {
      return this.updatedLabelsData?.filter(label => label?.name?.toLowerCase()?.includes(this.searchValue?.toLowerCase()))
    }


  },

  watch: {
    show: function (newVal) {
      if (newVal) {
        this.setSelectedLabels();
      }
    }
  },

  methods: {
    setSelectedLabels() {
      const selectedLabelIds = this.room?.labelsString ? this.room?.labelsString?.split(',')?.map(id => parseInt(id)) : [];

      this.updatedLabelsData = this.labelsData?.map(el => ({
        ...el,
        selected: selectedLabelIds.includes(el.id)
      }));
    },

    closeModal() {
      this.searchValue = ''
      this.toggle(null)
    },
    handleInput({ target: { value } }) {
      this.searchValue = value
    },
    handleCheck(event, label) {
      const updatedData = this.updatedLabelsData.map(el => {
        if (el.id === label.id) {
          return { ...el, selected: event.target.checked };
        }
        return el;
      });

      this.updatedLabelsData = updatedData;

      // Button text update logic
      if (updatedData.some(el => el.selected)) {
        this.buttontext = 'Assign Label';
      } else {
        this.buttontext = 'Save Changes';
      }
    },
    handleAddLabel() {
      const ids = []
      this.updatedLabelsData.forEach(el => {
        if (el.selected) {
          ids.push(el.id)
        }
      })
      const payload = {
        ids: ids.join(','),
        roomId: this.room.roomId
      }

      this.$emit('chat-label-handler', payload)
    }
  }
}
</script>
