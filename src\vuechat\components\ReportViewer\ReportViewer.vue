<template>
  <div class="report">
    <div :class="{ data: true, 'data-condition': isModalVisible }">
      <div class="date-select-div">
        <!--  -->
        <DateComponent
          @selectedDate="receiveDate"
          :startDate="startDate"
          :endDate="endDate"
        />
        <!-- <DateComponent @selectedDate="receiveDate" :startDate="startDate" :endDate="endDate" :disabled="!hasAdvancedReporting"/> -->
        <div class="report-div">
          <button @click="toggleModal" :class="{ 'read-report': isModalVisible }">
            <span>How to read the report</span> <img :src="info" alt="info" />
          </button>
          <button
            class="btn btn-primary"
            :disabled="totalNumberOfCampaigns === 0 || iframeMode || !hasDataExport"
            @click="handleExportClick"
          >
            <span v-if="hasDataExport">Download Report</span>
            <span v-else>Download Report <img :src="lockIcon" alt="Locked" class="lock-icon" style="height: 25px;width: 25px;"/></span>
          </button>
        </div>
      </div>

      <Loader v-if="loaderState" />
      <div v-if="!loaderState && (totalNumberOfCampaigns !== 0 || this.incomingMessagesData?.total_count !== 0)"
        class="content-div">

        <CommulativePerformance :chartData="chartData" :rateDealData="rateDealData"
          :totalNumberOfCampaigns="totalNumberOfCampaigns" :incomingMessagesData="incomingMessagesData"
          :reportName="this.reportName" />

        <CampaignPerformance ref="campaignPerformance" @update:currentReportName="updateCurrentReportName"
          :campaignTableData="campaignTableData" :reportName="this.reportName" :startDate="startDate"
          :endDate="endDate" />

        <Pagination v-if="showPagination && showCampPagination"
          :totalCampaigns="totalNumberOfCampaigns" :fetchPageData="fetchPageData" :selectedPage="currentPage" />

      </div>
      <DataHandler v-if="totalNumberOfCampaigns === 0 && this.incomingMessagesData?.total_count === 0" />
    </div>

    <!-- Upgrade Modal -->
    <UpgradePrompt
      v-if="showUpgradeModal && upgradeModalData"
      :title="upgradeModalData.title"
      :description="upgradeModalData.description"
      :features="upgradeModalData.features"
      :target-plan="upgradeModalData.targetPlan"
      :overlay-mode="true"
      @close="closeUpgradeModal"
      @upgrade-clicked="handleUpgradeClick"
    />

    <div v-if="isModalVisible" class="Info-modal-div">
      <InfoModal :toggleModal="toggleModal" />
    </div>
  </div>
</template>

<script>
import axios from '@/utils/api.js'
import WhatshiveLogo from '@/assets/icons/whatshive_logo.svg'
import Info from '@/assets/icons/info.svg'
import UpsideArrow from '@/assets/icons/upside_arrow.svg'
import CommulativePerformance from '@/components/CommulativePerformance'
import CampaignPerformance from '@/components/CampaignPerformance'
import DateComponent from '@/components/DateComponent'
import InfoModal from '@/components/InfoModal'
import Pagination from '@/components/Pagination'
import Loader from '@/components/ReportLoader'
import DataHandler from '@/components/DataNotFound'
import UpgradePrompt from '@/components/UpgradePrompt'
import moment from 'moment';
import { mapState } from 'vuex';
import { getUpgradeMessage } from '@/utils/planAccess';
import LockIcon from '@/assets/icons/lock_icon.svg';

export default {
  name: 'ReportViewer',
  components: {
    CommulativePerformance,
    CampaignPerformance,
    DateComponent,
    InfoModal,
    Pagination,
    Loader,
    DataHandler,
    UpgradePrompt
  },

  props: {
    reportName: {
      type: String,
    }
  },

  data() {
    return {
      whatshiveLogo: WhatshiveLogo,
      lockIcon: LockIcon,
      info: Info,
      upsideArrow: UpsideArrow,
      isModalVisible: false,
      showScrollButton: true,
      user_id: null,
      chartData: null,
      rateDealData: null,
      campaignTableData: null,
      totalNumberOfCampaigns: null,
      startDate: null,
      endDate: null,
      loaderState: false,
      currentPage: null,
      exportURL: null,
      domain: process.env.VUE_APP_API_URL,
      localReportName: this.reportName,
      showCampPagination: true,
      showUpgradeModal: false,
      upgradeModalData: null
    }
  },
  created() {
    const userData = this.$store.state.userData
    this.user_id = userData.user_id
    this.getInitialCampaignsReport();
  },
  watch: {
    startDate: function (newVal, oldVal) {
      if (!oldVal) {
        let endDate = this.convertDateFormat(this.endDate);
        let startDate = this.convertDateFormat(this.startDate);
        if (this.reportName === "Workflow") {
          this.exportURL = `${this.domain}workflow/report/export?user_id=${this.user_id}&to=${endDate}&from=${startDate}`
        } else {
          this.exportURL = `${this.domain}report/export?user_id=${this.user_id}&to=${endDate}&from=${startDate}`
        }
      }
    },
    reportName: function (newValue, oldValue) {
      this.startDate = null;
      this.endDate = null;
      this.getInitialCampaignsReport(null, null, 1);
    }
  },
  computed: {
    showPagination() {
      return this.totalNumberOfCampaigns >= 25 ;
    },

    // Plan-based access control
    userPlan() {
      return this.$store.getters.userPlan;
    },

    hasDataExport() {
      return this.$store.getters.hasExportFeatures;
    },

    hasAdvancedReporting() {
      return this.$store.getters.hasAdvancedReporting;
    },

    ...mapState(['iframeMode'])
  },
  methods: {
    convertDateFormat(inputDate) {
        const mdy = moment(inputDate, "MM/DD/YYYY", true);
        if (mdy.isValid()) {
          return mdy.format("DD/MM/YYYY");
        } else {
          return inputDate;
        }
    },

    toggleModal() {
      this.isModalVisible = !this.isModalVisible
    },
    closeModal(event) {
      if (!this.$el.contains(event.target)) {
        this.isModalVisible = false;
      }
    },
    fetchPageData(pageNumber) {
      this.getInitialCampaignsReport(this.startDate, this.endDate, pageNumber)
    },

    receiveDate(startDate, endDate, pageNumber) {
      this.startDate = startDate
      this.endDate = endDate
      this.getInitialCampaignsReport(startDate, endDate, pageNumber)
    },

    // Fetch the report
    async getInitialCampaignsReport(startDate = null, endDate = null, pageNumber = 1) {
      if (this.reportName === "Workflow") {
        this.exportURL = `${this.domain}workflow/report/export?user_id=${this.user_id}&to=${endDate}&from=${startDate}`
      } else {
        this.exportURL = `${this.domain}report/export?user_id=${this.user_id}&to=${endDate}&from=${startDate}`

      }
      this.loaderState = true
      let offset = pageNumber - 1

      try {
        let reportsUrl;
        if (this.reportName === "Workflow") {
          reportsUrl = startDate ? `api/workflow/report?user_id=${this.user_id}&to=${endDate}&from=${startDate}&offset=${offset}` : `api/workflow/report?user_id=${this.user_id}&offset=${offset}`

        } else {
          reportsUrl = startDate ? `api/reports?user_id=${this.user_id}&to=${endDate}&from=${startDate}&offset=${offset}` : `api/reports?user_id=${this.user_id}&offset=${offset}`
        }
        let messageAnalytics = startDate ? `api/message-analytics?user_id=${this.user_id}&to=${endDate}&from=${startDate}` : `api/message-analytics?user_id=${this.user_id}`

        const [reportResponse, messageAnalyticsResponse] = await Promise.all([
          axios.get(reportsUrl),
          axios.get(messageAnalytics)
        ]);

        this.incomingMessagesData = messageAnalyticsResponse?.data?.data
        this.chartData = reportResponse?.data?.combined
        this.campaignTableData = reportResponse?.data?.data
        this.rateDealData = reportResponse?.data?.stats
        this.totalNumberOfCampaigns = reportResponse?.data?.total
        this.currentPage = pageNumber
        this.loaderState = false
        this.startDate = reportResponse?.data?.range?.from
        this.endDate = reportResponse?.data?.range?.to

      } catch (err) {
        this.loaderState = false
        this.errorMessage = 'Something went wrong!'
        console.log(err)
      }
    },

    updateCurrentReportName(newReportName) {
      this.currentReportName = newReportName;
      this.showCampPagination = newReportName !== 'Template'
    },

    handleExportClick() {
      if (this.hasDataExport) {
        // Open export URL if user has access
        window.open(this.exportURL, '_blank');
      } else {
        // Show upgrade prompt
        this.upgradeModalData = getUpgradeMessage('dataExport');
        this.showUpgradeModal = true;
      }
    },

    closeUpgradeModal() {
      this.showUpgradeModal = false;
      this.upgradeModalData = null;
    },

    handleUpgradeClick() {
      // You can customize this URL based on your upgrade flow
      const upgradeUrl = process.env.VUE_APP_UPGRADE_URL || '#';
      window.open(upgradeUrl, '_blank');
      this.closeUpgradeModal();
    }
  }
}
</script>
