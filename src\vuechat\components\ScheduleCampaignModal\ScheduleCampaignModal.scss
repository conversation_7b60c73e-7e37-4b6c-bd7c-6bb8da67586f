.schedule-campaign-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999999;

  .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    // z-index: 999;
  }

  .secondary-div {
    background-color: #fff;
    padding: 7rem;
    position: relative;
    width: 680px;
    border-radius: 10px;
    z-index: 99;	

    h2 {
      font-weight: 500;
      margin-block-end: 5rem;
    }

    .content-div {
      display: flex;
      gap: 5rem;
      // z-index: 9999;

      .vdpComponent {
        width: 62rem;
      }

      .date-time-inputs {
        display: flex;
        flex-direction: column;
        width: 100%;
        justify-content: space-between;

        & div:nth-child(1) {
          display: flex;
          flex-direction: column;
          align-items: end;
          gap: 7rem;
          position: relative;

          .error-msg {
            position: absolute;
            top: 108px;
            left: 0;
            font-weight: 500;
            font-size: 2rem;
            line-height: 1.3;
          }

          input {
            height: fit-content;
            border: none;
            border-bottom: 1px solid #d4d4d4;
            font-size: 2.6rem;
            width: 100%;
            outline: none;
            padding-inline-start: 1.5rem;
          }

          select {
            box-shadow: none;
            padding-inline-start: 1.5rem;
          }
        }

        & div:nth-child(2) {
          display: flex;
          // align-items: space-between;
          justify-content: space-between;
        }

        .form-control {
          border: none;
          border-bottom: 1px solid #d4d4d4;
          font-size: 2.6rem !important;
          max-height: 10rem;
          appearance: auto; /* Ensure default dropdown appearance */
        }
      }
    }
  }

  @media screen and (min-width: 1910px) {
    .secondary-div {
      width: 1000px;
      padding: 5rem;

      .date-time-inputs {
        gap: 6rem;

        div:nth-child(1) {
          gap: 5rem !important;

          .error-msg{
            top: 148px !important;
          }
        }
      }
    }
  }
}
