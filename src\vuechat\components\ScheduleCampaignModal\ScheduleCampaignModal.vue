<template>
    <div class="schedule-campaign-modal" v-if="showCampaignScheduleModal">
        <div @click="closeModal" class="modal-overlay"></div>
        <div class="secondary-div modal-md">
            <h2>Pick date & time</h2>
            <div class="content-div">
                <date-pick v-model="date" :hasInputElement="false" :format="'YYYY-MM-DD'" :disable-days-before="minDate"
                    :is-date-disabled="isDateDisabled" />
                <div class="date-time-inputs">
                    <div>
                        <input :value="date" type="text" readonly>
                        <input v-model="timeInput" @blur="validateAndFormatTime" type="text"
                            placeholder="Enter time (e.g., 11:12 AM)" />
                        <select v-model="selectedTimezone" class="form-control">
                            <option v-for="tz in timezones" :key="tz" :value="tz">{{ tz }}</option>
                        </select>
                        <p v-if="errorMessage" class="error-msg text-danger">{{ errorMessage }}</p>
                    </div>
                    <div>
                        <button @click="closeModal" class="btn btn-secondary">Cancel</button>
                        <button @click="sendCampaignHandler" class="btn btn-primary">Schedule send</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import DatePick from 'vue-date-pick';
import 'vue-date-pick/dist/vueDatePick.css';
import moment from 'moment';
import momentTimeZone from 'moment-timezone';

export default {
    name: 'ScheduleCampaignModal',
    components: { DatePick },
    props: {
        showCampaignScheduleModal: Boolean
    },
    data() {
        return {
            date: moment().format('YYYY-MM-DD'),
            minDate: moment().toDate(), // Minimum date set to today
            timeInput: '',
            storeCurrentTime: '',
            errorMessage: '', // To store error messages
            timestamp: null, // To store the generated timestamp
            selectedTimezone: momentTimeZone.tz.guess(), // Set default timezone to user's local timezone
            timezones: momentTimeZone.tz.names(), // Array of timezones
        }
    },

    mounted() {
        this.updateTimeInput(); // Set the time input and current time when the component is mounted
    },

    methods: {

        updateTimeInput() {
            const currentTime = moment().tz(this.selectedTimezone);
            this.timeInput = currentTime.add(1, 'hours').format('hh:mm A'); // Set the time input to current time + 1 hour
            this.storeCurrentTime = currentTime.format('hh:mm A'); // Store the current time
            this.errorMessage = ''
        },

        closeModal() {
            this.$emit('close-modal')
        },

        resetDateTime() {
            this.validateAndFormatTime()
            // Set the current date, time, and timezone
            this.timeInput = moment().add(1, 'hours').format('hh:mm A'); // Format as 12-hour time with AM/PM
            this.selectedTimezone = moment.tz.guess();
        },

        sendCampaignHandler() {
            const isValid = this.validateAndFormatTime();
            isValid && this.$emit('schedule-campaign', this.date, this.timeInput, this.selectedTimezone)
        },

        isDateDisabled(date) {
            return moment(date).isBefore(this.minDate, 'day');
        },

        validateAndFormatTime() {
            this.errorMessage = '';

            // Regular expression to match 12-hour or 24-hour time formats
            const timeRegex = /^(1[0-2]|0?[1-9]):[0-5][0-9]\s?(AM|PM)?$|^(2[0-3]|[01]?[0-9]):[0-5][0-9]$/i;

            // Check if the input matches the regex
            if (!timeRegex.test(this.timeInput)) {
                this.errorMessage = 'Invalid time format.';
                return false;
            }

            // Extract parts of the time input
            const parts = this.timeInput.match(/^(\d{1,2}):(\d{2})(\s?(AM|PM))?$/i);
            let hours, minutes, period;

            if (parts) {
                hours = parseInt(parts[1], 10);
                minutes = parts[2];
                period = parts[4] ? parts[4].toUpperCase() : '';

                if (period) {
                    // Handle 12-hour format
                    if (hours === 12 && period === 'AM') {
                        hours = 0; // midnight
                    } else if (hours !== 12 && period === 'PM') {
                        hours += 12; // PM times are offset by 12 hours
                    }
                }

                const selectedTime = moment.tz(`${hours}:${minutes} ${period}`, 'HH:mm A', this.selectedTimezone);
                const currentTimeInSelectedZone = moment().tz(this.selectedTimezone); // Current time in selected timezone
                
                // Get the selected date as a moment object
                const selectedDate = moment(this.date, 'YYYY-MM-DD');

                // Check if the selected date is today
                if (selectedDate.isSame(currentTimeInSelectedZone, 'day')) {                    
                    // If today, check if the input time is at least 5 minutes in the future
                    if (selectedTime.isBefore(currentTimeInSelectedZone.add(5, 'minutes'))) {                        
                        this.errorMessage = 'Please select a time at least 5 minutes in the future.';
                        return false;
                    }
                }

                // Format the time to 12-hour format with AM/PM
                this.timeInput = selectedTime.format('hh:mm A');
                return true;
            } else {
                this.errorMessage = 'Invalid time format.';
                return false;
            }
        },

        // Will use later
        generateTimestamp() {
            if (!this.dateInput || !this.timeInput) {
                this.errorMessage = 'Please enter both date and time.';
                return;
            }

            const datePart = new Date(this.dateInput); // Parse the date part
            if (isNaN(datePart.getTime())) {
                this.errorMessage = 'Invalid date format.';
                return;
            }

            const [time, period] = this.timeInput.split(' ');
            let [hours, minutes] = time.split(':').map(Number);

            if (period === 'PM' && hours < 12) {
                hours += 12;
            } else if (period === 'AM' && hours === 12) {
                hours = 0;
            }

            // Set the time parts
            datePart.setHours(hours);
            datePart.setMinutes(minutes);

            // Store the timestamp
            this.timestamp = datePart.getTime(); // Timestamp in milliseconds
        },

    },

    watch: {
        selectedTimezone(newTimezone) {
            this.updateTimeInput(); // Update the time when timezone changes
        }
    },
}

</script>