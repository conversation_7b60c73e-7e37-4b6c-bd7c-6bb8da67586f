<template>
  <div v-show="show" class="success-modal">
    <transition name="vac-bounce">
      <div v-if="show" class="success-modal_content">
        <div class="success-header">
          {{ successMessage.heading }}
          <button class="close-button" @click.prevent="toggle">&times;</button>
        </div>

        <div class="success-message">
          <p>{{ successMessage.content }}</p>
        </div>
      </div>
    </transition>
    <div class="success-modal_overlay" @click.prevent="toggle" />
  </div>
</template>

<script>
export default {
  name: 'SuccessModal',
  props: {
    show: { type: Boolean },
    toggle: { type: Function, default: () => ({}) },
    successMessage: {
      type: Object,
      required: true
    }
  }
}
</script>
