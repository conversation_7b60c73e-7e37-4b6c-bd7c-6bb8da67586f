#vac-icon-search {
  fill: var(--chat-icon-color-search);
}

#vac-icon-add {
  fill: var(--chat-icon-color-add);
}

#vac-icon-toggle {
  fill: var(--chat-icon-color-toggle);
}

#vac-icon-menu {
  fill: var(--chat-icon-color-menu);
}

#vac-icon-close {
  fill: var(--chat-icon-color-close);
}

#vac-icon-close-image {
  fill: var(--chat-icon-color-close-image);
}

#vac-icon-file {
  fill: var(--chat-icon-color-file);
}

#vac-icon-paperclip {
  fill: #34b7f1;
}

#vac-icon-close-outline {
  // fill: var(--chat-icon-color-close-outline);
  fill: #919192;
}

#vac-icon-send {
  fill: var(--chat-icon-color-send);
}

#vac-icon-send-disabled {
  fill: var(--chat-icon-color-send-disabled);
}

#vac-icon-emoji {
  fill: var(--chat-icon-color-emoji);
}

#vac-icon-emoji-reaction {
  fill: var(--chat-icon-color-emoji-reaction);
}

#vac-icon-document {
  fill: #333;
}

#vac-icon-pencil {
  fill: var(--chat-icon-color-pencil);
}

#vac-icon-checkmark,
#vac-icon-wait,
#vac-icon-double-checkmark {
  fill: var(--chat-icon-color-checkmark);
}

#vac-icon-checkmark-seen,
#vac-icon-double-checkmark-seen {
  fill: #2cb7ef;
}

#vac-icon-eye {
  fill: var(--chat-icon-color-eye);
}

#vac-icon-dropdown-message {
  fill: var(--chat-icon-color-dropdown-message);
}

#vac-icon-dropdown-room {
  // fill: var(--chat-icon-color-dropdown-room);
  fill: #000;
}

#vac-icon-dropdown-scroll {
  fill: var(--chat-icon-color-dropdown-scroll);
}

#vac-icon-audio-play {
  fill: var(--chat-icon-color-audio-play);
}

#vac-icon-audio-pause {
  fill: var(--chat-icon-color-audio-pause);
}
#vac-icon-error {
  fill: #dc3545;
}
