/* Add your modal styles here */
.template-modal {
  cursor: context-menu;
  position: fixed;
  z-index: 2;
  top: 0px;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &_close {
    position: absolute;
    top: 10px;
    right: 0px;
    font-size: 30px;
    color: #333;
    cursor: pointer;
    border: none;
    background: none;
  }

  .modal-dialog {
    width: 90% !important;
    height: 80%;
    position: relative;
    z-index: 99;
    
    .modal-content {
      position: static !important;
      height: 100% !important;
      overflow: auto !important;
      background-color: #fff;

      .modal-header {
        margin-bottom: 1rem !important;
        padding: 4.3rem;
        padding-bottom: 0;
        .btn {
          all: unset;
          cursor: pointer;
        }
      }

      .form-group {
        margin-bottom: 2rem;

        label {
          font-weight: 600;
          font-size: 16px;
          line-height: 3rem;
          color: #2c3f51;
          margin-bottom: 0.8rem;
        }

        .form-control,
        .form-select {
          border: 2px solid #d2d2d2;
          border-radius: 2rem;
          font-size: 16px;
          line-height: 3rem;
          color: #919192;
          padding: 1.9rem 2.8rem;
          font-weight: normal;

          &::placeholder {
            color: #919192;
          }
        }

        .search-input {
          height: 6rem !important;
          margin-top: 5px;
          font-size: 16px;
          border-radius: 5px;
          padding: 15px 10px !important;
          border: 2px solid #9e9eeb;
        }

        input.form-control {
          height: 7rem;
          padding: 20px 10px;
        }

        textarea.form-control {
          padding: 2.6rem 3.1rem;
          overflow: hidden;
        }
      }
    }

    .token-dropdown {
      font-size: 14px;
      border: 1px solid #d2d2d2;
      padding-inline: 0px;
      background-color: #ffffff;
      list-style: none !important;
      position: absolute;
      bottom: 23px;
      right: 0;
      height: 30rem;
      width: 280px;
      overflow: hidden;

      li {
        padding-inline: 6px !important;

        input {
          border: 1px solid #cbd6e2 !important;
          outline: none !important;
        }

        .dropdown-item {
          padding: 8px;
          white-space: nowrap;
          width: 18em;
          cursor: pointer;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .dropdown-data {
        max-height: 23rem !important;
        overflow-y: auto;
        overflow-x: hidden;

        button:hover {
          width: 100%;
          background-color: #cbd6e2;
        }

        .no-data-found {
          justify-content: center;
          padding: 20px 0;
          font-weight: 500;
          display: none;
        }
      }
    }

    .tokenButton {
      border: 0px;
      padding: 5px 8px;
      font-size: 16px;
      font-weight: 500;
    }

    .modal-footer {
      display: flex !important;
      justify-content: flex-start;
      gap: 2rem;
      background-color: #eaf0f6;
      padding-block: 3rem;
      position: absolute;
      bottom: 0;
      width: 100%;
      left: 0;
      border-top: 1.5px solid #cbd6e2;
      padding-inline: 26px;
    }
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background: white;
  border-radius: 8px;
}

.footer {
  display: block !important;
}

.modal-body {
  padding: 1.5rem 4.3rem !important;
  margin-bottom: 20rem;
  // padding-top:10px  !important;

  p {
    padding-bottom: 5px;
  }
}

@media screen and (max-width: 830px) {
  .modal-title,
  .modal-body p,
  .modal-footer .btn {
    font-size: 16px;
  }

  .template-modal {
    .modal-dialog {
      .modal-footer {
        padding-block: 5rem;
      }
    }
  }

  button.btn {
    padding: 1.3rem 4.6rem;
  }
}
