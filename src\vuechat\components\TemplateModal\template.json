[{"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"document_url": "Document URL"}], "controllingFieldName": "media_type", "controllingFieldValue": "document"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"image_url": "Image Url"}], "controllingFieldName": "media_type", "controllingFieldValue": "image"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"video_url": "Video url"}], "controllingFieldName": "media_type", "controllingFieldValue": "video"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"location_address": "Location address"}, {"location_name": "Location name"}, {"latitude": "Latitude"}, {"longitude": "Longitude"}], "controllingFieldName": "media_type", "controllingFieldValue": "location"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"buttons_dynamic_1_button_1": "Button 1"}], "controllingFieldName": "media_type", "controllingFieldValue": "buttons_dynamic_1"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"button_copy": "Copy button"}], "controllingFieldName": "media_type", "controllingFieldValue": "button_copy"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"buttons_dynamic_2_button_1": "Button 1"}, {"buttons_dynamic_2_button_2": "Button 2"}], "controllingFieldName": "media_type", "controllingFieldValue": "buttons_dynamic_2"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"buttons_dynamic_1_copy_button_1": "Button 1"}, {"buttons_dynamic_1_copy_copy": "Copy button"}], "controllingFieldName": "media_type", "controllingFieldValue": "buttons_dynamic_1_copy"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"buttons_dynamic_2_copy_button_1": "Button 1"}, {"buttons_dynamic_2_copy_button_2": "Button 2"}, {"buttons_dynamic_2_copy_copy": "Copy button"}], "controllingFieldName": "media_type", "controllingFieldValue": "buttons_dynamic_2_copy"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"image_buttons_dynamic_1_url": "Image button url"}, {"image_buttons_dynamic_1_button_1": "Button 1"}], "controllingFieldName": "media_type", "controllingFieldValue": "image_buttons_dynamic_1"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"image_buttons_dynamic_2_url": "Image button url"}, {"image_buttons_dynamic_2_button_1": "Button 1"}, {"image_buttons_dynamic_2_button_2": "Button 2"}], "controllingFieldName": "media_type", "controllingFieldValue": "image_buttons_dynamic_2"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"image_buttons_copy_url": "Image button url"}, {"image_buttons_copy_copy": "Copy button"}], "controllingFieldName": "media_type", "controllingFieldValue": "image_buttons_copy"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"image_buttons_dynamic_1_copy_url": "Image button url"}, {"image_buttons_dynamic_1_copy_button_1": "Button 1"}, {"image_buttons_dynamic_1_copy_copy": "Copy button"}], "controllingFieldName": "media_type", "controllingFieldValue": "image_buttons_dynamic_1_copy"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"image_buttons_dynamic_2_copy_url": "Image button url"}, {"image_buttons_dynamic_2_copy_button_1": "Button 1"}, {"image_buttons_dynamic_2_copy_button_2": "Button 2"}, {"image_buttons_dynamic_2_copy_copy": "Copy button"}], "controllingFieldName": "media_type", "controllingFieldValue": "image_buttons_dynamic_2_copy"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"video_buttons_dynamic_1_url": "Video button url"}, {"video_buttons_dynamic_1_button_1": "Button 1"}], "controllingFieldName": "media_type", "controllingFieldValue": "video_buttons_dynamic_1"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"video_buttons_dynamic_2_url": "Video button url"}, {"video_buttons_dynamic_2_button_1": "Button 1"}, {"video_buttons_dynamic_2_button_2": "Button 2"}], "controllingFieldName": "media_type", "controllingFieldValue": "video_buttons_dynamic_2"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"video_buttons_copy_url": "Video button url"}, {"video_buttons_copy_copy": "Copy button"}], "controllingFieldName": "media_type", "controllingFieldValue": "video_buttons_copy"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"video_buttons_dynamic_1_copy_url": "Video button url"}, {"video_buttons_dynamic_1_copy_button_1": "Button 1"}, {"video_buttons_dynamic_1_copy_copy": "Copy button"}], "controllingFieldName": "media_type", "controllingFieldValue": "video_buttons_dynamic_1_copy"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"video_buttons_dynamic_2_copy_url": "Video button url"}, {"video_buttons_dynamic_2_copy_button_1": "Button 1"}, {"video_buttons_dynamic_2_copy_button_2": "Button 2"}, {"video_buttons_dynamic_2_copy_copy": "Copy button"}], "controllingFieldName": "media_type", "controllingFieldValue": "video_buttons_dynamic_2_copy"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"document_buttons_dynamic_1_url": "Document button url"}, {"document_buttons_dynamic_1_button_1": "Button 1"}], "controllingFieldName": "media_type", "controllingFieldValue": "document_buttons_dynamic_1"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"document_buttons_dynamic_2_url": "Document button url"}, {"document_buttons_dynamic_2_button_1": "Button 1"}, {"document_buttons_dynamic_2_button_2": "Button 2"}], "controllingFieldName": "media_type", "controllingFieldValue": "document_buttons_dynamic_2"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"document_buttons_copy_url": "Document button url"}, {"document_buttons_copy_copy": "Copy button"}], "controllingFieldName": "media_type", "controllingFieldValue": "document_buttons_copy"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"document_buttons_dynamic_1_copy_url": "Document button url"}, {"document_buttons_dynamic_1_copy_button_1": "Button 1"}, {"document_buttons_dynamic_1_copy_copy": "Copy button"}], "controllingFieldName": "media_type", "controllingFieldValue": "document_buttons_dynamic_1_copy"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"document_buttons_dynamic_2_copy_url": "Document button url"}, {"document_buttons_dynamic_2_copy_button_1": "Button 1"}, {"document_buttons_dynamic_2_copy_button_2": "Button 2"}, {"document_buttons_dynamic_2_copy_copy": "Copy button"}], "controllingFieldName": "media_type", "controllingFieldValue": "document_buttons_dynamic_2_copy"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"location_buttons_dynamic_1_location_address": "Location address"}, {"location_buttons_dynamic_1_location_name": "Location name"}, {"location_buttons_dynamic_1_latitude": "Latitude"}, {"location_buttons_dynamic_1_longitude": "Longitude"}, {"location_buttons_dynamic_1_button_1": "Button 1"}], "controllingFieldName": "media_type", "controllingFieldValue": "location_buttons_dynamic_1"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"location_buttons_dynamic_2_location_address": "Location address"}, {"location_buttons_dynamic_2_location_name": "Location name"}, {"location_buttons_dynamic_2_latitude": "Latitude"}, {"location_buttons_dynamic_2_longitude": "Longitude"}, {"location_buttons_dynamic_2_button_1": "Button 1"}, {"location_buttons_dynamic_2_button_2": "Button 2"}], "controllingFieldName": "media_type", "controllingFieldValue": "location_buttons_dynamic_2"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"location_buttons_copy_location_address": "Location address"}, {"location_buttons_copy_location_name": "Location name"}, {"location_buttons_copy_latitude": "Latitude"}, {"location_buttons_copy_longitude": "Longitude"}, {"location_buttons_copy_copy": "Copy button"}], "controllingFieldName": "media_type", "controllingFieldValue": "location_buttons_copy"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"location_buttons_dynamic_1_copy_location_address": "Location address"}, {"location_buttons_dynamic_1_copy_location_name": "Location name"}, {"location_buttons_dynamic_1_copy_latitude": "Latitude"}, {"location_buttons_dynamic_1_copy_longitude": "Longitude"}, {"location_buttons_dynamic_1_copy_button_1": "Button 1"}, {"location_buttons_dynamic_1_copy_copy": "Copy button"}], "controllingFieldName": "media_type", "controllingFieldValue": "location_buttons_dynamic_1_copy"}, {"dependencyType": "CONDITIONAL_SINGLE_FIELD", "dependentFieldNames": [{"location_buttons_dynamic_2_copy_location_address": "Location address"}, {"location_buttons_dynamic_2_copy_location_name": "Location name"}, {"location_buttons_dynamic_2_copy_latitude": "Latitude"}, {"location_buttons_dynamic_2_copy_longitude": "Longitude"}, {"location_buttons_dynamic_2_copy_button_1": "Button 1"}, {"location_buttons_dynamic_2_copy_button_2": "Button 2"}, {"location_buttons_dynamic_2_copy_copy": "Copy button"}], "controllingFieldName": "media_type", "controllingFieldValue": "location_buttons_dynamic_2_copy"}]