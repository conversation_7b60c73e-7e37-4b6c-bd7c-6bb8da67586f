.upload-modal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  min-width: 56rem;
  width: 35vw;
  min-height: 42rem;
  max-height: 90vh;
  background-color: white;
  // padding: 3.5rem 5rem;
  border-radius: 5px;
  box-shadow: 0px 4px 20px rgba(169, 170, 181, 0.25);
  z-index: 100;

  &_close {
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 30px;
    color: #333;
    cursor: pointer;
    border: none;
    background: none;
  }

  &_overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.85);
    // backdrop-filter: blur(2px);
    z-index: 99;
  }

  &-content {
    padding: 5rem;
    height: 100%;

    .content-heading {
      text-align: center;

      &_main {
        font-weight: bold;
        font-size: 2.5rem;
        line-height: 3.7rem;
        letter-spacing: 0.002em;
        color: #000000;
      }

      &_sub {
        font-weight: 500;
        font-size: 1.5rem;
        line-height: 2.2rem;
        color: #2c3f51;
      }
    }

    .dragdrop-box {
      width: 100%;
      min-height: 25rem;
      border: 2px dashed #2c3f51;
      box-sizing: border-box;
      border-radius: 10px;
      margin-top: 4.3rem;
      text-align: center;

      .drag-drop {
        text-align: center;
        padding: 2.5rem 0;

        .drop-text {
          font-weight: 500;
          font-size: 1.5rem;
          line-height: 2.2rem;
          color: #2c3f51;
        }
      }

      .diversion-text {
        font-weight: 500;
        font-size: 1.5rem;
        line-height: 2.2rem;
        color: #2c3f51;
      }

      .upload-button {
        margin: 8px 0 3rem 0;

        button {
          background: #2c3d4f;
          box-shadow: 0px 4px 25px rgba(218, 218, 218, 0.25);
          border-radius: 1rem;
          min-height: 4rem;
          outline: none;
          border-color: #2c3d4f;
          color: white;
          font-weight: 500;
          font-size: 1.5rem;
          line-height: 2.2rem;
          padding: 0.8rem 2.9rem;
        }
      }
    }

    .uploaded-files {
      margin: 2.3rem 0;
      margin-bottom: 0;

      .file-heading {
        font-weight: 500;
        font-size: 1.5rem;
        line-height: 2.2rem;
        color: #2c3f51;
        margin-bottom: 2rem;

        // span {
        // 	margin-right: 20px;
        // }

        // .dot-pulse {
        // 	display: inline-block;
        // 	position: relative;
        // 	left: -9999px;
        // 	top: -1px;
        // 	width: 5px;
        // 	height: 5px;
        // 	border-radius: 50%;
        // 	background-color: #2c3d4f;
        // 	color: #2c3d4f;
        // 	box-shadow: 9999px 0 0 -5px #2c3d4f;
        // 	animation: dotPulse 1.5s infinite linear;
        // 	animation-delay: 0.25s;
        // }

        // .dot-pulse::before,
        // .dot-pulse::after {
        // 	content: '';
        // 	display: inline-block;
        // 	position: absolute;
        // 	top: 0;
        // 	width: 5px;
        // 	height: 5px;
        // 	border-radius: 50%;
        // 	background-color: #2c3d4f;
        // 	color: #2c3d4f;
        // }

        // .dot-pulse::before {
        // 	box-shadow: 9984px 0 0 -5px #2c3d4f;
        // 	animation: dotPulseBefore 1.5s infinite linear;
        // 	animation-delay: 0s;
        // }

        // .dot-pulse::after {
        // 	box-shadow: 10014px 0 0 -5px #2c3d4f;
        // 	animation: dotPulseAfter 1.5s infinite linear;
        // 	animation-delay: 0.5s;
        // }

        // @keyframes dotPulseBefore {
        // 	0% {
        // 		box-shadow: 9984px 0 0 -5px #2c3d4f;
        // 	}
        // 	30% {
        // 		box-shadow: 9984px 0 0 2px #2c3d4f;
        // 	}
        // 	60%,
        // 	100% {
        // 		box-shadow: 9984px 0 0 -5px #2c3d4f;
        // 	}
        // }

        // @keyframes dotPulse {
        // 	0% {
        // 		box-shadow: 9999px 0 0 -5px #2c3d4f;
        // 	}
        // 	30% {
        // 		box-shadow: 9999px 0 0 2px #2c3d4f;
        // 	}
        // 	60%,
        // 	100% {
        // 		box-shadow: 9999px 0 0 -5px #2c3d4f;
        // 	}
        // }

        // @keyframes dotPulseAfter {
        // 	0% {
        // 		box-shadow: 10014px 0 0 -5px #2c3d4f;
        // 	}
        // 	30% {
        // 		box-shadow: 10014px 0 0 2px #2c3d4f;
        // 	}
        // 	60%,
        // 	100% {
        // 		box-shadow: 10014px 0 0 -5px #2c3d4f;
        // 	}
        // }
      }

      .upload-file-wrapper {
        overflow: auto;
        max-height: 20vh;
        overflow: auto;
        padding-right: 1rem;

        .upload-file-item {
          display: flex;
          width: 100%;
          justify-content: space-between;
          align-items: center;

          &:not(:last-child) {
            margin-bottom: 2rem;
          }

          img.extension-icon {
            height: 5rem;
            margin-right: 1.7rem;
          }

          .file-progress-wrapper {
            width: 100%;
            margin-right: 3.6rem;

            .file-name {
              font-weight: 500;
              font-size: 1.5rem;
              line-height: 2.2rem;
              margin-bottom: 0.6rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              width: 150px;
              color: #000000;
            }

            .progress {
              height: 3px;
              background: #eef3fb;
              border-radius: 20px;
            }
          }
        }
      }
    }
  }
}

@media only screen and (max-width: 1919px) {
  .upload-modal {
    &-content {
      padding: 4rem;

      .content-heading {
        text-align: center;

        &_main {
          font-size: 20px;
          line-height: 30px;
        }

        &_sub {
          font-size: 13px;
          line-height: 22px;
        }
      }

      .dragdrop-box {
        .drag-drop {
          .drop-text {
            font-size: 12px;
            line-height: 18px;
          }
        }
        .diversion-text {
          font-size: 12px;
          line-height: 18px;
        }

        .upload-button {
          margin: 2rem 0 3rem 0;
          button {
            background: #2c3d4f;
            box-shadow: 0px 4px 25px rgba(218, 218, 218, 0.25);
            border-radius: 1rem;
            min-height: 35px;
            outline: none;
            border-color: #2c3d4f;
            color: white;
            font-weight: 500;
            font-size: 12px;
            line-height: 18px;
            padding: 8px 25px;
          }
        }
      }

      .uploaded-files {
        .file-heading {
          font-size: 13px;
          line-height: 18px;
        }

        .upload-file-item {
          .file-progress-wrapper {
            .file-name {
              font-size: 12px;
              line-height: 18px;
            }
          }
        }
      }
    }
  }
}
