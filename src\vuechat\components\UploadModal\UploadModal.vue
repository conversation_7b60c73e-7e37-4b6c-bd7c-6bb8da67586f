<template>
  <div v-if="show">
    <div class="upload-modal">
      <button class="upload-modal_close" @click.prevent="toggle">&times;</button>

      <div class="upload-modal-content">
        <div class="content-heading">
          <div class="content-heading_main">UPLOAD FILES</div>
          <div class="content-heading_sub">Upload documents you want to share</div>
        </div>
        <div class="dragdrop-box">
          <div
            class="drag-drop"
            :class="{
              'bg-secondary': is_dragover
            }"
            @drag.prevent.stop=""
            @dragstart.prevent.stop=""
            @dragend.prevent.stop="is_dragover = false"
            @dragover.prevent.stop="is_dragover = true"
            @dragenter.prevent.stop="is_dragover = true"
            @dragleave.prevent.stop="is_dragover = false"
            @drop.prevent.stop="handleDragDrop"
          >
            <img :src="dropIcon" alt="Drag Drop Icon" />
            <div class="drop-text">Drag and drop files here</div>
          </div>
          <div class="diversion-text">OR</div>
          <div class="upload-button">
            <button @click.prevent="handleUpload">Browse Files</button>
          </div>
        </div>

        <div v-if="files.length" class="uploaded-files">
          <div class="file-heading">
            <span>Uploaded files</span>
          </div>
          <!-- files -->
          <div class="upload-file-wrapper">
            <div v-for="file in files" :key="file.name" class="upload-file-item">
              <img class="extension-icon" :src="file.extension === 'pdf' ? pdfIcon : docIcon" />
              <div class="file-progress-wrapper">
                <div class="file-name">
                  {{ file.name }}
                </div>
                <div class="hwa-progress-bar">
                  <div class="progress">
                    <div
                      class="progress-bar bg-darkblue"
                      role="progressbar"
                      style="width: 100%"
                      aria-valuenow="100"
                      aria-valuemin="0"
                      aria-valuemax="100"
                    />
                  </div>
                </div>
              </div>
              <div class="status-icon">
                <img :src="checkIcon" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="upload-modal_overlay" @click.prevent="toggle" />
  </div>
</template>

<script>
import DropIcon from '../../components/PngIcons/drop_icon.png'
import PdfIcon from '../../components/PngIcons/pdf_icon.png'
import DocIcon from '../../components/PngIcons/doc_icon.png'
import CheckIcon from '../../components/PngIcons/check_icon.png'
export default {
  name: 'UploadModal',
  props: {
    show: { type: Boolean },
    // eslint-disable-next-line vue/require-valid-default-prop
    files: { type: Array, default: [] },
    toggle: { type: Function, default: () => ({}) },
    handleUpload: { type: Function, default: () => ({}) }
  },
  data() {
    return {
      dropIcon: DropIcon,
      pdfIcon: PdfIcon,
      docIcon: DocIcon,
      checkIcon: CheckIcon,
      is_dragover: false
    }
  },

  methods: {
    handleDragDrop($event) {
      this.is_dragover = false
      this.handleUpload($event)
    }
  }
}
</script>
