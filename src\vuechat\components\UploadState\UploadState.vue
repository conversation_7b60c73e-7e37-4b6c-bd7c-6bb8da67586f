<template>
  <div v-if="show" class="upload-state">
    <div v-if="loading" class="upload-state-inner ball-pulse">
      <div />
      <div />
      <div />
    </div>
    <div v-if="error">
      <img :src="errorIcon" alt="Error Icon" />
    </div>
  </div>
</template>

<script>
import ErrorIcon from '../PngIcons/error_icon.png'
export default {
  name: 'UploadState',

  props: {
    show: { type: Boolean, default: false },
    loading: { type: Boolean, default: false },
    error: { type: Boolean, default: true }
  },

  data() {
    return {
      errorIcon: ErrorIcon
    }
  }
}
</script>
