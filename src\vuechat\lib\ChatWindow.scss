.vac-card-window {
  width: 100%;
  height: calc(100vh - 14rem);
  min-width: 1024px;
  display: flex;
  max-width: 100%;
  background: #f8f9fa;
  box-shadow: 0px 3px 12px 3px rgba(0, 0, 0, 0.15);
  color: var(--chat-color);
  overflow-wrap: break-word;
  position: relative;
  white-space: normal;
  // border: var(--chat-container-border);
  // border: 1px solid #ededed;
  // border-radius: var(--chat-container-border-radius);
  border-radius: 0;
  // box-shadow: var(--chat-container-box-shadow);
  -webkit-tap-highlight-color: transparent;

  * {
    font-family: inherit;
  }

  a {
    color: #0d579c;
    font-weight: 500;
  }

  .mr-4 {
    margin-right: 15px;
  }
  .vac-chat-container {
    height: 100%;
    width: 100%;
    display: flex;
    transition: width 0.3s;

    &.profile-visible {
      width: calc(100% - 400px);
    }

    input {
      min-width: 10px;
    }

    textarea,
    input[type='text'],
    input[type='search'] {
      -webkit-appearance: none;
    }
  }

  @media screen and (min-width: 1441px) {
    // height: calc(100vh - 50px - 38px);
    height: calc(100vh - 14rem);
  }

  @media screen and (min-width: 1920px) {
    // height: calc(100vh - 7rem - 38px);
    height: calc(100vh - 14rem);
  }
  @media screen and (max-width: 1919px) {
    height: calc(100vh - 14rem);
  }
}

// .vac-chat-outer {
// 	width: 100%;
// 	height: 100%;

// 	@media screen and (min-width: 1441px) {
// 		top: 19px;
// 		width: 1396px;
// 		height: calc(100% - 38px);
// 		margin: 0 auto;
// 		box-shadow: 0 6px 18px rgba(11, 20, 26, 0.05);
// 	}
// }
