<template>
  <div class="mw-100 d-flex flex-column h-100">
    <div class="vac-card-window d-flex h-100" :style="[cssVars]">
      <div class="vac-chat-container" :class="{ 'profile-visible': showProfile }">
        <!-- start chat  -->
        <div v-show="startchat">
          <start-chat
            v-if="!singleRoom"
            :user-data="userData"
            :current-user-id="currentUserId"
            :rooms="sortedRooms"
            :loading-rooms="loadingRooms"
            :rooms-loaded="roomsLoaded"
            :room="room"
            :room-actions="roomActions"
            :text-messages="t"
            :show-search="showSearch"
            :show-add-room="showAddRoom"
            :show-rooms-list="showRoomsList"
            :text-formatting="textFormatting"
            :link-options="linkOptions"
            :is-mobile="false"
            :toggle-labels-modal="toggleLabelsModal"
            :labels="labels"
            :unread-counts="unreadCounts"
            @fetch-room="fetchRoom"
            @fetch-more-rooms="fetchMoreRooms"
            @loading-more-rooms="loadingMoreRooms = $event"
            @add-room="addRoom"
            @room-action-handler="roomActionHandler"
            @closeChat="closeChat"
          >
            <template v-for="(i, name) in $scopedSlots" #[name]="data">
              <slot :name="name" v-bind="data" />
            </template>
          </start-chat>
        </div>
        <!-- end start chat -->
        <div v-show="!startchat">
          <rooms-list
            v-if="!singleRoom"
            :user-data="userData"
            :current-user-id="currentUserId"
            :rooms="sortedRooms"
            :loading-rooms="loadingRooms"
            :rooms-loaded="roomsLoaded"
            :room="room"
            :room-actions="roomActions"
            :text-messages="t"
            :show-search="showSearch"
            :show-add-room="showAddRoom"
            :show-rooms-list="showRoomsList"
            :text-formatting="textFormatting"
            :link-options="linkOptions"
            :is-mobile="false"
            :toggle-labels-modal="toggleLabelsModal"
            :labels="labels"
            :unread-counts="unreadCounts"
            @fetch-room="fetchRoom"
            @fetch-more-rooms="fetchMoreRooms"
            @loading-more-rooms="loadingMoreRooms = $event"
            @add-room="addRoom"
            @room-action-handler="roomActionHandler"
            @startChat="startChat"
            @handle-search="handleRoomSearch"
          >
            <template v-for="(i, name) in $scopedSlots" #[name]="data">
              <slot :name="name" v-bind="data" />
            </template>
          </rooms-list>
        </div>
        
        <room
          :is-search="isSearch"
          :selected-room="selectedRoom"
          :current-user-id="currentUserId"
          :rooms="rooms"
          :room-id="room.roomId || ''"
          :load-first-room="loadFirstRoom"
          :messages="messages"
          :room-message="roomMessage"
          :messages-loaded="messagesLoaded"
          :menu-actions="menuActions"
          :message-actions="messageActions"
          :show-send-icon="showSendIcon"
          :show-files="showFiles"
          :show-audio="showAudio"
          :audio-bit-rate="audioBitRate"
          :audio-sample-rate="audioSampleRate"
          :show-emojis="showEmojis"
          :show-reaction-emojis="showReactionEmojis"
          :show-new-messages-divider="showNewMessagesDivider"
          :show-footer="showFooter"
          :text-messages="t"
          :single-room="singleRoom"
          :show-rooms-list="showRoomsList"
          :text-formatting="textFormatting"
          :link-options="linkOptions"
          :is-mobile="false"
          :loading-rooms="loadingRooms"
          :room-info-enabled="roomInfoEnabled"
          :textarea-action-enabled="textareaActionEnabled"
          :accepted-files="acceptedFiles"
          :templates-text="templatesText"
          :toggle-labels-modal="toggleLabelsModal"
          :templates="templates"
          :unread-counts="unreadCounts"
          :messageInTransit="messageInTransit"
          @toggle-rooms-list="toggleRoomsList"
          @toggle-error-modal="toggleErrorModal"
          @room-info="roomInfo"
          @fetch-messages="fetchMessages"
          @send-message="sendMessage"
          @edit-message="editMessage"
          @delete-message="deleteMessage"
          @open-file="openFile"
          @open-user-tag="openUserTag"
          @menu-action-handler="menuActionHandler"
          @message-action-handler="messageActionHandler"
          @send-message-reaction="sendMessageReaction"
          @typing-message="typingMessage"
          @textarea-action-handler="textareaActionHandler"
          @carousel-handler="carouselHandler"
          @open-forward-modal="openForwardModal"
          @toggle-menu-bar="$emit('toggle-menu-bar')"
          @redirect-to-hubspot="$emit('redirect-to-hubspot', room)"
        >
          <template v-for="(i, name) in $scopedSlots" #[name]="data">
            <slot :name="name" v-bind="data" />
          </template>
        </room>
      </div>
      <!-- Profile Bar -->
      <profile-bar
        v-if="showProfile"
        :room="room"
        :loading-tab="loadingTab"
        :show-add-to-hubspot="showAddToHubspot"
        :setContactObjects="setContactObjects"
        :showParticipants="showParticipants"
        :show-error-modal="showErrorModal"
        :show-create-modal="showCreateModal"
        :engagement="engagement"
        :participants="participants"
        :error-message="errorMessage"
        :toggle-success-modal="toggleSuccessModal"
        :show-success-modal="showSuccessModal"
        :success-message="successMessage"
        :save-key="saveKey"
        @toggle="toggleProfile"
        @UpdateEngagement="UpdateEngagement"
        @AddNewEngagement="AddNewEngagement"
        @addToHubspot="addToHubspot"
        @getEngagement="getEngagement"
        @getGroupParticipants="getGroupParticipants"
        @redirect-to-hubspot="$emit('redirect-to-hubspot', room)"
      />
    </div>
    <transition name="vac-fade-message">
      <labels-modal
        :room-labels="roomLabels"
        :show="showLabels"
        :toggle="toggleLabelsModal"
        :loading="assigningLabel"
        @chat-label-handler="chatLabelHandler"
      />
    </transition>
    <app-carousel
      :show="showCarousel"
      :close="closeCarousel"
      :images="carouselData"
      @open-forward-modal="openForwardModal"
    />
    <forward-modal
      v-if="showForwardModal"
      :initial-rooms="rooms"
      :close="closeForwardModal"
      :loading-rooms="loadingRooms"
      :rooms-loaded="roomsLoaded"
      @send-forward-message="sendForwardMessage"
      @fetch-more-rooms="fetchMoreRooms"
      @loading-more-rooms="loadingMoreRooms = $event"
    />
    <error-modal :show="showErrorModal" :toggle="toggleErrorModal" :error-message="errorMessage" />
    <success-modal :show="showSuccessModal" :toggle="toggleSuccessModal" :success-message="successMessage" />
    <image-viewer
      :show="previewImage"
      :msg="previewMessage"
      :close="closeImageViewer"
      @open-forward-modal="openForwardModal"
    />
  </div>
</template>

<script>
import RoomsList from './RoomsList/RoomsList'
import LabelsModal from '../components/LabelsModal/LabelsModal'
import AppCarousel from '../components/Carousel/Carousel'
import ForwardModal from '../components/ForwardModal/ForwardModal'
import ErrorModal from '../components/ErrorModal/ErrorModal'
import SuccessModal from '../components/SuccessModal/SuccessModal'
import ImageViewer from '../components/ImageViewer/ImageViewer'
import axios from 'axios'
import Room from './Room/Room'
import ProfileBar from './ProfileBar/ProfileBar'
import locales from '../locales'
import { defaultThemeStyles, cssThemeVars } from '../themes'
import StartChat from './StartChat/StartChatList'
const { roomsValidation, partcipantsValidation } = require('../utils/data-validation')

export default {
  name: 'ChatContainer',
  components: {
    RoomsList,
    Room,
    LabelsModal,
    AppCarousel,
    ForwardModal,
    ErrorModal,
    ProfileBar,
    SuccessModal,
    ImageViewer,
    StartChat
  },

  props: {
    userData: { type: Object, required: true },
    theme: { type: String, default: 'light' },
    styles: { type: Object, default: () => ({}) },
    singleRoom: { type: Boolean, default: false },
    roomsListOpened: { type: Boolean, default: true },
    textMessages: { type: Object, default: null },
    currentUserId: { type: [String, Number], default: '' },
    rooms: { type: Array, default: () => [] },
    loadingRooms: { type: Boolean, default: false },
    roomsLoaded: { type: Boolean, default: false },
    engagement: { type: Object, default: null },
    participants: { type: Object, default: null },
    loadingTab: { type: Boolean, required: false },
    showAddToHubspot: { type: Boolean, required: false },
    setContactObjects: { type: Boolean, required: false },
    showParticipants: { type: Boolean, required: true },
    roomId: { type: [String, Number], default: null },
    loadFirstRoom: { type: Boolean, default: true },
    messages: { type: Array, default: () => [] },
    messagesLoaded: { type: Boolean, default: false },
    roomActions: { type: Array, default: () => [] },
    menuActions: { type: Array, default: () => [] },
    messageActions: {
      type: Array,
      default: () => [
        { name: 'replyMessage', title: 'Reply' }
        /*{
          name: "forwardMessage",
          title: "Forward Message",
        },*/
      ]
    },
    showSearch: { type: Boolean, default: true },
    showAddRoom: { type: Boolean, default: true },
    showSendIcon: { type: Boolean, default: true },
    showFiles: { type: Boolean, default: true },
    showAudio: { type: Boolean, default: true },
    audioBitRate: { type: Number, default: 128 },
    audioSampleRate: { type: Number, default: 44100 },
    showEmojis: { type: Boolean, default: true },
    showReactionEmojis: { type: Boolean, default: true },
    showNewMessagesDivider: { type: Boolean, default: true },
    showFooter: { type: Boolean, default: true },
    textFormatting: { type: Boolean, default: true },
    linkOptions: {
      type: Object,
      default: () => ({ disabled: false, target: '_blank', rel: null })
    },
    roomInfoEnabled: { type: Boolean, default: true },
    textareaActionEnabled: { type: Boolean, default: false },
    roomMessage: { type: String, default: '' },
    acceptedFiles: { type: String, default: '*' },
    templatesText: { type: Array, default: null },
    selectedRoom: { type: Object, default: () => {} },
    labels: { type: Array, required: true },
    showLabels: { type: Boolean, required: true },
    roomLabels: { type: Array, required: true },
    templates: { type: Array, required: true },
    assigningLabel: { type: Boolean, required: true },
    // true: { type: Boolean, required: true },
    toggleErrorModal: { type: Function, required: true },
    toggleSuccessModal: { type: Function, required: true },
    unreadCounts: { type: Object, required: true },
    sidebarVisible: { type: Boolean, required: true },
    showErrorModal: { type: Boolean, required: true },
    requests: { type: Array, required: true },
    request: { type: Object, default: () => {} },
    showCreateModal: { type: Boolean, required: false },
    showSuccessModal: { type: Boolean, required: true },
    saveKey: { type: Boolean, required: false },
    messageInTransit: { type: Boolean, required: true },
    errorMessage: { type: String, default: '' },
    successMessage: {
      type: Object,
      default: () => ({ heading: 'Success', content: 'Successful!' })
    }
  },

  emits: [
    'toggle-rooms-list',
    'room-info',
    'fetch-messages',
    'send-message',
    'edit-message',
    'delete-message',
    'open-file',
    'open-user-tag',
    'menu-action-handler',
    'message-action-handler',
    'send-message-reaction',
    'typing-message',
    'textarea-action-handler',
    'fetch-more-rooms',
    'add-room',
    'room-action-handler',
    'chat-label-handler',
    'toggle-menu-bar',
    'toggle-labels-modal',
    'send-forward-message',
    'close-sidebar',
    'redirect-to-hubspot',
    'UpdateEngagement',
    'AddNewEngagement',
    'addToHubspot',
    'getEngagement',
    'getGroupParticipants',
    'handle-search'
  ],

  data() {
    return {
      room: this.selectedRoom || {},
      carouselData: [],
      forwardMessage: {},
      loadingMoreRooms: false,
      showRoomsList: true,
      isMobile: false,
      showCarousel: false,
      showForwardModal: false,
      showProfile: false,
      previewImage: false,
      previewMessage: {},
      startchat: false,
      isSearch: false,
    }
  },

  computed: {
    t() {
      return {
        ...locales,
        ...this.textMessages
      }
    },
    cssVars() {
      const defaultStyles = defaultThemeStyles[this.theme]
      const customStyles = {}

      Object.keys(defaultStyles).map(key => {
        customStyles[key] = {
          ...defaultStyles[key],
          ...(this.styles[key] || {})
        }
      })

      return cssThemeVars(customStyles)
    },
    sortedRooms() {
      return this.rooms.slice().sort((a, b) => {
        const aVal = a.index || 0
        const bVal = b.index || 0

        if (a.pinned && b.pinned) {
          return aVal > bVal ? -1 : bVal > aVal ? 1 : 0
        } else if (!a.pinned && !b.pinned) {
          return aVal > bVal ? -1 : bVal > aVal ? 1 : 0
        } else {
          return a.pinned ? -1 : 1
        }
      })
    }
  },

  watch: {
    rooms: {
      immediate: true,
      deep: true,
      handler(newVal, oldVal) {
        
        if (!newVal[0] || !newVal.find(room => room.roomId === this.room.roomId)) {
          // console.log('C1',newVal);
          // if(this.isSearch && newVal[0]){
          //   this.fetchRoom({ room: newVal[0] })
          // }
          // 
          this.showRoomsList = true
        }

        if (!this.loadingMoreRooms && this.loadFirstRoom && newVal[0] && (!oldVal || newVal.length !== oldVal.length)) {
          if (this.roomId) {
            const room = newVal.find(r => r.roomId === this.roomId) || {}
            this.fetchRoom({ room })
            // added this
          } else if (this.room && Object.keys(this.room).length !== 0) {
            this.fetchRoom({ room: this.room })
          } else if (!this.isMobile || this.singleRoom) {
            this.fetchRoom({ room: this.sortedRooms[0] })
          } else {
            this.showRoomsList = true
          }
        }
      }
    },

    loadingRooms(val) {
      if (val) this.room = {}
    },

    roomId: {
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal && !this.loadingRooms && this.rooms.length) {
          const room = this.rooms.find(r => r.roomId === newVal)
          this.fetchRoom({ room })
        } else if (oldVal && !newVal) {
          this.room = {}
        }
      }
    },

    room(val) {
      if (!val || Object.entries(val).length === 0) return

      roomsValidation(val)

      val.users.forEach(user => {
        partcipantsValidation(user)
      })
    },

    roomsListOpened(val) {
      this.showRoomsList = val
    },

    sidebarVisible(val) {
      if (val && this.showProfile) {
        this.showProfile = false
      }
    }
  },

  created() {
    this.updateResponsive()
    window.addEventListener('resize', ev => {
      if (ev.isTrusted) this.updateResponsive()
    })
  },

  methods: {
    handleRoomSearch(event){
      this.isSearch = true;
      this.$emit('handle-room-search', event);
    },
    
    closeImageViewer() {
      this.previewImage = false
      this.previewMessage = {}
    },
    updateResponsive() {
      this.isMobile = false
    },
    toggleRoomsList() {
      this.showRoomsList = !this.showRoomsList
      if (this.isMobile) this.room = {}
      this.$emit('toggle-rooms-list', { opened: this.showRoomsList })
    },
    fetchRoom({ room , search}) {
      this.room = room
      
      this.isSearch = (typeof search === 'undefined') ? true : search;
      if (this.request) {
        this.cancel()
      }
      this.fetchMessages({ reset: true })
      if (this.isMobile) this.showRoomsList = false
      if (this.showProfile) this.showProfile = false
    },
    cancel() {
      this.request.cancel()
      this.$emit('clear-old-request', { msg: 'Cancelled' })
    },
    fetchMoreRooms() {
      this.$emit('fetch-more-rooms')
    },
    roomInfo() {
      if (!this.showProfile) this.showProfile = true
      if (this.sidebarVisible) {
        this.$emit('close-sidebar')
      }
      // this.$emit('room-info', this.room)
    },
    addRoom() {
      this.$emit('add-room')
    },
    fetchMessages(options) {
      const axiosSource = axios.CancelToken.source()
      this.$emit('fetch-messages', {
        room: this.room,
        options,
        source: axiosSource,
        search:this.isSearch
      })
    },
    sendMessage(message) {
      this.$emit('send-message', {
        ...message,
        roomId: this.room.roomId,
        phone: this.room.phone
      })
    },
    editMessage(message) {
      this.$emit('edit-message', { ...message, roomId: this.room.roomId })
    },
    deleteMessage(message) {
      this.$emit('delete-message', { message, roomId: this.room.roomId })
    },
    openFile({ message, file }) {
      if (file.action === 'preview') {
        this.previewImage = true
        this.previewMessage = file.file
        return
      }
      this.$emit('open-file', { message, file })
    },
    openUserTag({ user }) {
      this.$emit('open-user-tag', { user })
    },
    menuActionHandler(ev) {
      this.$emit('menu-action-handler', {
        action: ev,
        roomId: this.room.roomId
      })
    },
    roomActionHandler({ action, did }) {
      this.$emit('room-action-handler', {
        action,
        did
      })
    },
    messageActionHandler(ev) {
      if (ev.action.name === 'forwardMessage') {
        this.openForwardModal(ev.message)
      }

      // this.$emit('message-action-handler', {
      // 	...ev,
      // 	roomId: this.room.roomId
      // })
    },
    sendMessageReaction(messageReaction) {
      this.$emit('send-message-reaction', {
        ...messageReaction,
        roomId: this.room.roomId
      })
    },
    typingMessage(message) {
      this.$emit('typing-message', {
        message,
        roomId: this.room.roomId
      })
    },
    UpdateEngagement(value, objectId, label, tab) {
      this.$emit('update-engagement', {
        value,
        objectId,
        label,
        tab,
        roomId: this.room.roomId,
        phone: this.room.phone
      })
    },
    AddNewEngagement(submitForm, tab, ownerId, associations) {
      this.$emit('add-new-engagement', {
        submitForm,
        tab,
        ownerId,
        associations,
        roomId: this.room.roomId,
        phone: this.room.phone
      })
    },
    addToHubspot(phone, room) {
      this.$emit('add-to-hubspot', {
        phone,
        room,
        roomId: this.room.roomId
      })
    },
    getEngagement(value, tab) {
      if (this.request) {
        this.cancel()
      }
      const axiosSource = axios.CancelToken.source()
      this.$emit('get-engagement', {
        value,
        tab,
        roomId: this.room.roomId,
        source: axiosSource
      })
    },
    getGroupParticipants() {
      this.$emit('get-group-participants', {
        roomId: this.room.roomId
      })
    },
    textareaActionHandler(message) {
      this.$emit('textarea-action-handler', {
        message,
        roomId: this.room.roomId
      })
    },
    toggleLabelsModal(room) {
      this.$emit('toggle-labels-modal', room)
    },
    carouselHandler(data) {
      const images = data.map(el => ({
        id: el.name,
        name: el.name + '.' + el.extension,
        big: el.url,
        thumb: el.url,
        timestamp: el.timestamp,
        date: el.date,
        username: el.username,
        msg_id: el._id
      }))
      this.carouselData = images
      this.showCarousel = true
    },
    closeCarousel() {
      this.showCarousel = false
    },
    closeForwardModal() {
      this.showForwardModal = false
      this.forwardMessage = {}
    },
    openForwardModal(msg) {
      this.showForwardModal = true
      this.forwardMessage = msg
    },
    chatLabelHandler({ ids, roomId }) {
      // const value = target.checked
      this.$emit('chat-label-handler', ids, roomId)
    },
    sendForwardMessage(ids) {
      const payload = {
        chatIds: ids,
        msg: this.forwardMessage
      }
      this.$emit('send-forward-message', payload)
      this.closeForwardModal()
    },
    toggleProfile() {
      this.showProfile = !this.showProfile
    },
    startChat() {
      this.startchat = !this.startchat
    },
    closeChat() {
      this.startchat = !this.startchat
    }
  }
}
</script>

<style lang="scss">
@import '../styles/index.scss';
</style>
