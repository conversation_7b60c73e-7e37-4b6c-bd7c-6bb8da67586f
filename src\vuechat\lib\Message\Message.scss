.vac-message-wrapper {
  .vac-card-info {
    border-radius: 4px;
    text-align: center;
    margin: 10px auto;
    font-size: 12px;
    padding: 4px;
    display: block;
    overflow-wrap: break-word;
    position: relative;
    white-space: normal;
    box-shadow: 0 1px 1px -1px rgba(0, 0, 0, 0.1), 0 1px 1px -1px rgba(0, 0, 0, 0.11),
      0 1px 2px -1px rgba(0, 0, 0, 0.11);
  }

  .vac-card-date {
    max-width: 150px;
    // font-weight: 500;
    // text-transform: uppercase;
    // color: var(--chat-message-color-date);
    // background: var(--chat-message-bg-color-date);
    box-shadow: none;
    font-weight: 400;
    font-size: 1.5rem;
    line-height: 2.2rem;
    letter-spacing: 0.003em;
    color: #9ca6af;
    font-style: italic;
    margin: 15px auto;
    padding: 0;
  }

  .vac-card-system {
    max-width: 250px;
    padding: 8px 4px;
    color: var(--chat-message-color-system);
    background: var(--chat-message-bg-color-system);
  }

  .vac-line-new {
    color: var(--chat-message-color-new-messages);
    position: relative;
    text-align: center;
    font-size: 13px;
    padding: 10px 0;
  }

  .vac-line-new:after,
  .vac-line-new:before {
    border-top: 1px solid var(--chat-message-color-new-messages);
    content: '';
    left: 0;
    position: absolute;
    top: 50%;
    width: calc(50% - 60px);
  }

  .vac-line-new:before {
    left: auto;
    right: 0;
  }

  .vac-message-box {
    display: flex;
    flex: 0 0 50%;
    max-width: 50%;
    justify-content: flex-start;
    line-height: 1.4;
  }

  .vac-avatar {
    height: 28px;
    width: 28px;
    min-height: 28px;
    min-width: 28px;
    margin: 0 0 2px 0;
    align-self: flex-end;
  }

  .vac-message-container {
    position: relative;
    padding: 3px 10px;
    align-items: end;
    min-width: 100px;
    box-sizing: content-box;
  }

  .vac-message-container-offset {
    margin-top: 15px;
  }

  .vac-offset-current {
    margin-left: 50%;
    justify-content: flex-end;
  }

  .vac-message-card {
    position: relative;
    background: #ceefe4;
    color: var(--chat-message-color);
    border-radius: 8px;
    border-bottom-left-radius: 0;
    font-size: 14px;
    padding: 2.8rem 2.2rem 2.1rem 2.7rem;
    white-space: pre-line;
    max-width: 100%;
    -webkit-transition-property: box-shadow, opacity;
    transition-property: box-shadow, opacity;
    transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
    will-change: box-shadow;
    box-shadow: 0 1px 1px -1px rgba(0, 0, 0, 0.1), 0 1px 1px -1px rgba(0, 0, 0, 0.11),
      0 1px 2px -1px rgba(0, 0, 0, 0.11);

    &.vac-message-current .vac-reply-message {
      background: rgba(255, 255, 255, 0.2);

      .vac-reply-username {
        color: #d7d7d7;
      }
    }
  }

  .vac-message-highlight {
    box-shadow: 0 1px 2px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.11),
      0 1px 5px -1px rgba(0, 0, 0, 0.11);
  }

  .vac-message-current {
    background: #2c3f51 !important;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 8px;
    .vac-message-wait {
      background: #2c3f51 !important;
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 8px;
    }

    span.msg {
      color: white !important;
    }

    .vac-text-timestamp {
      color: white;
    }

    .vac-progress-time {
      color: #fff;
    }

    #vac-icon-audio-play {
      fill: white;
    }

    #vac-icon-audio-pause {
      fill: white;
    }

    .vac-player-bar .vac-player-progress .vac-line-container {
      background: #777777;
      .vac-line-progress {
        background: white;
      }
      .vac-line-dot {
        background: white;
      }
    }

    .vac-message-files-container .vac-file-container {
      background: #2c3f51;

      .hwa-file-info {
        background: #253646;

        .vac-text-ellipsis {
          color: #fff;
        }
      }
      .hwa-file-meta {
        color: #fff;

        .vac-text-extension {
          color: #fff;
        }
      }
    }

    .video-timestamp {
      color: #fff;
    }
  }

  .vac-message-deleted {
    color: var(--chat-message-color-deleted) !important;
    // font-size: 13px !important;
    font-size: 2rem;
    font-style: italic !important;
    background: var(--chat-message-bg-color-deleted) !important;

    .vac-text-timestamp {
      color: var(--chat-message-color-deleted) !important;
    }
  }

  .vac-icon-deleted {
    height: 18px;
    width: 18px;
    vertical-align: middle;
    margin: -3px 2px 0 0;
    fill: var(--chat-message-color-deleted);
  }

  .vac-message-image {
    position: relative;
    background-color: var(--chat-message-bg-color-image) !important;
    background-size: cover !important;
    background-position: center center !important;
    background-repeat: no-repeat !important;
    height: 321px;
    width: 47rem;
    // min-width: 150px;
    max-width: 100%;
    border-radius: 4px;
    margin: 0 auto 3px;
    transition: 0.4s filter linear;
  }

  .vac-text-username {
    font-size: 1.2rem;
    line-height: 1.5rem;
    color: #919192;
    position: absolute;
    top: -15px;
    left: 7px;
  }

  .vac-username-reply {
    margin-bottom: 5px;
  }

  .vac-text-timestamp {
    font-weight: 400;
    font-size: 1.4rem;
    line-height: 1.7rem;
    margin-top: 2px;
    color: #000;
    text-align: right;
  }

  .vac-progress-time {
    float: left;
    margin: -2px 0 0 40px;
    color: var(--chat-color);
    font-size: 12px;
  }

  .vac-icon-edited {
    -webkit-box-align: center;
    align-items: center;
    display: -webkit-inline-box;
    display: inline-flex;
    justify-content: center;
    letter-spacing: normal;
    line-height: 1;
    text-indent: 0;
    vertical-align: middle;
    margin: 0 4px 2px;

    svg {
      height: 12px;
      width: 12px;
    }
  }

  .vac-icon-check {
    height: 16px;
    width: 16px;
    vertical-align: middle;
    margin: -3px -3px 0 3px;
  }

  .vac-loading {
    filter: blur(2px);
    pointer-events: none;
  }

  .no-event {
    pointer-events: none;
  }

  @media only screen and (max-width: 1919px) {
    .vac-card-date {
      font-size: 12px;
    }

    .vac-text-username {
      font-size: 11px;
    }

    .vac-message-deleted {
      font-size: 13px;
    }

    .vac-icon-deleted {
      height: 14px;
      width: 14px;
      vertical-align: middle;
      margin: -2px 2px 0 0;
      fill: var(--chat-message-color-deleted);
    }

    .vac-message-image {
      height: 250px;
      width: 250px;
    }

    .vac-text-timestamp {
      font-size: 11px;
      line-height: 15px;
    }
  }

  @media only screen and (max-width: 768px) {
    .vac-message-container {
      padding: 2px 3px 1px;
    }

    .vac-message-container-offset {
      margin-top: 2.5rem;
    }

    .vac-message-box {
      flex: 0 0 80%;
      max-width: 80%;
    }

    .vac-avatar {
      height: 25px;
      width: 25px;
      min-height: 25px;
      min-width: 25px;
      margin: 0 6px 1px 0;
    }

    .vac-offset-current {
      margin-left: 20%;
    }

    .vac-progress-time {
      margin-left: 37px;
    }
  }
}
