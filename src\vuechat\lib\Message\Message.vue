<template>
  <div :id="message._id" :ref="message._id" class="vac-message-wrapper">
    <div v-if="showDate" class="vac-card-info vac-card-date ++">
      {{ message.date }}
    </div>
    <div v-if="unreadCounts[roomId]?.msg_id === message._id" class="vac-line-new">
      {{ textMessages.NEW_MESSAGES }}
    </div>

    <div v-if="message.system" class="vac-card-info vac-card-system">
      <format-message
        :content="message.content"
        :users="roomUsers"
        :text-formatting="textFormatting"
        :link-options="linkOptions"
        :msg-search-query="msgSearchQuery"
        @open-user-tag="openUserTag"
      >
        <template v-for="(i, name) in $scopedSlots">
          <slot :name="name" v-bind="data" />
        </template>
      </format-message>
    </div>

    <div v-else class="vac-message-box" :class="{ 'vac-offset-current': message.fromMe }">
      <slot name="message" v-bind="{ message }">
        <div
          class="vac-message-container"
          :class="{
            'vac-message-container-offset': messageOffset
          }"
        >
          <div
            :id="`${message._id}-child`"
            class="vac-message-card"
            :class="{
              'vac-message-highlight': isMessageHover,
              'vac-message-wait': !message.saved && message.fromMe,
              'vac-message-current': message.fromMe,
              'vac-message-deleted': message.deleted,
              'no-event': message.files ? message.files[0].loading : null
            }"
            @mouseover="onHoverMessage"
            @mouseleave="onLeaveMessage"
          >
            <div
              v-if="isGroup && messageOffset"
              class="vac-text-username"
              :class="{
                'vac-username-reply': !message.deleted && message.replyMessage
              }"
            >
              <span>{{ message.username }}</span>
            </div>

            <message-reply
              v-if="!message.deleted && message.replyMessage"
              :message="message"
              :room-users="roomUsers"
              :text-formatting="textFormatting"
              :link-options="linkOptions"
              :is-group="isGroup"
              @reply-msg-handler="$emit('reply-msg-handler', message)"
            >
              <template v-for="(i, name) in $scopedSlots">
                <slot :name="name" v-bind="data" />
              </template>
            </message-reply>

            <div v-if="message.deleted">
              <slot name="deleted-icon">
                <svg-icon name="deleted" class="vac-icon-deleted" />
              </slot>
              <span>{{ textMessages.MESSAGE_DELETED }}</span>
            </div>

            <format-message
              v-else-if="!message.files || !message.files.length"
              :content="message.content"
              :users="roomUsers"
              :text-formatting="textFormatting"
              :link-options="linkOptions"
              :msg-search-query="msgSearchQuery"
              @open-user-tag="openUserTag"
            >
              <template v-for="(i, name) in $scopedSlots">
                <slot :name="name" v-bind="data" />
              </template>
            </format-message>

            <message-files
              v-else-if="!isAudio || message.files.length > 1"
              :current-user-id="currentUserId"
              :message="message"
              :room-users="roomUsers"
              :text-formatting="textFormatting"
              :link-options="linkOptions"
              :msg-search-query="msgSearchQuery"
              @open-file="openFile"
              @carousel-handler="carouselHandler"
            >
              <template v-for="(i, name) in $scopedSlots">
                <slot :name="name" v-bind="data" />
              </template>
            </message-files>

            <template v-else>
              <div :class="{ 'vac-loading': message.files[0].loading }">
                <audio-player
                  :src="message.files[0].url"
                  @update-progress-time="progressTime = $event"
                  @hover-audio-progress="hoverAudioProgress = $event"
                >
                  <template v-for="(i, name) in $scopedSlots">
                    <slot :name="name" v-bind="data" />
                  </template>
                </audio-player>

                <div v-if="!message.deleted" class="vac-progress-time">
                  {{ progressTime }}
                </div>
                <div class="vac-text-timestamp">
                  <div v-if="message.edited && !message.deleted" class="vac-icon-edited">
                    <slot name="pencil-icon">
                      <svg-icon name="pencil" />
                    </slot>
                  </div>
                  <span>{{ message.timestamp }}</span>
                  <span v-if="isCheckmarkVisible">
                    <slot name="checkmark-icon" v-bind="{ message }">
                      <svg-icon
                        :name="
                          message.distributed === 'wait' && !message.saved
                            ? 'wait'
                            : message.distributed
                            ? 'double-checkmark'
                            : 'checkmark'
                        "
                        :param="message.seen ? 'seen' : ''"
                        class="vac-icon-check"
                      />
                    </slot>
                  </span>
                </div>
              </div>
            </template>

            <div v-if="showTimeStamp" class="vac-text-timestamp">
              <div v-if="message.edited && !message.deleted" class="vac-icon-edited">
                <slot name="pencil-icon">
                  <svg-icon name="pencil" />
                </slot>
              </div>
              <span>{{ message.timestamp }}</span>
              <span v-if="isCheckmarkVisible" v-tooltip="message.reason">
                <slot name="checkmark-icon" v-bind="{ message }">
                  <svg-icon
                    :reason="message.reason"
                    :name="
                      message.failed
                        ? 'error'
                        : message.distributed === 'wait' && !message.saved
                        ? 'wait'
                        : message.distributed
                        ? 'double-checkmark'
                        : 'checkmark'
                    "
                    :param="message.seen ? 'seen' : ''"
                    class="vac-icon-check"
                  />
                </slot>
              </span>
            </div>

            <message-actions
              :current-user-id="currentUserId"
              :message="message"
              :message-actions="messageActions"
              :room-footer-ref="roomFooterRef"
              :show-reaction-emojis="showReactionEmojis"
              :hide-options="hideOptions"
              :message-hover="messageHover"
              :hover-message-id="hoverMessageId"
              :hover-audio-progress="hoverAudioProgress"
              :toggle-labels-modal="toggleLabelsModal"
              @hide-options="$emit('hide-options', false)"
              @update-message-hover="messageHover = $event"
              @update-options-opened="optionsOpened = $event"
              @update-emoji-opened="emojiOpened = $event"
              @message-action-handler="messageActionHandler"
              @send-message-reaction="sendMessageReaction"
              @open-forward-modal="openForwardModal"
              @toggle-error-modal="toggleErrorModal"
            >
              <template v-for="(i, name) in $scopedSlots">
                <slot :name="name" v-bind="data" />
              </template>
            </message-actions>
          </div>

          <message-reactions
            :current-user-id="currentUserId"
            :message="message"
            @send-message-reaction="sendMessageReaction"
          />
        </div>
      </slot>
    </div>
  </div>
</template>

<script>
import SvgIcon from '../../components/SvgIcon/SvgIcon'
import FormatMessage from '../../components/FormatMessage/FormatMessage'

import MessageReply from './MessageReply/MessageReply'
import MessageFiles from './MessageFiles/MessageFiles'
import MessageActions from './MessageActions/MessageActions'
import MessageReactions from './MessageReactions/MessageReactions'
import AudioPlayer from './AudioPlayer/AudioPlayer'

const { messagesValidation } = require('../../utils/data-validation')
const { isAudioFile } = require('../../utils/media-file')

export default {
  name: 'Message',
  components: {
    SvgIcon,
    FormatMessage,
    AudioPlayer,
    MessageReply,
    MessageFiles,
    MessageActions,
    MessageReactions
  },

  props: {
    currentUserId: { type: [String, Number], required: true },
    textMessages: { type: Object, required: true },
    index: { type: Number, required: true },
    message: { type: Object, required: true },
    messages: { type: Array, required: true },
    editedMessage: { type: Object, required: true },
    roomUsers: { type: Array, default: () => [] },
    messageActions: { type: Array, required: true },
    roomFooterRef: { type: HTMLDivElement, default: null },
    newMessages: { type: Array, default: () => [] },
    showReactionEmojis: { type: Boolean, required: true },
    showNewMessagesDivider: { type: Boolean, required: true },
    textFormatting: { type: Boolean, required: true },
    linkOptions: { type: Object, required: true },
    hideOptions: { type: Boolean, required: true },
    msgSearchQuery: { type: String, required: true },
    toggleLabelsModal: { type: Function, default: () => ({}) },
    isGroup: { type: Boolean, default: false },
    unreadCounts: { type: Object, required: true },
    roomId: { type: [String, Number], required: true }
  },

  emits: [
    'hide-options',
    'message-added',
    'open-file',
    'open-user-tag',
    'message-action-handler',
    'send-message-reaction',
    'carousel-handler',
    'open-forward-modal',
    'reply-msg-handler'
  ],

  data() {
    return {
      hoverMessageId: null,
      messageHover: false,
      optionsOpened: false,
      emojiOpened: false,
      newMessage: {},
      progressTime: '- : -',
      hoverAudioProgress: false
    }
  },

  computed: {
    showTimeStamp() {
      return !this.message.files || (this.message.files && this.message.deleted)
    },
    showDate() {
      return this.index > 0 && this.message.date !== this.messages[this.index - 1].date
    },
    messageOffset() {
      return this.index === 0
        ? true
        : this.index > 0 && this.message.username !== this.messages[this.index - 1].username
    },
    isMessageHover() {
      return this.editedMessage._id === this.message._id || this.hoverMessageId === this.message._id
    },
    isAudio() {
      return this.message.files?.some(file => isAudioFile(file))
    },

    isCheckmarkVisible() {
      return (
        this.message.fromMe &&
        !this.message.deleted &&
        (this.message.saved ||
          this.message.distributed ||
          this.message.seen ||
          this.message.distributed === 'wait' ||
          this.message.failed)
      )
    }
  },

  watch: {
    newMessages: {
      immediate: true,
      deep: true,
      handler(val) {
        if (!val.length || !this.showNewMessagesDivider) {
          return (this.newMessage = {})
        }

        this.newMessage = val.reduce((res, obj) => (obj.index < res.index ? obj : res))
      }
    }
  },

  mounted() {
    messagesValidation(this.message)

    this.$emit('message-added', {
      message: this.message,
      index: this.index,
      ref: this.$refs[this.message._id]
    })
  },

  methods: {
    onHoverMessage() {
      this.messageHover = true
      if (this.canEditMessage()) this.hoverMessageId = this.message._id
    },
    canEditMessage() {
      return !this.message.deleted
    },
    onLeaveMessage() {
      if (!this.optionsOpened && !this.emojiOpened) this.messageHover = false
      this.hoverMessageId = null
    },
    openFile(file) {
      this.$emit('open-file', { message: this.message, file })
    },
    openUserTag(user) {
      this.$emit('open-user-tag', { user })
    },
    messageActionHandler(action) {
      this.messageHover = false
      this.hoverMessageId = null

      setTimeout(() => {
        this.$emit('message-action-handler', { action, message: this.message })
      }, 300)
    },
    sendMessageReaction({ emoji, reaction }) {
      this.$emit('send-message-reaction', {
        messageId: this.message._id,
        reaction: emoji,
        remove: reaction && reaction.indexOf(this.currentUserId) !== -1
      })
      this.messageHover = false
    },
    carouselHandler(data) {
      this.$emit('carousel-handler', data)
    },
    openForwardModal(msg) {
      this.$emit('open-forward-modal', msg)
    },
    toggleErrorModal() {
      this.$emit('toggle-error-modal')
    }
  }
}
</script>
