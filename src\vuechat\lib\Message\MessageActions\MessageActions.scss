.vac-message-actions-wrapper {
  .vac-options-container {
    position: absolute;
    top: 2px;
    right: 8px;
    height: 40px;
    width: 50px;
    overflow: hidden;
    border-top-right-radius: 8px;
  }

  .vac-options-container.files-variant {
    top: 2px;
    right: 2px;
    border-top-right-radius: 6px;
  }

  .vac-blur-container {
    position: absolute;
    height: 100%;
    width: 100%;
    left: 8px;
    bottom: 10px;
    background: #eef3fb;
    // filter: blur(3px);
    border-bottom-left-radius: 8px;
  }

  .vac-options-me {
    background: #2c3f51;
  }

  .vac-message-options {
    background: var(--chat-icon-bg-dropdown-message);
    border-radius: 50%;
    position: absolute;
    top: 7px;
    right: 7px;

    svg {
      height: 17px;
      width: 17px;
      padding: 5px;
      margin: -5px;
      box-sizing: content-box;
    }
  }

  .vac-message-emojis {
    position: absolute;
    top: 6px;
    right: 30px;
  }

  .vac-menu-options {
    right: 15px;
  }

  .vac-menu-left {
    right: -118px;
  }

  .message-4wd-icon {
    width: 25px;
    height: 25px;
    background: #d9d9d9;
    /* padding: 27px; */
    border-radius: 50%;
    text-align: center;
    right: -35px;
    top: 50%;
    position: absolute;
    transform: translateY(-50%);

    &.from-me {
      left: -35px;
    }

    svg {
      width: 15px;
      height: 15px;
      transform: translate(-1px, 1px);
    }

    &:hover {
      cursor: pointer;
    }
  }

  .message-error-icon {
    // width: 25px;
    // height: 25px;
    border-radius: 50%;
    text-align: center;
    right: -5.2rem;
    top: 50%;
    position: absolute;
    transform: translateY(-50%);

    img {
      width: 3.8rem;
      height: 3.5rem;
      // transform: translate(-1px, 1px);
    }

    &:hover {
      cursor: pointer;
    }
  }

  @media only screen and (max-width: 768px) {
    .vac-options-container {
      right: 3px;
    }

    .vac-menu-left {
      right: -50px;
    }
  }
}
