<template>
  <div class="vac-message-actions-wrapper">
    <div
      v-if="!message.error && !isInstaPage"
      class="vac-options-container"
      :class="{ 'files-variant': message.files }"
      :style="{
        display: hoverAudioProgress ? 'none' : 'initial',
        width: filteredMessageActions.length && showReactionEmojis ? '45px' : '45px'
      }"
    >
      <transition-group name="vac-slide-left" tag="span">
        <div
          v-if="isMessageActions || isMessageReactions"
          key="1"
          class="vac-blur-container"
          :class="{
            'vac-options-me': message.fromMe === 1
          }"
        />

        <div
          v-if="isMessageActions"
          ref="actionIcon"
          key="2"
          class="vac-svg-button vac-message-options"
          @click="openOptions"
        >
          <slot name="dropdown-icon">
            <svg-icon name="dropdown" param="message" />
          </slot>
        </div>
      </transition-group>
    </div>

    <transition
      v-if="filteredMessageActions.length"
      :name="message.fromMe === 1 ? 'vac-slide-left' : 'vac-slide-right'"
    >
      <div
        v-if="optionsOpened"
        ref="menuOptions"
        v-click-outside="closeOptions"
        class="vac-menu-options"
        :class="{
          'vac-menu-left': !message.fromMe
        }"
        :style="{ top: `${menuOptionsTop}px` }"
      >
        <div class="vac-menu-list">
          <div v-for="action in filteredMessageActions" :key="action.name">
            <div class="vac-menu-item" @click="messageActionHandler(action)">
              {{ action.title }}
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- Forward Icon -->
    <!-- <div
			v-if="message.files && !message.deleted && !message.error"
			class="message-4wd-icon"
			:class="{ 'from-me': message.fromMe }"
			@click.prevent="$emit('open-forward-modal', message)"
		>
			<svg
				xmlns="http://www.w3.org/2000/svg"
				viewBox="3.886999999999998 6.484999999999999 16.666 12.352000000000004"
			>
				<path
					d="M14.248 6.973a.688.688 0 0 1 1.174-.488l5.131 5.136a.687.687 0 0 1 0 .973l-5.131 5.136a.688.688 0 0 1-1.174-.488v-2.319c-4.326 0-7.495 1.235-9.85 3.914-.209.237-.596.036-.511-.268 1.215-4.391 4.181-8.492 10.361-9.376v-2.22z"
					fill="#fff"
				/>
			</svg>
		</div> -->
    <!-- Error Icon -->

    <div v-if="message.error" class="message-error-icon" @click.prevent="$emit('toggle-error-modal')">
      <img :src="errorIcon" alt="Error Icon" />
    </div>
  </div>
</template>

<script>
import vClickOutside from 'v-click-outside'

import SvgIcon from '../../../components/SvgIcon/SvgIcon'
import ErrorIcon from '../../../components/PngIcons/error_icon.png'

export default {
  name: 'MessageActions',
  components: {
    SvgIcon
  },

  directives: {
    clickOutside: vClickOutside.directive
  },

  props: {
    message: { type: Object, required: true },
    messageActions: { type: Array, required: true },
    roomFooterRef: { type: HTMLDivElement, default: null },
    showReactionEmojis: { type: Boolean, required: true },
    hideOptions: { type: Boolean, required: true },
    messageHover: { type: Boolean, required: true },
    hoverMessageId: { type: [String, Number], default: null },
    hoverAudioProgress: { type: Boolean, required: true }
  },

  emits: [
    'update-emoji-opened',
    'update-options-opened',
    'update-message-hover',
    'hide-options',
    'message-action-handler',
    'send-message-reaction',
    'open-forward-modal',
    'toggle-message-forward'
  ],

  data() {
    return {
      menuOptionsTop: 0,
      optionsOpened: false,
      optionsClosing: false,
      emojiOpened: false,
      errorIcon: ErrorIcon
    }
  },

  computed: {
    isInstaPage(){      
      let check = window.location.hash.includes('insta');
      return check;
    },
    isMessageActions() {
      return (
        this.filteredMessageActions.length &&
        this.messageHover &&
        !this.message.deleted &&
        !this.message.disableActions &&
        !this.hoverAudioProgress
      )
    },
    isMessageReactions() {
      return (
        this.showReactionEmojis &&
        this.messageHover &&
        !this.message.deleted &&
        !this.message.disableReactions &&
        !this.hoverAudioProgress
      )
    },
    filteredMessageActions() {
      return this.message.fromMe === 1 ? this.messageActions : this.messageActions.filter(message => !message.onlyMe)
    }
  },

  watch: {
    emojiOpened(val) {
      this.$emit('update-emoji-opened', val)
      if (val) this.optionsOpened = false
    },
    hideOptions(val) {
      if (val) {
        this.closeEmoji()
        this.closeOptions()
      }
    },
    optionsOpened(val) {
      this.$emit('update-options-opened', val)
    }
  },

  methods: {
    openOptions() {
      if (this.optionsClosing) return

      this.optionsOpened = !this.optionsOpened
      if (!this.optionsOpened) return

      this.$emit('hide-options', false)

      setTimeout(() => {
        if (!this.roomFooterRef || !this.$refs.menuOptions || !this.$refs.actionIcon) {
          return
        }

        const menuOptionsTop = this.$refs.menuOptions.getBoundingClientRect().height

        const actionIconTop = this.$refs.actionIcon.getBoundingClientRect().top
        const roomFooterTop = this.roomFooterRef.getBoundingClientRect().top

        const optionsTopPosition = roomFooterTop - actionIconTop > menuOptionsTop + 50

        if (optionsTopPosition) this.menuOptionsTop = 30
        else this.menuOptionsTop = -menuOptionsTop
      })
    },
    closeOptions() {
      this.optionsOpened = false
      this.optionsClosing = true
      this.updateMessageHover()
      setTimeout(() => (this.optionsClosing = false), 100)
    },

    closeEmoji() {
      this.emojiOpened = false
      this.updateMessageHover()
    },
    updateMessageHover() {
      if (this.hoverMessageId !== this.message._id) {
        this.$emit('update-message-hover', false)
      }
    },
    messageActionHandler(action) {
      this.closeOptions()

      this.$emit('message-action-handler', action)
    }
  }
}
</script>
