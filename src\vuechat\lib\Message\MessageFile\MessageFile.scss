.vac-message-file-container {
  position: relative;

  .vac-message-image-container {
    cursor: pointer;
  }

  .vac-loading {
    filter: blur(3px);
  }

  .vac-image-buttons {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 4px;
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0) 55%,
      rgba(0, 0, 0, 0.02) 60%,
      rgba(0, 0, 0, 0.05) 65%,
      rgba(0, 0, 0, 0.1) 70%,
      rgba(0, 0, 0, 0.2) 75%,
      rgba(0, 0, 0, 0.3) 80%,
      rgba(0, 0, 0, 0.5) 85%,
      rgba(0, 0, 0, 0.6) 90%,
      rgba(0, 0, 0, 0.7) 95%,
      rgba(0, 0, 0, 0.8) 100%
    );
    // background: linear-gradient(
    // 	to bottom,
    // 	rgba(0, 0, 0, 0) 55%,
    // 	rgba(0, 0, 0, 0) 60%,
    // 	rgba(0, 0, 0, 0) 65%,
    // 	rgba(0, 0, 0, 0) 70%,
    // 	rgba(0, 0, 0, 0) 75%,
    // 	rgba(0, 0, 0, 0) 80%,
    // 	rgba(0, 0, 0, 0.01) 85%,
    // 	rgba(0, 0, 0, 0.1) 90%,
    // 	rgba(0, 0, 0, 0.2) 95%,
    // 	rgba(0, 0, 0, 0.3) 100%
    // );

    // svg {
    // 	height: 26px;
    // 	width: 26px;
    // }

    .vac-button-view,
    .vac-button-download {
      position: absolute;
      bottom: 6px;
      left: 7px;
    }

    // :first-child {
    // 	left: 40px;
    // }

    .vac-button-view {
      max-width: 18px;
      bottom: 8px;
    }

    .hwa-text-timestamp {
      position: absolute;
      bottom: 3px;
      right: 7px;
      font-size: 1.5rem;
      line-height: 2.2rem;

      color: #ffffff;

      text-shadow: 0px 4px 10px rgba(152, 152, 152, 0.25);
    }
  }

  .vac-video-container {
    width: 47rem;
    max-width: 100%;
    margin: auto;

    video {
      border-radius: 4px;
    }
  }

  .mb-8 {
    margin-bottom: 8px;
  }

  @media only screen and (max-width: 1919px) {
    .vac-video-container {
      width: 350px;
      max-width: 100%;
      margin: auto;

      // &:last-child {
      // 	margin: 0 auto 15px;
      // }

      video {
        border-radius: 4px;
      }
    }
  }
}
