<template>
  <div class="vac-message-file-container">
    <div
      v-if="isImage"
      :ref="'imageRef' + index"
      class="vac-message-image-container"
      @mouseover="imageHover = true"
      @mouseleave="imageHover = false"
      @click.stop="openFile('preview')"
    >
      <upload-state :show="inUploadState" :loading="false" :error="file.error" />
      <div
        class="vac-message-image"
        :class="{
          'vac-loading': inUploadState && message.fromMe === 1,
          'mb-8': message.content
        }"
        :style="{
          'background-image': `url('${inUploadState ? file.localUrl || file.url : file.url}')`,
          'max-height': `${imageResponsive.maxHeight}px`
        }"
      >
        <transition name="vac-fade-image">
          <div v-if="!inUploadState" class="vac-image-buttons">
            <div class="hwa-text-timestamp">
              <span class="d-inline-block">{{ message.timestamp }}</span>
              <span v-if="isCheckmarkVisible" v-tooltip="message.reason">
                <slot name="checkmark-icon" v-bind="{ file }">
                  <svg-icon
                    :reason="message.reason"
                    :name="
                      message.failed
                        ? 'error'
                        : message.distributed === 'wait' && !message.saved
                        ? 'wait'
                        : message.distributed
                        ? 'double-checkmark'
                        : 'checkmark'
                    "
                    :param="message.seen ? 'seen' : ''"
                    class="vac-icon-check"
                  />
                </slot>
              </span>
            </div>
          </div>
        </transition>
      </div>
    </div>

    <div
      v-else-if="isVideo"
      class="vac-video-container"
      :class="{
        'mb-2': isAudio
      }"
    >
      <upload-state :show="inUploadState" :loading="false" :error="file.error" />
      <div
        :class="{
          'vac-loading': inUploadState && message.fromMe === 1
        }"
      >
        <video width="100%" height="100%" controls>
          <source :src="inUploadState ? file.localUrl : file.url" />
        </video>
      </div>
    </div>
  </div>
</template>

<script>
import UploadState from '../../../components/UploadState/UploadState'
import SvgIcon from '../../../components/SvgIcon/SvgIcon'

const { isImageFile, isVideoFile, isAudioFile } = require('../../../utils/media-file')

export default {
  name: 'MessageFile',
  components: { SvgIcon, UploadState },

  props: {
    message: { type: Object, required: true },
    file: { type: Object, required: true },
    index: { type: Number, required: true }
  },

  emits: ['open-file'],

  data() {
    return {
      imageResponsive: '',
      imageLoading: false,
      imageHover: false
    }
  },

  computed: {
    isAudio() {
      return isAudioFile(this.file)
    },

    inUploadState() {
      return this.file.loading || this.file.error
    },
    isImage() {
      return isImageFile(this.file)
    },
    isVideo() {
      return isVideoFile(this.file)
    },
    isCheckmarkVisible() {
      return (
        this.message.fromMe &&
        !this.message.deleted &&
        (this.message.saved || this.message.distributed || this.message.seen)
      )
    }
  },

  watch: {
    file: {
      immediate: true,
      handler() {
        this.checkImgLoad()
      }
    }
  },

  mounted() {
    const ref = this.$refs['imageRef' + this.index]

    if (ref) {
      this.imageResponsive = {
        maxHeight: ref.clientWidth - 18,
        loaderTop: ref.clientHeight / 2 - 9
      }
    }
  },

  methods: {
    checkImgLoad() {
      if (!isImageFile(this.file)) return
      this.imageLoading = true
      const image = new Image()
      image.src = this.file.url
      image.addEventListener('load', () => (this.imageLoading = false))
    },
    openFile(action) {
      if (!this.inUploadState) {
        this.$emit('open-file', { file: this.file, action })
      }
    }
  }
}
</script>
