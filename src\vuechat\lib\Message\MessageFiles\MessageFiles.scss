.vac-message-files-container {
  margin: -2.3rem -1.7rem -2rem -2.2rem;
  // border-radius: 8px;

  &.with-reply {
    margin: 0;
  }

  .vac-blur {
    filter: blur(3px);
  }

  .hwa-multiple-images {
    width: 49.5%;
    // min-width: 150px;
    height: auto;
    float: left;
    overflow: hidden;
    position: relative;
    cursor: pointer;

    &.more-images::after {
      content: '';
      width: 100%;
      left: 0;
      z-index: 2;
      top: 0;
      position: absolute;
      height: calc(100% - 3px);
      background: rgba(0, 0, 0, 0.5);
    }

    &.more-images-loading {
      cursor: inherit;
    }

    .images-num {
      position: absolute;
      top: 50%;
      left: 50%;
      color: white;
      font-size: 4rem;
      transform: translate(-50%, -50%);
      z-index: 3;
    }

    &:nth-of-type(odd) {
      margin-right: 1%;
    }
  }

  .msg {
    margin: 8px;
    margin-top: 0;
    display: block;
  }

  .vac-file-container {
    // height: 26rem;
    width: 47rem;
    // min-width: 100%;
    // width: 47rem;
    // width: 250px;
    overflow: hidden;
    max-width: 100%;
    background: #eef3fb;
    cursor: pointer;
    transition: all 0.6s;
    padding: 0;
    border: none;
    border-radius: 8px;
    overflow: hidden;
    margin: auto;

    .hwa-file-display {
      min-width: 100%;
      max-width: 100%;
      height: 26rem;
      width: 47rem;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;

      // .pdf-embed {
      // 	width: 100%;
      // 	overflow: hidden;
      // 	height: 100%;
      // 	pointer-events: none;
      // }
    }

    .hwa-file-info {
      display: flex;
      width: 100%;
      background: darken(#e8eef6, 5%);
      align-items: center;
      height: 5.8rem;
      min-height: 45px;
      padding: 0 2rem;
      color: #242424;
      margin-bottom: 5px;

      .file-icon {
        height: 30px;
      }

      .vac-text-ellipsis {
        font-weight: 500;
        font-size: 1.8rem;
        line-height: 2.7rem;
        text-align: left;
        margin: 0 1.7rem;
        color: #242424;
      }

      .download-icon {
        height: 3.5rem;
        min-height: 25px;
      }

      @media screen and (max-width: 1919px) {
        .vac-text-ellipsis {
          font-size: 13px;
        }
      }
    }

    .hwa-file-meta {
      display: flex;
      width: 100%;
      margin: 7px 5px;
      align-items: center;
      justify-content: space-between;
      font-weight: 500;
      font-size: 15px;
      line-height: 18px;
      color: #000;
      text-shadow: 0px 4px 10px rgba(152, 152, 152, 0.25);

      .file-info {
        display: flex;
        width: 50%;
      }

      .page-size {
        white-space: nowrap;
      }

      .vac-text-extension {
        font-size: 15px;
        line-height: 18px;
        color: #000;
        margin: 0;
        text-transform: uppercase;
        position: relative;
        padding-right: 15px;

        &::after {
          content: '.';
          position: absolute;
          right: 7.5px;
          bottom: 0.3rem;
        }

        &.document {
          padding: 0 15px;
          &::before {
            content: '.';
            position: absolute;
            left: 7.5px;
            bottom: 0.3rem;
          }

          &::after {
            content: '.';
            position: absolute;
            right: 7.5px;
            bottom: 0.3rem;
          }
        }
      }
    }

    &:hover {
      opacity: 0.85;
    }
  }

  .hwa-video-container {
    .video-timestamp {
      // color: #000;
      font-weight: 400;
      font-size: 1.4rem;
      line-height: 1.7rem;
      margin-right: 10px;
      text-align: right;
      margin-bottom: 3px;
    }
  }

  @media screen and (max-width: 1919px) {
    .vac-file-container {
      width: 250px;

      .hwa-file-meta {
        font-size: 9px;
        line-height: 9px;

        .page-size {
          white-space: nowrap;
        }

        .vac-text-extension {
          font-size: 9px;
          line-height: 9px;
          padding-right: 15px;

          &::after {
            right: 1rem;
            bottom: 0.3rem;
          }

          &.document {
            padding: 0 15px;
            &::before {
              left: 1rem;
              bottom: 0.3rem;
            }

            &::after {
              right: 1rem;
              bottom: 0.3rem;
            }
          }
        }
      }
    }

    .hwa-video-container {
      .video-timestamp {
        font-size: 11px;
        line-height: 15px;
      }
    }
  }
}

.text_right {
  width: 100%;
  text-align: right;
}
