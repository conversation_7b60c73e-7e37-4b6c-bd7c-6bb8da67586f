<template>
  <div class="vac-message-files-container" :class="{ 'with-reply': message.replyMessage }">
    <div class="clearfix">
      <!-- Image file start -->
      <div v-if="imageFiles.length === 1" class="hwa-single-image">
        <message-file
          :file="imageFiles[0]"
          :current-user-id="currentUserId"
          :message="message"
          :index="0"
          @open-file="$emit('open-file', $event)"
        >
          <template v-for="(i, name) in $scopedSlots" #[name]="data">
            <slot :name="name" v-bind="data" />
          </template>
        </message-file>
      </div>

      <div v-else-if="imageFiles.length <= 3">
        <div v-for="(file, idx) in imageFiles" :key="idx + 'iv'" class="hwa-single-image">
          <message-file
            :file="file"
            :current-user-id="currentUserId"
            :message="message"
            :index="idx"
            @open-file="$emit('open-file', $event)"
          >
            <template v-for="(i, name) in $scopedSlots" #[name]="data">
              <slot :name="name" v-bind="data" />
            </template>
          </message-file>
        </div>
      </div>

      <div v-else-if="imageFiles.length === 4">
        <div v-for="(file, idx) in imageFiles" :key="idx + 'iv'" class="hwa-multiple-images">
          <message-file
            :file="file"
            :current-user-id="currentUserId"
            :message="message"
            :index="idx"
            @open-file="$emit('open-file', $event)"
          >
            <template v-for="(i, name) in $scopedSlots" #[name]="data">
              <slot :name="name" v-bind="data" />
            </template>
          </message-file>
        </div>
      </div>

      <div v-else>
        <div v-for="(file, idx) in imageFiles.slice(0, 3)" :key="idx + 'iv'" class="hwa-multiple-images">
          <message-file
            :file="file"
            :current-user-id="currentUserId"
            :message="message"
            :index="idx"
            @open-file="$emit('open-file', $event)"
          >
            <template v-for="(i, name) in $scopedSlots" #[name]="data">
              <slot :name="name" v-bind="data" />
            </template>
          </message-file>
        </div>
        <div
          class="hwa-multiple-images more-images"
          :class="{ 'more-images-loading': imageFiles[3].loading }"
          @click.prevent="handleCarousel(imageFiles, imageFiles[3])"
        >
          <message-file
            :file="imageFiles[3]"
            :current-user-id="currentUserId"
            :message="message"
            :index="3"
            @open-file="$emit('open-file', $event)"
          >
            <template v-for="(i, name) in $scopedSlots" #[name]="data">
              <slot :name="name" v-bind="data" />
            </template>
          </message-file>
          <span class="images-num"> +{{ imageFiles.length - 4 }} </span>
        </div>
      </div>
    </div>
    <!-- Image file end -->

    <!-- Video File start -->
    <div v-if="videoFiles">
      <div v-for="(file, idx) in videoFiles" :key="idx + 'iv'" class="hwa-video-container">
        <message-file
          :file="file"
          :current-user-id="currentUserId"
          :message="message"
          :index="idx"
          @open-file="$emit('open-file', $event)"
        >
          <template v-for="(i, name) in $scopedSlots" #[name]="data">
            <slot :name="name" v-bind="data" />
          </template>
        </message-file>
        <div class="video-timestamp">
          <span>{{ message.timestamp }}</span>
          <span v-if="isCheckmarkVisible">
            <slot name="checkmark-icon" v-bind="{ message }">
              <svg-icon
                :name="message.distributed ? 'double-checkmark' : 'checkmark'"
                :param="message.seen ? 'seen' : ''"
                class="vac-icon-check"
              />
            </slot>
          </span>
        </div>
      </div>
    </div>
    <!-- Video File End -->

    <div v-for="(file, idx) in otherFiles" :key="idx + 'a'" class="position-relative">
      <upload-state :show="file.loading || file.error" :loading="false" :error="file.error" />
      <div class="vac-file-container" @click.stop="openFile(file, 'download')">
        <div class="hwa-file-display">
          <embed
            v-if="file.extension === 'pdf'"
            :class="{ 'vac-blur': file.loading || file.error }"
            :src="`${file.loading ? file.localUrl : file.url}`"
            type="application/pdf"
          />
          <img
            v-else
            :src="file.extension === 'pdf' ? pdfIcon : docIcon"
            alt="Pdf Icon"
            class="file-icon"
            :class="{ 'vac-blur': file.loading || file.error }"
          />
        </div>
        <div class="hwa-file-info">
          <img :src="file.extension === 'pdf' ? pdfIcon : docIcon" alt="Pdf Icon" class="file-icon" />
          <div class="vac-text-ellipsis">
             {{ decodeURIComponent(file.extension ? `${file.name}.${file.extension}` : file.name) }}
          </div>
          <img
            :src="message.fromMe === 1 ? downloadIcon : downloadIconDark"
            alt="Download Icon"
            class="download-icon"
          />
        </div>

        <div class="hwa-file-meta">
          <div class="file-info" />
          <div class="mr-4">
            <span>{{ message.timestamp }}</span>
            <span v-if="isCheckmarkVisible">
              <slot name="checkmark-icon" v-bind="{ message }">
                <svg-icon
                  :name="message.distributed ? 'double-checkmark' : 'checkmark'"
                  :param="message.seen ? 'seen' : ''"
                  class="vac-icon-check"
                />
              </slot>
            </span>
          </div>
        </div>
      </div>
    </div>

    <format-message
      :content="message.content"
      :users="roomUsers"
      :text-formatting="textFormatting"
      :link-options="linkOptions"
      :msg-search-query="msgSearchQuery"
      @open-user-tag="$emit('open-user-tag')"
    >
      <template v-for="(i, name) in $scopedSlots" #[name]="data">
        <slot :name="name" v-bind="data" />
      </template>
    </format-message>
  </div>
</template>

<script>
import SvgIcon from '../../../components/SvgIcon/SvgIcon'
import PdfIcon from '../../../components/PngIcons/pdf_icon.png'
import DocIcon from '../../../components/PngIcons/doc_icon.png'
import DownloadIcon from '../../../components/PngIcons/download_icon.png'
import DownloadIconDark from '../../../components/PngIcons/download_icon-dark.png'
import FormatMessage from '../../../components/FormatMessage/FormatMessage'
import MessageFile from '../MessageFile/MessageFile'
import UploadState from '../../../components/UploadState/UploadState'

const { isImageVideoFile, isVideoFile, isImageFile } = require('../../../utils/media-file')

export default {
  name: 'MessageFiles',
  components: { SvgIcon, FormatMessage, MessageFile, UploadState },

  props: {
    currentUserId: { type: [String, Number], required: true },
    message: { type: Object, required: true },
    roomUsers: { type: Array, required: true },
    textFormatting: { type: Boolean, required: true },
    linkOptions: { type: Object, required: true },
    msgSearchQuery: { type: String, required: true }
  },

  emits: ['open-file', 'open-user-tag', 'carousel-handler'],

  data() {
    return {
      pdfIcon: PdfIcon,
      docIcon: DocIcon,
      downloadIcon: DownloadIcon,
      downloadIconDark: DownloadIconDark
    }
  },

  computed: {
    imageFiles() {
      return this.message.files.filter(file => isImageFile(file))
    },
    videoFiles() {
      return this.message.files.filter(file => isVideoFile(file))
    },
    otherFiles() {
      return this.message.files.filter(file => !isImageVideoFile(file))
    },
    isCheckmarkVisible() {
      return (
        this.message.fromMe &&
        !this.message.deleted &&
        (this.message.saved || this.message.distributed || this.message.seen)
      )
    }
  },

  methods: {
    openFile(file, action) {
      if (!file.loading && !file.error) {
        this.$emit('open-file', { file, action })
      }
    },
    handleCarousel(imageFiles, file) {
      if (!file.loading && !file.error) {
        this.$emit('carousel-handler', imageFiles)
      }
    }
  }
}
</script>
