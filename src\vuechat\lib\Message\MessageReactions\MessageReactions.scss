.vac-button-reaction {
  display: inline-flex;
  align-items: center;
  border: var(--chat-message-border-style-reaction);
  outline: none;
  background: var(--chat-message-bg-color-reaction);
  border-radius: 4px;
  margin: 4px 2px 0;
  transition: 0.3s;
  padding: 0 5px;
  font-size: 18px;
  line-height: 23px;

  span {
    font-size: 11px;
    font-weight: 500;
    min-width: 7px;
    color: var(--chat-message-color-reaction-counter);
  }

  &:hover {
    border: var(--chat-message-border-style-reaction-hover);
    background: var(--chat-message-bg-color-reaction-hover);
    cursor: pointer;
  }

  &.vac-reaction-me {
    border: var(--chat-message-border-style-reaction-me);
    background: var(--chat-message-bg-color-reaction-me);

    span {
      color: var(--chat-message-color-reaction-counter-me);
    }

    &:hover {
      border: var(--chat-message-border-style-reaction-hover-me);
      background: var(--chat-message-bg-color-reaction-hover-me);
    }
  }
}
