<template>
  <div class="vac-reply-message" @click="$emit('reply-msg-handler', message)">
    <div v-if="isGroup" class="vac-reply-username">
      {{ replyUsername }}
    </div>

    <div v-if="isImage" class="vac-image-reply-container">
      <div
        class="vac-message-image vac-message-image-reply"
        :style="{
          'background-image': `url('${firstFile.url}')`
        }"
      />
    </div>

    <div v-else-if="isVideo" class="vac-video-reply-container">
      <video width="100%" height="100%" controls>
        <source :src="firstFile.url" />
      </video>
    </div>

    <div v-else-if="containsFile" class="vac-reply-username hwa-filename">
      <img class="d-block" :src="fileIcon" alt="File Icon" height="30" />

      <span class="name">{{ `${firstFile.name}.` }}</span>
      <span class="ext">{{ `${firstFile.extension}` }}</span>
    </div>

    <div class="vac-reply-content">
      <format-message
        :content="message.replyMessage.content"
        :users="roomUsers"
        :text-formatting="textFormatting"
        :link-options="linkOptions"
        :reply="true"
      >
        <template v-for="(i, name) in $scopedSlots" #[name]="data">
          <slot :name="name" v-bind="data" />
        </template>
      </format-message>
    </div>
  </div>
</template>

<script>
import FormatMessage from '../../../components/FormatMessage/FormatMessage'
import FileIcon from '../../../components/PngIcons/file_icon.png'

const { isImageFile, isVideoFile } = require('../../../utils/media-file')

export default {
  name: 'MessageReply',
  components: {
    FormatMessage
  },

  props: {
    message: { type: Object, required: true },
    textFormatting: { type: Boolean, required: true },
    linkOptions: { type: Object, required: true },
    roomUsers: { type: Array, required: true },
    isGroup: { type: Boolean, default: false }
  },

  emits: ['reply-msg-handler'],

  data() {
    return {
      fileIcon: FileIcon
    }
  },

  computed: {
    replyUsername() {
      const { username } = this.message.replyMessage
      return username || ''
    },
    firstFile() {
      return this.message.replyMessage.files ? this.message.replyMessage.files[0] : {}
    },
    containsFile() {
      return this.message.replyMessage.files
    },

    isImage() {
      return isImageFile(this.firstFile)
    },
    isVideo() {
      return isVideoFile(this.firstFile)
    }
  }
}
</script>
