.profile {
  width: 400px;
  height: 100%;
  transition: all 0.2s;

  &_header {
    padding-right: 20px;
    padding-left: 25px;
    background: rgb(240, 242, 245);
    display: flex;
    height: 17rem;
    align-items: center;
    border-bottom: 1px solid #ededed;

    svg {
      cursor: pointer;
      width: 40px;
      height: 40px;
    }

    &-info {
      // font-size: 16px;
      margin: 0;
      color: rgb(17, 27, 33);
      // font-weight: 400;
      margin-left: 20px;

      font-weight: 600;
      font-size: 2.5rem;
      line-height: 3.7rem;
    }
  }

  &_info {
    padding: 28px 30px 19px;
    // box-shadow: 0 1px 3px rgba(11, 20, 26, 0.08);

    .img-box {
      width: 200px;
      height: 200px;
      margin: auto;
      margin-bottom: 15px;
      background-size: cover;
      background-position: center center;
      background-repeat: no-repeat;
      background-color: #ddd;
      border-radius: 50%;
      cursor: pointer;
    }

    .profile-text-box {
      display: flex;
      flex-direction: column;
      align-items: center;

      h2 {
        font-size: 24px;
        font-weight: 400;
        text-align: center;
        word-break: break-word;
        margin: 0;
        line-height: 1;
      }

      h3 {
        margin-top: 4px;
        line-height: 1.5;
        color: #667781;
        font-size: 16px;
        font-weight: 400;
        margin-bottom: 0;
      }
    }
  }

  @media only screen and (max-width: 1919px) {
    &_header {
      height: 15rem;

      svg {
        width: 24px;
        height: 24px;
      }

      &-info {
        font-weight: 400;
        font-size: 16px;
      }
    }
  }
}
