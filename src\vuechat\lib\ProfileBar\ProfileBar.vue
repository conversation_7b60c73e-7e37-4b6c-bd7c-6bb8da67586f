<template>
  <div class="profile">
    <!-- header -->
    <div class="profile_header">
      <svg viewBox="0 0 24 24" width="24" height="24" class="" @click="$emit('toggle')">
        <path
          fill="#54656f"
          d="m19.1 17.2-5.3-5.3 5.3-5.3-1.8-1.8-5.3 5.4-5.3-5.3-1.8 1.7 5.3 5.3-5.3 5.3L6.7 19l5.3-5.3 5.3 5.3 1.8-1.8z"
        />
      </svg>
      <div class="profile-heading">
        <p class="profile_header-info">
          {{ heading }}
        </p>
        <!-- <img
					v-if="Object.keys(engagement).length !== 0 && setContactObjects === true"
					:src="hubspotIcon"
					alt="Hubspot Icon"
					height="30"
					title="Open this details"
					@click.stop="openHubspot"
				/> -->
      </div>
    </div>

    <div class="right-side-bar-loader">
      <loader :show="showAddToHubspot" />
    </div>
    <span v-if="showAddToHubspot !== true && setContactObjects === true">
      <div v-if="Object.keys(engagement).length === 0" class="add-contact-hubspot" @click="addToHubspot(phone, room)">
        <a href="#" class="add-contact-to-hubspot"> Add To HubSpot </a>
      </div>
    </span>

    <!-- info -->
    <div v-if="profile_info" class="profile_info">
      <div class="img-box" :style="{ 'background-image': `url('${avatar}')` }" @click="toggleImageViewer" />
      <div class="profile-text-box">
        <h2>{{ room.roomName }}</h2>
        <h3 v-if="Object.keys(participants).length === 0">{{ phone }}</h3>
      </div>
    </div>

    <div class="right-side-bar-loader">
      <loader :show="showParticipants" />
    </div>
    <div v-if="Object.keys(participants).length !== 0" class="user_list">
      <h5>Group Members</h5>
      <ul>
        <li v-for="(value, key) in participants" :key="key">{{ value }}</li>
      </ul>
    </div>

    <transition name="vac-fade-message">
      <profile-image v-if="showImageViewer" :image="avatar" :name="room.roomName" @close="toggleImageViewer" />
    </transition>

    <div v-show="!startChat">
      <HubspotSideBar
        :fieldname="room.roomName"
        :room="room"
        :engagement="engagement"
        :error-message="errorMessage"
        :toggle-success-modal="toggleSuccessModal"
        :success-message="successMessage"
        :save-key="saveKey"
        :show-success-modal="showSuccessModal"
        :loading-tab="loadingTab"
        :show-error-modal="showErrorModal"
        :show-create-modal="showCreateModal"
        :phone="phone"
        @UpdateEngagement="UpdateEngagement"
        @AddNewEngagement="AddNewEngagement"
        @getEngagement="getEngagement"
        @redirect-to-hubspot="$emit('redirect-to-hubspot', room)"
      />
    </div>
  </div>
</template>

<script>
import Loader from '../../components/Loader/InnerLoader'
import DummyAvatar from '../../components/PngIcons/profile-placeholder.png'
import ProfileImage from './ProfileImage.vue'
import HubspotIcon from '../../components/PngIcons/hubspot_icon.png'
import HubspotSideBar from '../../components/HubspotSideBar/HubspotSideBar'

export default {
  name: 'ProfileBar',
  components: {
    Loader,
    ProfileImage,
    HubspotSideBar
  },
  props: {
    room: { type: Object, required: true },
    engagement: { type: Object, default: null },
    participants: { type: Object, default: null },
    loadingTab: { type: Boolean, required: false },
    showErrorModal: { type: Boolean, required: true },
    showCreateModal: { type: Boolean, default: false },
    errorMessage: { type: String, default: '' },
    showAddToHubspot: { type: Boolean, required: true },
    setContactObjects: { type: Boolean, required: false },
    showParticipants: { type: Boolean, required: true },
    toggleSuccessModal: { type: Function, required: true },
    showSuccessModal: { type: Boolean, required: true },
    saveKey: { type: Boolean, required: false },
    successMessage: {
      type: Object,
      default: () => ({ heading: 'Success', content: 'Successful!' })
    }
  },
  emits: ['toggle', 'redirect-to-hubspot'],
  data() {
    return {
      avatar: this.room.avatar || DummyAvatar,
      hubspotIcon: HubspotIcon,
      heading: this.room.isGroup ? 'Group info' : 'Contact info',
      phone: this.room.roomName === this.room.phone ? '' : this.room.phone,
      showImageViewer: false,
      profile_info: true,
      startChat: true
    }
  },
  beforeMount() {
    this.getGroupParticipants()
  },
  methods: {
    getGroupParticipants() {
      this.$emit('getGroupParticipants')
    },
    toggleImageViewer() {
      this.showImageViewer = !this.showImageViewer
    },
    openHubspot() {
      this.profile_info = !this.profile_info
      this.startChat = !this.startChat
    },
    UpdateEngagement(value, field, label, tab) {
      this.$emit('UpdateEngagement', value, field, label, tab)
    },
    getEngagement(value, tab) {
      this.$emit('getEngagement', value, tab)
    },
    AddNewEngagement(submitForm, tab, ownerId, associations) {
      this.$emit('AddNewEngagement', submitForm, tab, ownerId, associations)
    },
    addToHubspot(phone, room) {
      this.$emit('addToHubspot', phone, room)
    }
  }
}
</script>

<style>
.user_list {
  padding: 10px;
  overflow: auto;
}

.user_list h5 {
  text-align: center;
}

.user_list ul li {
  margin: 6px 0;
  background-color: #eef3fb;
  font-size: 18px;
  list-style: none;
  text-align: center;
  padding: 10px 10px;
  border-radius: 4px;
}

.user_list ul {
  padding: 0;
  overflow: auto;
  height: calc(114vh - 625px);
}
</style>
