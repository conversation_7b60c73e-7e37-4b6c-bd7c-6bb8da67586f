.image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background: rgba(255, 255, 255, 0.96);

  &_header {
    background: #fff;
    display: flex;
    flex: 0 0 auto;
    flex-direction: row;
    padding: 0 20px;
    align-items: center;
    height: 60px;
    transition: visibility 0.3s, opacity 0.3s cubic-bezier(0.1, 0.82, 0.25, 1);

    .avatar-thumb {
      margin: 0 15px;
      height: 40px;
      width: 40px;
      border-radius: 50%;
      background-size: cover;
      background-position: center center;
      background-repeat: no-repeat;
      background-color: #ddd;
    }

    .user_name {
      padding-right: 15px;
      display: flex;
      flex-basis: auto;
      flex-direction: column;
      flex-grow: 1;
      justify-content: center;
      min-width: 0;
      font-size: 17px;
      font-weight: 400;
    }

    svg {
      margin: 0 8px;
      cursor: pointer;
    }
  }

  &_body {
    height: calc(100% - 60px);
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      display: block;
      max-height: 90%;
    }
  }
}
