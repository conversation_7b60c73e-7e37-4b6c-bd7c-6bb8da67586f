<template>
  <div class="image-viewer">
    <div class="image-viewer_header">
      <div class="avatar-thumb" :style="{ 'background-image': `url('${image}')` }" />
      <div class="user_name">
        {{ name }}
      </div>

      <svg viewBox="0 0 24 24" width="24" height="24" @click="$emit('close')">
        <path
          fill="rgb(84, 101, 111)"
          d="m19.8 5.8-1.6-1.6-6.2 6.2-6.2-6.2-1.6 1.6 6.2 6.2-6.2 6.2 1.6 1.6 6.2-6.2 6.2 6.2 1.6-1.6-6.2-6.2 6.2-6.2z"
        />
      </svg>
    </div>
    <div class="image-viewer_body">
      <img :src="image" :alt="name" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImageViewer',
  props: {
    image: { type: String, required: true },
    name: { type: String, required: true }
  },
  emits: ['close']
}
</script>

<style lang="scss" scoped>
@import './ProfileImage.scss';
</style>
