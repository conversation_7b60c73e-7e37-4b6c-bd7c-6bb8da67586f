.vac-col-messages {
  position: relative;
  height: 100%;
  width: 100%;
  max-width: 100%;
  // max-width: calc(100% - 53.5rem);
  // min-width: 330px;
  // width: 100%;
  // min-width: calc(100vw - 500px);
  // max-width: calc(100vw - 330px);
  //flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .vac-container-center {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .vac-room-empty {
    font-size: 14px;
    color: #9ca6af;
    font-style: italic;
    line-height: 20px;
    white-space: pre-line;

    div {
      padding: 0 10%;
    }
  }

  .vac-container-scroll {
    background: var(--chat-content-bg-color);
    flex: 1 1 0;
    overflow-y: auto;
    order: 2;
    // margin-right: 1px;
    //margin-top: 10rem;
    -webkit-overflow-scrolling: touch;

    &.vac-scroll-smooth {
      scroll-behavior: smooth;
    }
  }

  .vac-messages-container {
    padding: 0 2rem 2rem;
  }

  .vac-text-started {
    font-size: 14px;
    color: var(--chat-message-color-started);
    font-style: italic;
    text-align: center;
    margin-top: 30px;
    margin-bottom: 20px;
  }

  .vac-infinite-loading {
    height: 68px;
  }

  .vac-icon-scroll {
    position: absolute;
    bottom: 130px;
    right: 20px;
    padding: 8px;
    background: #f0f0f0;
    border-radius: 50%;
    box-shadow: 0 1px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 1px 2px 0 rgba(0, 0, 0, 0.12);
    display: flex;
    cursor: pointer;
    z-index: 9;

    svg {
      height: 25px;
      width: 25px;
    }
  }

  .vac-messages-count {
    position: absolute;
    top: -8px;
    left: 11px;
    background-color: var(--chat-message-bg-color-scroll-counter);
    color: var(--chat-message-color-scroll-counter);
  }

  .vac-room-footer {
    width: 100%;
    max-width: 100%;
    // overflow: hidden;
    border-bottom-right-radius: 4px;
    background: #f8f9fa;
    flex: none;
    order: 3;
    min-height: 62px;
  }

  .vac-box-footer {
    display: flex;
    position: relative;
    flex-direction: column;
    // border: 1px solid #34b7f1;
    background: #f8f9fa;
    // border-radius: 20px;
    // overflow: hidden;
    // margin: 3rem;
    padding: 3rem 4.8rem 2.5rem 4.8rem;
  }

  .media-error-msg {
    color: red;
    font-weight: 500;
    font-size: 2rem;
    margin: 0;
  }

  .textarea-wrapper {
    display: flex;
    align-items: center;
  }

  .textarea-box {
    width: 100%;
    display: flex;
    background: #fff;
    position: relative;
    border: 0.3rem solid #34b7f1;
    border-radius: 60px;

    .spinner-border {
      width: 20px;
      height: 20px;
      border-width: 2px;
    }

    .vac-textarea {
      // height: 8.6rem;
      width: 100%;
      overflow: auto;
      box-sizing: content-box;
      outline: 0;
      resize: none;

      // padding: 2.7rem 170px 2.7rem 5.3rem;
      // box-sizing: border-box;
      background: transparent;
      color: #000;
      caret-color: var(--chat-color-caret);
      font-weight: 500;
      font-size: 20px;
      // padding: 2.6rem 4.8rem;
      // padding-right: 10rem;
      padding: 1.6rem 0rem 1.6rem 4.8rem;
      margin: 1rem 10rem 1rem 1rem;
      line-height: 24px;
      min-height: 24px;
      height: 24px;
      border: 0px transparent;

      &::placeholder {
        color: rgba(52, 183, 241, 0.4);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &.padding-50 {
        padding-bottom: 50px;
      }

      &::-webkit-scrollbar-thumb {
        background: #34b7f1;
        border-radius: 30px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: darken(#34b7f1, 10%);
      }
    }

    .send-icon {
      position: absolute;
      top: 50%;
      right: 5rem;
      transform: translate(0, -50%);

      // &.vac-send-disabled {
      // 	background: #d2d2d2;
      // }

      img {
        height: 3.15rem;
      }
    }
  }

  .vac-textarea-outline {
    border: 1px solid var(--chat-border-color-input-selected);
    box-shadow: inset 0px 0px 0px 1px var(--chat-border-color-input-selected);
  }

  .vac-icon-textarea,
  .vac-icon-textarea-left {
    display: flex;
    align-items: center;

    // svg,
    // .vac-wrapper {
    // 	margin: 0 7px;
    // }
  }

  .vac-icon-textarea {
    .paperclip-icon {
      // width: 32px;
      margin-right: 2.4rem;

      svg {
        // transform: rotate(45deg);
        // position: relative;
        // bottom: 4px;
        width: 4.8rem;
        // height: 37px;
      }
    }

    .emoji-icon {
      margin-right: 2.4rem;

      svg {
        width: 4.8rem;
      }
    }
  }

  .vac-icon-textarea-left {
    display: flex;
    align-items: center;
    margin-right: 5px;

    svg,
    .vac-wrapper {
      margin: 0 7px;
    }

    .vac-icon-microphone {
      fill: var(--chat-icon-color-microphone);
      margin: 0 7px;
    }

    .vac-dot-audio-record {
      height: 15px;
      width: 15px;
      border-radius: 50%;
      background-color: var(--chat-message-bg-color-audio-record);
      animation: vac-scaling 0.8s ease-in-out infinite alternate;

      @keyframes vac-scaling {
        0% {
          transform: scale(1);
          opacity: 0.4;
        }

        100% {
          transform: scale(1.1);
          opacity: 1;
        }
      }
    }

    .vac-dot-audio-record-time {
      font-size: 16px;
      color: var(--chat-color);
      margin-left: 8px;
      width: 45px;
    }

    $audio-icon-size: 28px;

    .vac-icon-audio-stop,
    .vac-icon-audio-confirm {
      min-height: $audio-icon-size;
      min-width: $audio-icon-size;

      svg {
        min-height: $audio-icon-size;
        min-width: $audio-icon-size;
      }
    }

    .vac-icon-audio-stop {
      margin-right: 20px;

      #vac-icon-close-outline {
        fill: var(--chat-icon-color-audio-cancel);
      }
    }

    .vac-icon-audio-confirm {
      margin-right: 3px;
      margin-left: 12px;

      #vac-icon-checkmark {
        fill: var(--chat-icon-color-audio-confirm);
      }
    }
  }

  .vac-send-disabled,
  .vac-send-disabled svg {
    cursor: none !important;
    pointer-events: none !important;
    // transform: none !important;
  }

  .vac-messages-hidden {
    opacity: 0;
  }

  .empty-room-menu {
    cursor: pointer;
    position: absolute;
    right: 4rem;
    top: 7rem;
  }

  @media only screen and (max-width: 1919px) {
    .vac-container-scroll {
      //margin-top: 1;
    }

    .textarea-box {
      .vac-textarea {
        font-size: 14px;
        line-height: 19px;
        max-height: 100px;
        // height: 20px !important;
        min-height: 20px;
        // padding: 14px 4.8rem;
        padding: 10px 4.8rem;
        padding-right: 10rem;
        // min-height: 42px;
        // padding: 10.5px 2rem;
        // line-height: 45px;
        // line-height: 35px;
        // padding: 2.5rem 30rem 2.5rem 3rem;
      }

      .send-icon {
        img {
          height: 22px;
        }
      }
    }

    // .vac-icon-textarea {
    // 	// right: 5rem;
    // 	// bottom: 4rem;

    // 	// .paperclip-icon {
    // 	// 	svg {
    // 	// 		width: 30px;
    // 	// 		height: 30px;
    // 	// 	}
    // 	// }

    // 	// .emoji-icon {
    // 	// 	svg {
    // 	// 		// width: 30px;
    // 	// 	}
    // 	// }
    // }
  }

  @media only screen and (max-width: 768px) {
    .vac-container-scroll {
      margin-top: 50px;
    }

    .vac-infinite-loading {
      height: 58px;
    }

    // .vac-box-footer {
    // 	border-top: none;
    // 	padding: 7px 2px 7px 7px;
    // }

    .vac-text-started {
      margin-top: 20px;
    }

    .vac-textarea {
      padding: 7px;
      // line-height: 18px;

      &::placeholder {
        color: transparent;
      }
    }

    // .vac-icon-textarea,
    // .vac-icon-textarea-left {
    // 	svg,
    // 	.vac-wrapper {
    // 		margin: 0 5px !important;
    // 	}
    // }

    .vac-room-footer {
      width: 100%;
    }

    .vac-icon-scroll {
      bottom: 70px;
    }
  }
}

.footer-disabled {
  cursor: not-allowed;
  pointer-events: none;
  user-select: none;
  opacity: 0.5;
}