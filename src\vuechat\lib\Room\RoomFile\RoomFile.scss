.vac-room-file-container {
  display: flex;
  position: relative;
  margin: 0 4px;

  .vac-message-image {
    position: relative;
    background-color: var(--chat-message-bg-color-image) !important;
    background-size: cover !important;
    background-position: center center !important;
    background-repeat: no-repeat !important;
    height: 100px;
    width: 100px;
    border: var(--chat-border-style-input);
    border-radius: 4px;
  }

  .vac-file-container {
    // height: 80px;
    // width: 80px;
    background: #eef3fb;
    border-radius: 20px;
    padding: 7px 15px 7px 10px;
    flex-wrap: nowrap;
    max-width: 200px;
    display: flex;

    .file-icon {
      margin-right: 10px;
      img {
        height: 20px;
      }
    }

    .vac-text-ellipsis {
      font-weight: 500;
      font-size: 12px;
      line-height: 20px;
      margin-right: 18px;

      color: #000000;
    }
  }

  .vac-icon-remove {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    z-index: 10;

    svg {
      height: 10px;
      width: 10px;
      border-radius: 50%;
    }

    // &:before {
    // 	content: ' ';
    // 	position: absolute;
    // 	width: 100%;
    // 	height: 100%;
    // 	background: rgba(0, 0, 0, 0.5);
    // 	border-radius: 50%;
    // 	z-index: -1;
    // }
  }
}
