<template>
  <div class="vac-room-file-container">
    <div class="vac-svg-button vac-icon-remove" @click="$emit('remove-file', index)">
      <slot name="reply-close-icon">
        <svg-icon name="close-outline" />
      </slot>
    </div>

    <div v-if="isPdf" class="vac-file-container">
      <div class="file-icon">
        <slot name="file-icon">
          <img :src="pdfIcon" alt="PDF Icon" />
        </slot>
      </div>
      <div class="vac-text-ellipsis">
        {{ file.name }}
      </div>
    </div>

    <div v-else class="vac-file-container">
      <div class="file-icon">
        <slot name="file-icon">
          <img :src="docIcon" alt="PDF Icon" />
        </slot>
      </div>
      <div class="vac-text-ellipsis">
        {{ file.name }}
      </div>
    </div>
  </div>
</template>

<script>
import SvgIcon from '../../../components/SvgIcon/SvgIcon'
import PdfIcon from '../../../components/PngIcons/pdf_icon.png'
import DocIcon from '../../../components/PngIcons/doc_icon.png'

const { isPdfFile } = require('../../../utils/media-file')
import { mapMutations } from 'vuex'

export default {
  name: 'RoomFiles',
  components: {
    SvgIcon
  },

  props: {
    file: { type: Object, required: true },
    index: { type: Number, required: true }
  },

  emits: ['remove-file'],

  data() {
    return {
      pdfIcon: PdfIcon,
      docIcon: DocIcon
    }
  },

  computed: {
    isPdf() {
      this.setSelectedFileData(this.verifyUploadedMedia(this.file),'function call');
      return isPdfFile(this.file)
    }
  },

  methods: {
    ...mapMutations(['setSelectedFileData']), 

    // Verify Uploaded media type and size
    verifyUploadedMedia() {
      let bytes, fileType;
      bytes = this.file.size;
      fileType = this.file.rawData.type;
      // Allowed media types
      const allowedTypes = [
        "text/plain", "audio/aac", "audio/x-m4a", "audio/wav", "audio/mp4",
        "image/png", "image/jpeg", "image/gif", "video/mp4", "video/ogg",
        "video/x-msvideo", "video/quicktime", "video/webm"
      ];

      // Convert bytes to MB
      const mb = bytes / (1024 * 1024);

      // Check if file type is allowed
      if (!allowedTypes.includes(fileType)) {
        return false; // Invalid file type
      }

      // Check if size exceeds 8MB
      if (mb > 8) {
        return false; // File too large
      }

      // Format bytes
      if (bytes < 1024) return bytes + " B";
      let kb = bytes / 1024;
      if (kb < 1024) return kb.toFixed(2) + " KB";
      let mbSize = kb / 1024;
      if (mbSize < 1024) return mbSize.toFixed(2) + " MB";
      let gb = mbSize / 1024;
      if (gb < 1024) return gb.toFixed(2) + " GB";
      let tb = gb / 1024;

      return tb.toFixed(2) + " TB";
    }
  }
}
</script>
