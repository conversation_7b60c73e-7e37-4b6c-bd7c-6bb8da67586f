.vac-room-files-container {
  display: flex;
  align-items: center;
  padding: 2rem;
  padding-bottom: 0;
  background: #fff;
  border-bottom: 1px solid #ededed;
  margin-bottom: 5px;
  max-width: 100%;
  width: 100%;

  .vac-files-box {
    display: flex;
    overflow: auto;
    width: calc(100% - 30px);
    max-width: 100%;
    padding-bottom: 2rem;
  }

  video {
    height: 100px;
    border: var(--chat-border-style-input);
    border-radius: 4px;
  }

  .vac-icon-close {
    margin-left: auto;
    position: relative;
    top: -1rem;
    // transform: translateY(-20%);

    svg {
      height: 20px;
      width: 20px;
    }
  }
}

@media only screen and (max-width: 1919px) {
  // .vac-room-files-container {
  // 	padding: 3px 0 0 0;
  // }
}

@media only screen and (max-width: 768px) {
  .vac-files-container {
    padding: 6px 4px 4px 2px;
  }
}
