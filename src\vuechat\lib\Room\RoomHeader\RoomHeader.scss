.vac-room-header {
  position: relative;
  display: flex;
  align-items: center;
  height: 10rem;
  width: 100%;
  z-index: 10;
  margin-right: 1px;
  // border-top-right-radius: var(--chat-container-border-radius);
  border-color: #ededed;

  .vac-room-wrapper {
    display: flex;
    align-items: center;
    min-width: 0;
    height: 100%;
    width: 100%;
    padding-right: 4.8rem;
    padding-left: 2.69rem;
    background: #ffffff;
    border-bottom: var(--chat-border-style);
  }

  .vac-toggle-button {
    // display: none;
    margin-right: 3.89rem;

    &:hover {
      opacity: 1;
    }

    img {
      height: 2rem;
    }
  }

  .vac-rotate-icon {
    transform: rotate(180deg) !important;
  }

  .vac-info-wrapper {
    display: flex;
    align-items: center;
    min-width: 0;
    width: 100%;
    // height: 100%;
  }

  .vac-room-name {
    font-weight: 600;
    font-size: 2.4rem;
    line-height: 2.9rem;
    color: #000000;

    img {
      margin-left: 1rem;
    }
  }

  .vac-room-info {
    font-size: 13px;
    line-height: 18px;
    color: var(--chat-header-color-info);
  }

  .vac-room-options {
    margin-left: auto;
  }

  // .hwa-menu-icon {
  // 	cursor: pointer;
  // 	margin-left: 4.4rem;
  // 	// img {
  // 	// 	width: 37.39px;
  // 	// 	height: 22.44px;
  // 	// }
  // }

  .vac-avatar {
    height: 7rem;
    width: 7rem;
    min-height: 7rem;
    min-width: 7rem;
    border-radius: 50%;
    margin-right: 2.4rem;
  }

  .hwa-template {
    margin-right: 4.8rem;
    display: flex;
    align-items: center;
    position: relative;

    .template-box {
      display: flex;
      align-items: center;

      .hwa-template_text {
        font-size: 2.6rem;
        line-height: 3.2rem;
        letter-spacing: 0.003em;
        color: #000000;
        font-weight: 500;
      }

      svg {
        height: 1.3rem;
        top: 1px;
        position: relative;
      }

      svg.rotateSvg {
        transform: rotate(180deg);
      }
    }

    .vac-menu-options {
      width: 50rem;
      height: 60rem;
      min-width: 180px;
      min-height: 188px;
      right: 0;
      top: 6rem;
      background-color: #fff;
      box-shadow: 2px 2px 30px rgba(123, 123, 123, 0.25);
    }

    .no-template-center {
      text-align: center;
    }

    .vac-menu-item {
      width: 45rem;
      min-width: 200px;
      white-space: normal;
      padding: 10px;
      font-weight: 500;
      font-size: 1.6rem;
      line-height: 2.4rem;
      color: #000000;
    }

    .template-search-box {
      position: relative;
      width: 90%;
      margin: auto;
      margin-bottom: 12px;
      margin-top: 12px;

      input {
        width: 100%;
        height: 6rem;
        background: #fff;
        // backdrop-filter: blur(50px);
        border-radius: 6rem;
        padding: 1.8rem 8.2rem 1.8rem 5.2rem;
        font-size: 2rem;
        line-height: 2.4rem;
        font-weight: 400;
        color: rgba(156, 166, 175, 0.6);
        // margin-right: 4.4rem;
        outline: none;
        border: 0.3rem solid #d2d2d2;
        box-shadow: none;

        &::placeholder {
          color: rgba(156, 166, 175, 0.6);
        }
      }

      img {
        position: absolute;
        width: 2.2rem;
        left: 2rem;
        top: 50%;
        z-index: 2;
        transform: translateY(-50%);
      }

      span.close-icon {
        cursor: pointer;
        color: rgba(134, 150, 160, 0.87);
        font-size: 30px;
        position: absolute;
        top: 50%;
        right: 3rem;
        transform: translateY(-50%);
      }
    }

    .hwa-menu-list {
      padding: 0;
      height: calc(100% - 59px);
      box-sizing: border-box;
      overflow-y: auto;
      border-radius: 4px;
      display: block;
      cursor: pointer;
      background: var(--chat-dropdown-bg-color);
      overflow-x: hidden;
    }

    &:hover {
      cursor: pointer;
    }
  }

  .hwa-convo-search {
    margin-right: 0;
    position: relative;

    img {
      position: absolute;
      width: 2.2rem;
      left: 2rem;
      top: 50%;
      z-index: 2;
      transform: translateY(-50%);
    }

    input {
      width: 28.3rem;
      height: 6rem;
      background: #fff;
      backdrop-filter: blur(50px);
      border-radius: 6rem;
      padding: 1.8rem 8.2rem 1.8rem 5.2rem;
      font-size: 2rem;
      line-height: 2.4rem;
      font-weight: 400;
      color: rgba(156, 166, 175, 0.6);
      // margin-right: 4.4rem;
      outline: none;
      border: 0.3rem solid #d2d2d2;
      box-shadow: none;

      &::placeholder {
        color: rgba(156, 166, 175, 0.6);
      }
    }
    #count {
      position: absolute;
      left: 18rem;
      font-size: 10px;
      top: 50%;
      display: flex;
      // flex-direction: column;
      align-items: center;
      z-index: 2;
      padding: 0;
      transform: translateY(-50%);
    }
    .convo-buttons {
      position: absolute;
      right: 0.9rem;
      top: 50%;
      display: flex;
      // flex-direction: column;
      align-items: center;
      z-index: 2;
      padding: 0;
      transform: translateY(-50%);

      button {
        background: none;
        border: none;

        svg path {
          &:hover {
            fill: #34b7f1;
          }
        }

        &.prev {
          transform: rotate(180deg);
        }
      }

      &_left {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      &_right {
        button {
          color: #919192;
          font-size: 4rem;
          padding-left: 0;
          // line-height: 1.4;

          &:hover {
            color: #34b7f1;
          }
        }
      }
    }

    // &.menu-open {
    // 	margin-right: 0;
    // }
  }

  @media only screen and (max-width: 1919px) {
    height: 10rem;

    // .vac-toggle-button {
    // 	img {
    // 		height: 15px;
    // 	}
    // }

    // .hwa-menu-icon {
    // 	img {
    // 		// width: 32px;
    // 		height: 3.3rem;
    // 	}
    // }
    .hwa-convo-search {
      input {
        font-size: 12px;
      }
    }
    .hwa-template {
      .vac-menu-item {
        font-size: 14px;
        line-height: 18px;
      }

      .hwa-menu-list {
        margin: 0;
        // height: calc(100% - 30px);
        height: calc(100% - 59px);
      }

      .template-search-box {
        input {
          font-size: 12px;
          height: 35px;
        }
      }
    }
  }

  // @media only screen and (max-width: 768px) {
  // 	height: 50px;

  // 	.vac-room-wrapper {
  // 		padding: 0 10px;
  // 	}

  // 	.vac-room-name {
  // 		font-size: 16px;
  // 		line-height: 22px;
  // 	}

  // 	.vac-room-info {
  // 		font-size: 12px;
  // 		line-height: 16px;
  // 	}

  // 	.vac-avatar {
  // 		height: 37px;
  // 		width: 37px;
  // 		min-height: 37px;
  // 		min-width: 37px;
  // 	}
  // }
}
