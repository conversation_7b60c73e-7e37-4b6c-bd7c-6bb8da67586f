<template>
  <div class="vac-room-header">
    <slot name="room-header" v-bind="{ room, typingUsers, userStatus }">
      <div class="vac-room-wrapper">
        <div
          v-if="!singleRoom"
          class="vac-svg-button vac-toggle-button"
          :class="{ 'vac-rotate-icon': !showRoomsList && !isMobile }"
          @click="$emit('toggle-rooms-list')"
        >
          <img :src="toggleIcon" alt="Toggle Icon" />
          <!-- <slot name="toggle-icon">
						<svg-icon name="toggle" />
					</slot> -->
        </div>
        <div
          class="vac-info-wrapper"
          :class="{ 'vac-item-clickable': roomInfoEnabled }"
          @click="$emit('room-info')"
        >
          <slot name="room-header-avatar" v-bind="{ room }">
            <div
              class="vac-avatar"
              :style="{
                'background-image': `url('${room.avatar || dummyAvatar}')`,
              }"
            />
          </slot>
          <slot
            name="room-header-info"
            v-bind="{ room, typingUsers, userStatus }"
          >
            <div class="vac-text-ellipsis">
              <div class="vac-room-name vac-text-ellipsis">
                {{ room.roomName }}
                <img
                  v-if="!isInstaPage"
                  :src="hubspotIcon"
                  alt="Hubspot Icon"
                  height="30"
                  title="Open this contact in HubSpot"
                  @click.stop="openHubspot"
                />
              </div>
              <div v-if="typingUsers" class="vac-room-info vac-text-ellipsis">
                {{ typingUsers }}
              </div>
              <div v-else class="vac-room-info vac-text-ellipsis">
                {{ userStatus }}
              </div>
            </div>
          </slot>
        </div>
        <div class="d-flex align-items-center">
          <!-- Template -->
          <div v-if="!isInstaPage" class="hwa-template">
            <div class="template-box" @click.prevent="templateHandler">
              <div class="hwa-template_text">Templates</div>
              <svg
                width="25"
                height="15"
                viewBox="0 0 25 15"
                fill="none"
                :class="{ rotateSvg: templateOpened }"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M25 0H0L12.5 15L25 0Z" fill="#34B7F1" />
              </svg>
            </div>
            <!-- templates list -->
            <transition name="vac-slide-left">
              <div
                v-if="templateOpened"
                v-click-outside="closeTemplates"
                class="vac-menu-options"
              >
                <div
                  v-if="!localTemplates.length"
                  class="vac-menu-item no-template-center"
                >
                  <span class="no-label"
                    >You don't have any template please create from
                    <a
                      class="nav-link"
                      target="_blank"
                      href="https://business.facebook.com/wa/manage/message-templates"
                    >
                      here
                    </a>
                  </span>
                </div>
                <div v-if="localTemplates.length">
                  <div class="template-search-box">
                    <input
                      v-model="searchTemplate"
                      @input="debouncedHandleKeypressWhatsApp(searchTemplate)"
                      type="text"
                      placeholder="Search"
                    />
                    <span
                      v-if="searchTemplate"
                      class="close-icon"
                      @click.prevent="clearTSearch"
                    >
                      &times;
                    </span>
                    <img :src="searchIcon" alt="search icon" />
                  </div>
                  <div class="hwa-menu-list">
                    <div v-if="loading" class="vac-menu-item loading-item">
                      Searching templates...
                    </div>
                    <div
                      v-for="template in filteredTemplates"
                      :key="template.id"
                      class="vac-menu-item"
                      @click.prevent="openModal(template)"
                    >
                      {{ template.name }}
                    </div>
                  </div>
                </div>
              </div>
            </transition>
          </div>
          <!-- Search -->
          <div class="hwa-convo-search">
            <input
              class="form-control"
              type="input"
              placeholder="Conversation"
              aria-label="Search"
              :value="searchVal"
              @input="inputHandler($event)"
            />
            <img :src="searchIcon" alt="search icon" />
            <span v-if="searchVal.length" id="count" />
            <div v-if="searchVal.length" class="convo-buttons">
              <div class="convo-buttons_left">
                <button class="prev" @click.prevent="handleScroll('prev')">
                  <svg
                    width="12"
                    height="12"
                    viewBox="0 0 25 15"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M25 0H0L12.5 15L25 0Z" fill="#919192" />
                  </svg>
                </button>
                <button class="next" @click.prevent="handleScroll('next')">
                  <svg
                    width="12"
                    height="12"
                    viewBox="0 0 25 15"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M25 0H0L12.5 15L25 0Z" fill="#919192" />
                  </svg>
                </button>
              </div>
              <div class="convo-buttons_right">
                <button @click.prevent="clearSearch">&times;</button>
              </div>
            </div>
          </div>
        </div>
        <slot v-if="room.roomId" name="room-options">
          <div
            v-if="menuActions.length"
            class="vac-svg-button vac-room-options"
            @click="menuOpened = !menuOpened"
          >
            <slot name="menu-icon">
              <svg-icon name="menu" />
            </slot>
          </div>
          <transition v-if="menuActions.length" name="vac-slide-left">
            <div
              v-if="menuOpened"
              v-click-outside="closeMenu"
              class="vac-menu-options"
            >
              <div class="vac-menu-list">
                <div v-for="action in menuActions" :key="action.name">
                  <div class="vac-menu-item" @click="menuActionHandler(action)">
                    {{ action.title }}
                  </div>
                </div>
              </div>
            </div>
          </transition>
        </slot>
      </div>
    </slot>
    <template-modal
      v-if="isModalOpen"
      :selectedTemplate="selectedTemplate"
      :roomPhone="room.roomId"
      :objectId="room.object_id"
      :defaultHeaderTemplateUrl="defaultHeaderTemplateUrl"
      @close-modal="closeModal"
    />
  </div>
</template>

<script>
import vClickOutside from 'v-click-outside';
import ToggleIcon from '../../../components/SvgIcon/toggle_icon.svg';
import DummyAvatar from '../../../components/PngIcons/profile-placeholder.png';
import HubspotIcon from '../../../components/PngIcons/hubspot_icon.png';
import SvgIcon from '../../../components/SvgIcon/SvgIcon';
import SearchIcon from '../../../components/PngIcons/search_icon.png';
import typingText from '../../../utils/typing-text';
import TemplateModal from '../../../components/TemplateModal/TemplateModal.vue';
import axios from '@/utils/api.js';

export default {
  name: 'RoomHeader',
  components: {
    TemplateModal,
    SvgIcon,
  },

  directives: {
    clickOutside: vClickOutside.directive,
  },

  props: {
    currentUserId: { type: [String, Number], required: true },
    textMessages: { type: Object, required: true },
    singleRoom: { type: Boolean, required: true },
    showRoomsList: { type: Boolean, required: true },
    isMobile: { type: Boolean, required: true },
    roomInfoEnabled: { type: Boolean, required: true },
    menuActions: { type: Array, required: true },
    room: { type: Object, required: true },
    templates: { type: Array, required: true },
  },

  emits: [
    'toggle-rooms-list',
    'room-info',
    'menu-action-handler',
    'add-template',
    'handle-message-search',
    'toggle-menu-bar',
    'redirect-to-hubspot',
  ],

  data() {
    return {
      toggleIcon: ToggleIcon,
      dummyAvatar: DummyAvatar,
      hubspotIcon: HubspotIcon,
      searchVal: '',
      menuOpened: false,
      searchIcon: SearchIcon,
      templateOpened: false,
      currentIdx: -1,
      searchTemplate: '',
      isModalOpen: false,
      loading: false,
      selectedTemplate: null,
      roomPhone: String,
      localTemplates: [],
      defaultHeaderTemplateUrl: null,
    };
  },

  computed: {
    isInstaPage() {
      let check = window.location.hash.includes('insta');
      return check;
    },
    typingUsers() {
      return typingText(this.room, this.currentUserId, this.textMessages);
    },
    userStatus() {
      if (!this.room.users || this.room.users.length !== 2) return;

      const user = this.room.users.find((u) => u._id !== this.currentUserId);

      if (!user.status) return;

      let text = '';

      if (user.status.state === 'online') {
        text = this.textMessages.IS_ONLINE;
      } else if (user.status.lastChanged) {
        text = this.textMessages.LAST_SEEN + user.status.lastChanged;
      }

      return text;
    },
    filteredTemplates() {
      return this.localTemplates.filter((t) =>
        t.name.toLowerCase().includes(this.searchTemplate.toLowerCase()),
      );
    },
  },

  created() {
    this.localTemplates = this.templates;
    const userData = this.$store.state.userData;
    this.user_id = userData.user_id;
    this.debouncedHandleKeypressWhatsApp = this.debounce(
      this.handleKeypressWhatsApp,
      300,
    );
  },
  watch: {
    templates: {
      immediate: true,
      handler(newTemplates) {
        if (newTemplates && newTemplates.length) {
          this.localTemplates = [...newTemplates];
        }
      },
    },
  },

  methods: {
    debounce(func, wait) {
      let timeout;
      return function (...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
      };
    },
    async handleKeypressWhatsApp(searchText) {
      let searchValue = searchText?.toLowerCase();

      // Handle empty search
      if (searchValue.length === 0) {
        return;
      }

      // Query API if we have 3+ characters
      if (searchValue.length >= 3) {
        this.loading = true;

        try {
          const response = await axios.post(`api/whatsapp/template`, {
            user_id: this.user_id,
            filters: {
              name: searchValue,
              status: 'APPROVED',
              parse: true,
            },
          });

          let matchTemplates = response?.data?.templates?.data || [];

          if (matchTemplates.length > 0) {
            // Add unique templates to localTemplates
            const uniqueNewTemplates = matchTemplates.filter(
              (newTemplate) =>
                !this.localTemplates.some(
                  (existingTemplate) => existingTemplate.id === newTemplate.id,
                ),
            );

            if (uniqueNewTemplates.length > 0) {
              this.localTemplates =
                this.localTemplates.concat(uniqueNewTemplates);
            }
          }
        } catch (error) {
          console.error('Error fetching WhatsApp templates:', error);
        } finally {
          this.loading = false;
        }
      }
    },
    inputHandler(event) {
      this.searchVal = event.target.value;
      // console.log(event.target.value, 'her')
      // console.log('this.searchVal', this.searchVal)
      this.debounce(this.handleConvoSearch(), 50);
    },
    clearSearch() {
      this.searchVal = '';
      this.handleConvoSearch();
    },
    handleConvoSearch() {
      this.$emit('handle-message-search', this.searchVal);
      this.currentIdx = -1;
      const idx = 0;
      const elems = document.getElementsByClassName('text__highlight');
      // console.log(elems, 'elems')
      const elem = elems[idx];

      setTimeout(() => {
        if (elem) {
          elem.scrollIntoView();
        }
        // console.log(elems.length, 'heeel')
        let countEl = document.getElementById('count');
        countEl && (countEl.innerHTML = elems.length);
      }, 350);
    },

    menuActionHandler(action) {
      this.closeMenu();
      this.$emit('menu-action-handler', action);
    },
    closeMenu() {
      this.menuOpened = false;
    },
    closeTemplates() {
      this.templateOpened = false;
      this.searchTemplate = '';
    },

    templateHandler() {
      this.clearTSearch();
      this.templateOpened = !this.templateOpened;
    },
    handleScroll(where) {
      const elems = document.getElementsByClassName('text__highlight');

      if (where === 'next') {
        this.currentIdx =
          this.currentIdx + 1 > elems.length - 1 ? 0 : this.currentIdx + 1;

        const prevIndex =
          this.currentIdx === 0 ? elems.length - 1 : this.currentIdx - 1;

        if (elems[this.currentIdx]) {
          elems[this.currentIdx].scrollIntoView();
          elems[this.currentIdx].classList.add('active');
          elems[prevIndex].classList.remove('active');
        }
      } else {
        this.currentIdx =
          this.currentIdx - 1 < 0 ? elems.length - 1 : this.currentIdx - 1;
        const nextIndex =
          this.currentIdx === elems.length - 1 ? 0 : this.currentIdx + 1;

        if (elems[this.currentIdx]) {
          elems[this.currentIdx].scrollIntoView();
          elems[this.currentIdx].classList.add('active');
          elems[nextIndex].classList.remove('active');
        }
      }
    },
    clearTSearch() {
      this.searchTemplate = '';
    },
    openHubspot() {
      this.$emit('redirect-to-hubspot', this.room);
    },
    openModal(template) {
      this.checkTemplateHeaderUrl(template?.id);
      this.isModalOpen = true;
      this.selectedTemplate = template;
      this.templateOpened = !this.templateOpened;
    },
    closeModal() {
      this.isModalOpen = false;
      this.selectedTemplate = null;
    },

    async checkTemplateHeaderUrl(templateId) {
      this.defaultHeaderTemplateUrl = null;
      try {
        const { data } = await axios.get(
          `api/template/media/${templateId}?user_id=${this.user_id}`,
        );
        this.defaultHeaderTemplateUrl = data?.file_url;
      } catch (err) {
        this.errorMessage = 'Unable to update';
        setTimeout(() => (this.errorMessage = ''), 1000);
        console.log(err);
      }
    },
  },
};
</script>
