.vac-reply-container {
  display: flex;
  padding: 3rem 3rem 0 3rem;
  // background: var(--chat-footer-bg-color);
  align-items: center;
  width: calc(100% - 20px);

  .vac-reply-box {
    width: 100%;
    overflow: hidden;
    background: #f5f5f5;
    border-radius: 2rem;
    // padding: 8px 10px;

    // border-left: 8px solid #34b7f1;
    position: relative;
    display: flex;

    .left-border {
      border-top-left-radius: 2rem;
      border-bottom-left-radius: 2rem;
      flex: none;
      width: 1rem;
      background-color: #34b7f1;
    }

    .vac-reply-wrapper {
      padding: 3rem 3rem 3rem 2rem;
      width: 100%;
    }
  }

  .vac-reply-info {
    overflow: hidden;
  }

  .vac-reply-username {
    color: #34b7f1;
    // font-size: 12px;
    // line-height: 15px;
    // margin-bottom: 0.7rem;
  }

  .vac-reply-content {
    font-size: 2rem;
    line-height: 3rem;
    color: #919192;
    white-space: pre-line;

    font-weight: 500;

    .vac-format-message-wrapper {
      max-height: 200px;
      overflow-y: auto;
    }

    .vac-format-message-wrapper .vac-format-container span.msg {
      color: #919192;
      font-size: 2rem;
      line-height: 3rem;
    }
  }

  .vac-icon-reply {
    margin-left: 3rem;

    svg {
      height: 21px;
      width: 21px;
    }
  }

  .vac-image-reply {
    max-height: 100px;
    max-width: 200px;
    margin: 4px 10px 0 0;
    border-radius: 4px;

    .reply-icon {
      margin-bottom: 5px;

      img {
        width: 30px;
      }
    }
  }

  .vac-audio-reply {
    margin-right: 10px;
  }

  .vac-file-container {
    max-width: 100px;
    text-align: center;
    background: white;
    padding: 5px;
  }

  @media only screen and (max-width: 1919px) {
    .vac-reply-content {
      font-size: 14px;
      line-height: 21px;
      font-weight: 400;

      .vac-format-message-wrapper .vac-format-container span.msg {
        color: #919192;
        font-size: 14px;
        font-weight: 400;
        line-height: 21px;
      }
    }
  }
}
