.vac-template-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  .vac-template-box {
    display: flex;
    width: 100%;
    height: 54px;
    overflow: hidden;
    cursor: pointer;
    background: var(--chat-footer-bg-color-tag);
    transition: background-color 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);
  }

  .vac-template-active {
    background: var(--chat-footer-bg-color-tag-active);
  }

  .vac-template-info {
    display: flex;
    overflow: hidden;
    padding: 0 20px;
    align-items: center;
  }

  .vac-template-tag {
    font-size: 14px;
    font-weight: bold;
    margin-right: 10px;
  }

  .vac-template-text {
    font-size: 14px;
  }

  @media only screen and (max-width: 768px) {
    .vac-template-box {
      height: 50px;
    }

    .vac-template-info {
      padding: 0 12px;
    }
  }
}
