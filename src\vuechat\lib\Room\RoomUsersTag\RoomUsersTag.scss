.vac-tags-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  .vac-tags-box {
    display: flex;
    width: 100%;
    height: 54px;
    overflow: hidden;
    cursor: pointer;
    background: var(--chat-footer-bg-color-tag);
    transition: background-color 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);
  }

  .vac-tags-box-active {
    background: var(--chat-footer-bg-color-tag-active);
  }

  .vac-tags-info {
    display: flex;
    overflow: hidden;
    padding: 0 20px;
    align-items: center;
  }

  .vac-tags-avatar {
    height: 34px;
    width: 34px;
    min-height: 34px;
    min-width: 34px;
  }

  .vac-tags-username {
    font-size: 14px;
  }

  @media only screen and (max-width: 768px) {
    .vac-tags-box {
      height: 50px;
    }

    .vac-tags-info {
      padding: 0 12px;
    }
  }
}
