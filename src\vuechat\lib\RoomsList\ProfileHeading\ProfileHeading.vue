<template>
  <div class="profile-heading-wrapper">
    <div class="profile-box">
      <div v-if="avatar">
        <img :src="avatar" :alt="userData.username" />
      </div>
      <div class="profile-info">
        <div class="login-as">Logged in as</div>
        <div class="profile-name">
          {{ userData.username || this.instaUserName }}
        </div>
      </div>
    </div>
    <!--<div class="start-icon popover__wrapper">
			<img :src="startChatIcon" alt="" @click="startChat" />
			<div class="popover__content">
				<p class="popover__message">
Start New Chat
</p>
			</div>
		</div>-->
  </div>
</template>

<script>
import DummyAvatar from '../../../components/PngIcons/profile-placeholder.png'
import StartChatIcon from '../../../components/SvgIcon/start_chat.svg'
import { mapState } from 'vuex'

export default {
  name: 'ProfileHeading',
  props: {
    userData: { type: Object, required: true }
  },

  emits: ['clicked'],
  data() {
    return {
      startChatIcon: StartChatIcon,
      // avatar: this.userData.avatar || DummyAvatar
      avatar: this.userData.avatar
    }
  },
  computed: {
    ...mapState(['instaUserName'])
  },
  methods: {
    startChat() {
      this.$emit('clicked')
    }
  }
}
</script>
<style scoped>
.popover__wrapper {
  position: relative;
}
.popover__content {
  opacity: 0;
  visibility: hidden;
  position: absolute;
  left: -6.12rem;
  transform: translate(-0.8rem, 0px);
  /* background-color: #f1f1f1; */
  /* padding: 0.8rem; */
  /* box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26); */
  width: 15rem;
  border-radius: 3px;
  padding-top: 0px;
  border: 1px solid #dedede;
  padding: 0.5rem 0.8rem;
}
.popover__wrapper:hover .popover__content {
  z-index: 10;
  opacity: 1;
  visibility: visible;
  transform: translate(-0.8rem, 0.7rem);
  transition: opacity 0.5s cubic-bezier(0.75, -0.02, 0.2, 0.97);
}
.popover__message {
  font-size: 1.6rem;
  color: #414141;
  font-weight: 540;
  margin-bottom: 0px;
  line-height: 1.9rem;
}
</style>
