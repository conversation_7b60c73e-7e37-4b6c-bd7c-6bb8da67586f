<template>
  <div class="vac-room-container">
    <slot name="room-list-item" v-bind="{ room }">
      <div class="hwa-room-meta">
        <!-- <span
          v-for="label in room.labels.slice(0, 2)"
          v-show="showRoomsLabel"
          :key="label.id"
          class="label-icon"
          :style="{ background: label.color }"
        /> -->

        <span
          v-for="label in room.labels.slice(0, 2)"
          :key="label.id"
          class="label-icon"
          :style="{ background: label.color }"
        />
        <img class="pin-icon" :class="{ 'd-block': room.pinned }" :src="pinIcon" alt="pin" height="15" />
      </div>

      <slot name="room-list-avatar" v-bind="{ room }">
        <div
          class="vac-avatar"
          :style="{
            'background-image': `url('${room.avatar || dummyAvatar}')`
          }"
        />
      </slot>
      <div class="vac-name-container vac-text-ellipsis">
        <div class="vac-title-container">
          <div v-if="userStatus" class="vac-state-circle" :class="{ 'vac-state-online': userStatus === 'online' }" />
          <div class="vac-room-name vac-text-ellipsis">
            {{ room.roomName }}
          </div>
          <div v-if="room.lastMessage" class="vac-text-date">
            {{ room.lastMessage.timestamp }}
          </div>
        </div>
        <div
          v-if="room.lastMessage"
          class="vac-text-last"
          :class="{
            'vac-message-new': room.lastMessage && room.lastMessage.new && !typingUsers
          }"
        >
          <span v-if="isMessageCheckmarkVisible">
            <slot name="checkmark-icon" v-bind="{ message: room.lastMessage }">
              <svg-icon
                :name="room.lastMessage.distributed ? 'double-checkmark' : 'checkmark'"
                :param="room.lastMessage.seen ? 'seen' : ''"
                class="vac-icon-check"
              />
            </slot>
          </span>
          <div v-if="room.lastMessage && !room.lastMessage.deleted && isAudio" class="vac-text-ellipsis">
            <slot name="microphone-icon">
              <svg-icon name="microphone" class="vac-icon-microphone" />
            </slot>
            {{ formattedDuration }}
          </div>
          <div v-else-if="room.isGroup">
            <span
              style="
                background-color: var(--chat-room-bg-color-badge);
                color: var(--chat-room-color-badge);
                padding: 1px 3px;
                border-radius: 5px;
                font-weight: 600;
              "
              >Group</span
            >
          </div>
          <format-message
            v-else-if="!room.isGroup && room.lastMessage"
            :content="getLastMessage"
            :deleted="!!room.lastMessage.deleted && !typingUsers"
            :users="room.users"
            :linkify="false"
            :text-formatting="textFormatting"
            :link-options="linkOptions"
            :single-line="true"
          >
            <template #deleted-icon="data">
              <slot name="deleted-icon" v-bind="data" />
            </template>
          </format-message>
          <div v-if="!room.lastMessage && typingUsers" class="vac-text-ellipsis">
            {{ typingUsers }}
          </div>
          <div class="vac-room-options-container">
            <div v-if="unreadCount" class="vac-badge-counter vac-room-badge">
              {{ unreadCount }}
            </div>
            <slot v-if="!isInstaPage" name="room-list-options" v-bind="{ room }">
              <div
                v-if="roomActions.length"
                class="vac-svg-button vac-list-room-options"
                @click.stop="$emit('open-room-menu', room.roomId)"
              >
                <slot name="room-list-options-icon">
                  <!-- <img height="40" :src="dropDownIcon" alt="Drop Down Icon" /> -->
                  <svg-icon name="dropdown" param="room" />
                </slot>
              </div>
              <!-- Room Actions -->
              <transition v-if="roomActions.length" name="vac-slide-left">
                <div v-if="roomMenuOpened === room.roomId" v-click-outside="closeRoomMenu" class="vac-menu-options">
                  <div class="vac-menu-list">
                    <div v-for="action in roomActions" :key="action.name">
                      <div class="vac-menu-item" @click.stop="roomActionHandler(action, room)">
                        {{ action.title }}
                      </div>
                    </div>
                  </div>
                </div>
              </transition>
            </slot>
          </div>
        </div>
      </div>
    </slot>
  </div>
</template>

<script>
import vClickOutside from 'v-click-outside'
import { mapMutations } from 'vuex'
import SvgIcon from '../../../components/SvgIcon/SvgIcon'
import PinIcon from '../../../components/PngIcons/pin_icon.png'
import DummyAvatar from '../../../components/PngIcons/profile-placeholder.png'
import FormatMessage from '../../../components/FormatMessage/FormatMessage'

import typingText from '../../../utils/typing-text'
const { isAudioFile } = require('../../../utils/media-file')

export default {
  name: 'RoomsContent',
  components: {
    SvgIcon,
    FormatMessage
  },

  directives: {
    clickOutside: vClickOutside.directive
  },

  props: {
    currentUserId: { type: [String, Number], required: true },
    room: { type: Object, required: true },
    textFormatting: { type: Boolean, required: true },
    linkOptions: { type: Object, required: true },
    textMessages: { type: Object, required: true },
    // showRoomsLabel: { type: Boolean, required: true },
    roomMenuOpened: { type: [String, Number], default: null },
    toggleLabelsModal: { type: Function, default: () => ({}) },
    unreadCounts: { type: Object, required: true }
  },

  emits: ['room-action-handler', 'handle-show-labels', 'open-room-menu', 'close-room-menu'],

  data() {
    return {
      pinIcon: PinIcon,
      dummyAvatar: DummyAvatar
    }
  },

  computed: {
    isInstaPage(){      
      let check = window.location.hash.includes('insta');
      return check;
    },
    getLastMessage() {
      const isTyping = this.typingUsers
      if (isTyping) return isTyping
      const content = this.room.lastMessage.deleted ? this.textMessages.MESSAGE_DELETED : this.room.lastMessage.content

      // if (this.room.isGroup) {
      //   return "Group";
      // }
      return content
    },
    userStatus() {
      if (!this.room.users || this.room.users.length !== 2) return

      const user = this.room.users.find(u => u._id !== this.currentUserId)
      if (user && user.status) return user.status.state

      return null
    },
    typingUsers() {
      return typingText(this.room, this.currentUserId, this.textMessages)
    },
    isMessageCheckmarkVisible() {
      return (
        !this.typingUsers &&
        this.room.lastMessage &&
        !this.room.lastMessage.deleted &&
        this.room.lastMessage.fromMe === 1 &&
        (this.room.lastMessage.saved || this.room.lastMessage.distributed || this.room.lastMessage.seen)
      )
    },
    formattedDuration() {
      const file = this.room.lastMessage.files[0]

      if (!file.duration) {
        return `${file.name}.${file.extension}`
      }

      let s = Math.floor(file.duration)
      return (s - (s %= 60)) / 60 + (s > 9 ? ':' : ':0') + s
    },
    isAudio() {
      return this.room.lastMessage.files ? isAudioFile(this.room.lastMessage.files[0]) : false
    },
    roomActions() {
      return [
        {
          name: 'pinChat',
          title: this.room.pinned ? 'Unpin Chat' : 'Pin Chat'
        },
        {
          name: 'labelChat',
          title: 'Assign Label'
        }
      ]
    },
    unreadCount() {
      return this.unreadCounts[this.room.roomId]?.val || 0
    }
  },

  methods: {
    ...mapMutations(['setRoomId']),

    roomActionHandler(action, room) {
      this.closeRoomMenu()
      if (action.name === 'labelChat') {
        this.setRoomId(room)
        this.toggleLabelsModal(this.room)
      } else if (action.name === 'pinChat') {
        this.$emit('room-action-handler', { action, did: this.room.did })
        // this.$emit("handle-show-labels", true);
      }
    },

    closeRoomMenu() {
      this.$emit('close-room-menu')
    }
  }
}
</script>
