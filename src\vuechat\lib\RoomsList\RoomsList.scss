.vac-rooms-container {
  display: flex;
  flex-flow: column;
  flex: 0 0 25%;
  min-width: 336px;
  width: 53.5rem;
  position: relative;
  background: var(--chat-sidemenu-bg-color);
  height: 100%;
  border-top-left-radius: var(--chat-container-border-radius);
  border-bottom-left-radius: var(--chat-container-border-radius);
  border-color: var(--chat-sidemenu-bg-color-hover);
  border-right: 1px solid #d1d7db;

  &.vac-rooms-container-full {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .vac-rooms-empty {
    font-size: 14px;
    color: #9ca6af;
    font-style: italic;
    text-align: center;
    margin: 40px 0;
    line-height: 20px;
    white-space: pre-line;
  }

  .vac-room-list {
    flex: 1;
    position: relative;
    max-width: 100%;
    cursor: pointer;
    // margin-top: 15px;
    overflow-y: auto;
    padding: 0 1.6rem;
  }

  .vac-no-chat {
    margin-left: 20%;
    margin-top: 50%;
    font-size: 16px;
    font-family: 'Montserrat', sans-serif !important;
    color: #9ca6af;
  }

  .vac-room-item {
    // border-radius: 8px;
    align-items: center;
    display: flex;
    flex: 1 1 100%;
    margin-bottom: 0.8rem;
    padding: 2.8rem 5.3rem 2.2rem 4.7rem;
    position: relative;
    min-height: 71px;
    height: 12.7rem;
    transition: background-color 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);

    &:hover {
      background: var(--chat-sidemenu-bg-color-hover);
      border-radius: 1.2rem;
    }

    &:hover .vac-list-room-options {
      opacity: 1;
    }

    .vac-avatar {
      margin-right: 3.2rem;
    }

    .msg_box {
      color: rgba(0, 0, 0, 0.7);
    }
  }

  .vac-room-selected {
    // color: var(--chat-sidemenu-color-active) !important;
    // background: var(--chat-sidemenu-bg-color-active) !important;
    // background: #f5f5f5;
    background: var(--chat-sidemenu-bg-color-active) !important;
    border-radius: 1.2rem;

    &:hover {
      background: var(--chat-sidemenu-bg-color-active) !important;
    }
  }

  @media only screen and (min-width: 1920px) {
    min-width: 54.5rem;
  }

  // @media only screen and (max-width: 768px) {
  // 	.vac-room-list {
  // 		padding: 0 7px 5px;
  // 	}

  // 	.vac-room-item {
  // 		min-height: 60px;
  // 		padding: 0 8px;
  // 	}
  // }
}
