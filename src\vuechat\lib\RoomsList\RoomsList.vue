<template>
  <transition name="vac-fade-message">
    <div v-show="showRoomsList" class="vac-rooms-container" :class="{ 'vac-rooms-container-full': isMobile }">
      <slot name="rooms-header" />
      <profile-heading :user-data="userData" @clicked="clicked" />
      <slot name="rooms-list-search">
        <rooms-search
          :rooms="rooms"
          :loading-rooms="loadingRooms"
          :text-messages="textMessages"
          :show-search="showSearch"
          :show-add-room="showAddRoom"
          :show-rooms-label="showRoomsLabel"
          :labels="labelsData"
          :search-text="searchText"
          @reset-room-search="resetRoomSearch"
          @handle-search="handleSearch"
          @clear-search="clearSearch"
          @handle-show-labels="handleShowLabels"
          @filter-room-by-labels="filterRoomByLabels"
          @search-room="searchRoom"
          @add-room="$emit('add-room')"
          @update-search-text="searchText = $event"
          ref="roomSearch"
        >
          <template v-for="(i, name) in $scopedSlots">
            <slot :name="name" v-bind="data" />
          </template>
        </rooms-search>
      </slot>
      <div v-if="this.isSearchLoading" class="loader-inner ball-pulse" style="text-align: center;">
        <div />
        <div />
        <div />
      </div>
      <loader :show="loadingRooms" />
      

      <div v-if="!loadingRooms && !rooms.length" class="vac-rooms-empty">
        <slot name="rooms-empty">
          {{ textMessages.ROOMS_EMPTY }}
        </slot>
      </div>

      <!-- filteredRoom -->

      <div v-if="!loadingRooms && (search || showRoomsLabel)" class="vac-room-list filter">
        <div v-if="filteredRooms.length === 0" class="vac-no-chat"><span>No chats or contact found</span></div>
        <div v-else>
          <div
            v-for="(fRoom,index) in filteredRooms"
            :id="fRoom.roomId"
            :key="`${fRoom.roomId}-${fRoom.created_at}-${index}`"
            class="vac-room-item"
            :class="{ 'vac-room-selected': selectedRoomId === fRoom.roomId }"
            @click="openRoom(fRoom)"
          >
            <room-content
              :current-user-id="currentUserId"
              :room="fRoom"
              :text-formatting="textFormatting"
              :link-options="linkOptions"
              :text-messages="textMessages"
              :room-actions="roomActions"
              :show-rooms-label="showRoomsLabel"
              :toggle-labels-modal="toggleLabelsModal"
              :room-menu-opened="roomMenuOpened"
              :unread-counts="unreadCounts"
              @open-room-menu="openRoomMenu"
              @close-room-menu="closeRoomMenu"
              @handle-show-labels="handleShowLabels"
              @room-action-handler="$emit('room-action-handler', $event)"
            >
              <template v-for="(i, name) in $scopedSlots">
                <slot :name="name" v-bind="data" />
              </template>
            </room-content>
          </div>
        </div>
      </div>

      <!-- all rooms -->
      <div v-show="!loadingRooms && !search && !showRoomsLabel" id="rooms-list" class="vac-room-list">
        <div
          v-for="(sRoom,index) in sRooms"
          :id="sRoom.roomId"
          :key="`${sRoom.roomId}-${sRoom.created_at}-${index}`"
          class="vac-room-item"
          :class="{ 'vac-room-selected': selectedRoomId === sRoom.roomId }"
          @click="openRoom(sRoom)"
        >
          <room-content
            :current-user-id="currentUserId"
            :room="sRoom"
            :text-formatting="textFormatting"
            :link-options="linkOptions"
            :text-messages="textMessages"
            :room-actions="roomActions"
            :show-rooms-label="showRoomsLabel"
            :toggle-labels-modal="toggleLabelsModal"
            :room-menu-opened="roomMenuOpened"
            :unread-counts="unreadCounts"
            @open-room-menu="openRoomMenu"
            @close-room-menu="closeRoomMenu"
            @handle-show-labels="handleShowLabels"
            @room-action-handler="$emit('room-action-handler', $event)"
          >
            <template v-for="(i, name) in $scopedSlots">
              <slot :name="name" v-bind="data" />
            </template>
          </room-content>
        </div>
        
        <transition name="vac-fade-message">
          <div v-if="filteredRooms.length || !loadingRooms && !isInstaPage" id="infinite-loader-rooms">
            <loader :show="showLoader" :infinite="true" />
          </div>
        </transition>
      </div>
    </div>
  </transition>
</template>

<script>
import Loader from '../../components/Loader/Loader'

import RoomsSearch from './RoomsSearch/RoomsSearch'
import RoomContent from './RoomContent/RoomContent'
import ProfileHeading from './ProfileHeading/ProfileHeading'
import filteredItems from '../../utils/filter-items'

export default {
  name: 'RoomsList',
  components: {
    Loader,
    RoomsSearch,
    RoomContent,
    ProfileHeading
  },

  props: {
    userData: { type: Object, required: true },
    currentUserId: { type: [String, Number], required: true },
    textMessages: { type: Object, required: true },
    showRoomsList: { type: Boolean, required: true },
    showSearch: { type: Boolean, required: true },
    showAddRoom: { type: Boolean, required: true },
    textFormatting: { type: Boolean, required: true },
    linkOptions: { type: Object, required: true },
    isMobile: { type: Boolean, required: true },
    rooms: { type: Array, required: true },
    loadingRooms: { type: Boolean, required: true },
    roomsLoaded: { type: Boolean, required: true },
    room: { type: Object, required: true },
    roomActions: { type: Array, required: true },
    toggleLabelsModal: { type: Function, default: () => ({}) },
    labels: { type: Array, required: true },
    unreadCounts: { type: Object, required: true },
  },

  emits: ['add-room', 'room-action-handler', 'loading-more-rooms', 'fetch-room', 'fetch-more-rooms', 'startChat'],

  data() {
    return {
      search: false,
      searchText: '',
      filteredRooms: this.rooms || [],
      sRooms: this.rooms || [],
      filteredRoomsByLabel: this.rooms || [],
      observer: null,
      showLoader: true,
      loadingMoreRooms: false,
      selectedRoomId: '',
      showRoomsLabel: false,
      roomMenuOpened: null,
      isSearchLoading:false,
    }
  },

  computed: {
    isInstaPage(){      
      let check = window.location.hash.includes('insta');
      return check;
    },
    labelsData() {
      return this.labels.map(el => ({ ...el, selected: false }))
    }
  },

  watch: {
    rooms: {
      deep: true,
      handler(newVal, oldVal) {
        this.sRooms = newVal
        this.filteredRooms = newVal;
        this.search = false;
        this.isSearchLoading = false;
        if (newVal.length !== oldVal.length || this.roomsLoaded) {
          this.loadingMoreRooms = false
        }
      }
    },
    loadingRooms(val) {
      if (!val) {
        setTimeout(() => this.initIntersectionObserver())
      }
    },
    loadingMoreRooms(val) {
      this.$emit('loading-more-rooms', val)
    },
    roomsLoaded: {
      immediate: true,
      handler(val) {
        if (val) {
          this.loadingMoreRooms = false
          if (!this.loadingRooms) {
            this.showLoader = false
          }
        }
      }
    },
    room: {
      immediate: true,
      handler(val) {
        if (val && !this.isMobile) this.selectedRoomId = val.roomId
      }
    }
  },

  methods: {
    initIntersectionObserver() {
      if (this.observer) {
        this.showLoader = true
        this.observer.disconnect()
      }

      const loader = document.getElementById('infinite-loader-rooms')
      if (loader) {
        const options = {
          root: document.getElementById('rooms-list'),
          rootMargin: '60px',
          threshold: 0
        }
        
        this.observer = new IntersectionObserver(entries => { 
          if (entries[0].isIntersecting) {
            this.loadMoreRooms()
          }
        }, options)

        this.observer.observe(loader)
      }
    },
    searchRoom(ev) {
      if (ev.target.value) {
        this.search = true
        this.showLoader = false
      } else {
        this.search = false
        if (!this.roomsLoaded && !this.showRoomsLabel) {
          this.showLoader = true
        }
        this.$emit('handle-search', '');
      }
      
      const searchArray = this.showRoomsLabel ? this.filteredRoomsByLabel : this.sRooms
      if(ev.target.value.length > 3){
        this.isSearchLoading = true;
        this.$emit('handle-search', ev.target.value);
      } 

      this.filteredRooms = searchArray;

      // this.filteredRooms = filteredItems(searchArray, 'roomName', 'phone', ev.target.value)
    },
    openRoom(room) {
      if (room.roomId === this.room.roomId && !this.isMobile) return
      if (!this.isMobile) this.selectedRoomId = room.roomId
      this.$emit('fetch-room', { room , search:false})
    },
    loadMoreRooms() {
      console.log('loadMoreRooms', this.loadingMoreRooms);
      console.log('search', this.search);
      console.log('showRoomsLabel', this.showRoomsLabel);
      if (this.loadingMoreRooms || this.search || this.showRoomsLabel) return
      console.log('roomsLoaded', this.roomsLoaded);
      if (this.roomsLoaded) {
        this.loadingMoreRooms = false
        return (this.showLoader = false)
      }
      console.log('sRoom', this.sRooms.length);
      if (this.sRooms.length > 299) {
        this.$emit('fetch-more-rooms')
        this.loadingMoreRooms = true
      } else {
        this.showLoader = false
      }
    },
    handleShowLabels(show) {
      if (show) {
        // this.searchText = ''
        this.showRoomsLabel = false
        if (!this.roomsLoaded) {
          this.showLoader = true
        }
        this.search = false
      } else {
        // this.searchText = ''
        this.showRoomsLabel = true
        this.showLoader = false
        this.filteredRooms = this.sRooms
      }
    },
    filterRoomByLabels(labelIds) {
      // this.searchText = ''
      if (labelIds.length) {
        const filteredRooms = []
        labelIds.forEach(id => {
          const _rooms = this.rooms.filter(room => {
            const labels = room.labels.map(lbl => lbl.id === id)
            return labels.includes(true)
          })
          filteredRooms.push(..._rooms)
        })
        this.filteredRooms = [...new Set(filteredRooms)]
        this.filteredRoomsByLabel = [...new Set(filteredRooms)]
      } else {
        this.filteredRooms = this.rooms
        this.filteredRoomsByLabel = this.rooms
      }
    },
    openRoomMenu(roomId) {
      this.$nextTick(() => {
        if (this.$refs.roomSearch) {
          this.$refs.roomSearch.closeList();
        }
      });
      this.roomMenuOpened = null
      this.roomMenuOpened = roomId
    },
    closeRoomMenu() {
      this.roomMenuOpened = null
    },
    resetRoomSearch() {
      // this.searchText = ''
      this.handleShowLabels(true)
      this.filterRoomByLabels([])
    },
    handleSearch(newSearchText) {
      this.searchText = newSearchText;
      this.searchRoom({ target: { value: newSearchText } }); 
    },
    clearSearch() {
      this.searchText = '';
      this.searchRoom({ target: { value: '' } });
    },
    clicked() {
      this.$emit('startChat')
    }
  }
}
</script>
