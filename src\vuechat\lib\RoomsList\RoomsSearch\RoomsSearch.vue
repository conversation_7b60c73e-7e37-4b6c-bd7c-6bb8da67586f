<template>
  <div
    :class="{
      'vac-box-search ++': showSearchBar,
      'vac-box-empty': !showSearchBar
    }"
  >
    <div v-if="!loadingRooms" class="d-flex align-items-center w-100 gap-5 justify-content-between">
      <!-- Search -->
      <template v-if="showSearch">
        <div class="hwa-search-box">
          <div v-if="!loadingRooms" class="">
            <img class="vac-icon-search" :src="searchIcon" alt="search icon" />
            <span v-if="searchText" class="clear-search" @click.prevent="$emit('clear-search')"> &times; </span>
          </div>
          <input
            v-if="!loadingRooms"
            type="input"
            placeholder="Contact"
            autocomplete="off"
            :value="searchText"
            class="form-control hwa-chat-search-input"
            @input="handleSearch"
          />
        </div>
      </template>

      <!-- Label -->
      <div v-if="!isInstaPage" class="hwa-label popover__wrapper">
        <img v-if="!showRoomsLabel" :src="filterIcon" alt="Filter Icon" @click.prevent="openList" />
        <button v-if="showRoomsLabel" class="close-list" @click.prevent="resetList">&times;</button>
        <div v-if="!showRoomsLabel" class="popover__content">
          <p class="popover__message">Label Filter</p>
        </div>
      </div>
      <!-- Label dropdown -->
      <transition name="vac-slide-left">
        <div v-if="labelsListOpened" v-click-outside="closeList" class="vac-menu-options labels-list-wrapper">
          <div class="d-flex items-center justify-content-between px-5 py-4">
            <button @click.prevent="openSidebar" class="btn btn-primary" :disabled="isLoading">Create Label</button>
            <button @click.prevent="handleDeleteLabel" class="delete-btn"
              :class="selectedLabelCounter !== 1 ? 'opacity-50' : ''" :disabled="selectedLabelCounter !== 1"><img
                :src="deleteIcon" alt=""></button>
          </div>
          <ul class="list-group labels-list">
            <span class="no-label" v-if="labelsData?.length === 0 && !isLoading">You don't have any labels.</span>
            <Spinner v-if="isLoading" />
            <li v-for="(label, idx) in labelsData" :key="label.id" class="list-group-item">
              <div class="d-flex justify-content-between align-items-center">
                <span class="label-icon" :style="{ background: `${label.color}` }" />
                <span class="label-text">{{ label.name }}</span>
              </div>
              <label class="custom-checkbox standard">
                <input type="checkbox" class="label-check" :checked="label.selected"
                  @change="filterRooms($event, idx)" />
                <span class="checkmark" />
              </label>
            </li>
          </ul>
        </div>
      </transition>
      <!-- sidebar -->
      <sidebar heading="Create new label" :show="showSidebar" @close="openSidebar">
        <create-label v-if="showSidebar" :error="errorInAdding" :adding-label="addingLabel"
          @create-handler="addLabel" />
      </sidebar>

      <!-- Dynamic Confirmation modal -->
      <DynamicConfirmationModal :show="showConfirmModal"
        :description="`You're about to delete '${labelName}' label. Delete label can't be restored`" @delete-handler="deleteLabel"
        @cancel-handler="handleDeleteLabel" />
    </div>
  </div>
</template>

<script>
import axios from '@/utils/api.js'
import { convertDate } from '@/utils/utils.js'
import SearchIcon from '../../../components/SvgIcon/search_icon.svg'
import FilterIcon from '../../../components/SvgIcon/filter_icon.svg'
import DeleteIcon from '@/assets/icons/delete.svg'
import vClickOutside from 'v-click-outside'
import Sidebar from '@/components/Sidebar'
import CreateLabel from '@/components/CreateLabel'
import DynamicConfirmationModal from '@/components/DynamicConfirmationModal.vue'
import Spinner from '@/components/Spinner'
import { mapActions } from 'vuex';
import debounce from 'lodash/debounce';

export default {
  name: 'RoomsSearch',

  components: {
    Sidebar,
    CreateLabel,
    DynamicConfirmationModal,
    Spinner
  },

  directives: {
    clickOutside: vClickOutside.directive
  },

  props: {
    searchText: { type: String, required: true },
    showSearch: { type: Boolean, required: true },
    showAddRoom: { type: Boolean, required: true },
    rooms: { type: Array, required: true },
    loadingRooms: { type: Boolean, required: true },
    showRoomsLabel: { type: Boolean, required: true },
    labels: { type: Array, required: true }
  },

  emits: ['filter-room-by-labels', 'handle-show-labels', 'reset-room-search', 'handle-search', 'clear-search'],

  data() {
    return {
      filterIcon: FilterIcon,
      searchIcon: SearchIcon,
      deleteIcon: DeleteIcon,
      labelsListOpened: false,
      labelsData: this.labels,
      timeout: null,
      showSidebar: false,
      selectedLabelCounter: 0,
      showConfirmModal: false,
      labelId: null,
      labelName: null,
      isLoading: false,
    }
  },

  computed: {
    isInstaPage(){
      let check = window.location.hash.includes('insta');
      return check;
    },
    showSearchBar() {
      return this.showSearch || this.showAddRoom
    }
  },

  watch: {
    labels: function () {
      this.labelsData = this.labels
    }
  },

  beforeDestroy() {
    clearTimeout(this.timeout);
    if (this.debouncedHandleSearch.cancel) {
      this.debouncedHandleSearch.cancel();
    }
  },
  created() {
    const userData = this.$store.state.userData

    this.user_id = userData.user_id
    this.dialog_id = userData.portal_id + '.' + userData.instance_id
    this.getInitialData();

    this.debouncedHandleSearch = debounce((event) => {
      this.$emit('handle-search', event);
    },300);
  },

  methods: {
    ...mapActions(['saveLabelsData']),

    async getInitialData() {
      this.isLoading = true;
      this.labelsData = [];
      try {
        const { data } = await axios.get(`api/label?user_id=${this.user_id}`)

        if (data.ok) {

          this.saveLabelsData([...data.data]);
          this.labelsData = [...data.data]

          this.isLoading = false
        } else {
          throw new Error()
        }
      } catch (err) {
        this.isLoading = false

        this.errorMessage = 'Something went wrong!'
      }
    },

    openSidebar() {
      this.showSidebar = !this.showSidebar
      if (!this.showSidebar) {
        this.resetList();
      }
    },

    handleDeleteLabel() {
      this.showConfirmModal = !this.showConfirmModal;
      if (!this.showConfirmModal) {
        this.resetList();
      }
    },

    resetList() {
      // this.labelsData = this.labels
      this.$emit('reset-room-search')
      this.selectedLabelCounter = 0;

    },
    closeList() {
      this.labelsListOpened = false
      if(!this.selectedLabelCounter){
        this.resetList()
      }
    },
    openList() {
      this.labelsListOpened = true
      this.getInitialData()
      const labelsArr = this.labelsData.map(el => ({
        ...el,
        selected: false
      }))
      this.labelsData = labelsArr
      this.$emit('handle-show-labels', false)
    },
    filterRooms(event, idx) {
      const isChecked = event.target.checked;
      if (isChecked) {
        this.selectedLabelCounter += 1;
      } else {
        this.selectedLabelCounter -= 1;
      }
      this.labelsData[idx].selected = event.target.checked
      const labelIds = this.labelsData.filter(el => el.selected).map(el => el.id)
      this.labelId = labelIds[0];
      let labelName = this.labelsData.filter(el => el.selected);
      this.labelName = labelName[0]?.name
        
      this.$emit('filter-room-by-labels', labelIds)
    },
    handleSearch(event) {
      const newSearchText = event.target.value;
      this.debouncedHandleSearch(newSearchText);
      this.$emit('update-search-text', newSearchText);
    },

    async addLabel(data) {
      this.addingLabel = true

      const reqData = {
        user_id: this.user_id,
        dialogId: this.dialog_id,
        ...data
      }

      try {
        const { data } = await axios.post(`api/label`, reqData)
        if (data.ok) {
          const label = {
            ...data.data,
            created_at: convertDate(new Date())
          }
          this.labelsData.unshift(label)
          this.$store.dispatch("updateLabelData", data?.data);
          this.addingLabel = false
          this.errorInAdding = false
          this.showSidebar = false
          // this.successMessage = 'Successfully Added!'
          this.resetList();
          this.getInitialData(); 
        } else {
          throw new Error()
        }
      } catch (err) {
        this.errorInAdding = true
        this.addingLabel = false
        console.log(err)
      }
    },

    async deleteLabel() {
      try {
        const { data } = await axios.delete(`api/label/${this.labelId}?user_id=${this.user_id}`)
        if (!data.ok) {
          throw new Error()
        } else {
          this.errorMessage = ''
          this.successMessage = 'Successfully deleted!'
          this.showConfirmModal = false
          this.resetList();
          this.getInitialData();
          this.$store.dispatch("callUpdateRoomLabels", this.labelId);
        }
      } catch (err) {
        this.errorMessage = 'Something went wrong!'
        console.log(err)
      }
    },
  }
}
</script>
<style scoped>
.popover__wrapper {
  position: relative;
}
.popover__content {
  opacity: 0;
  visibility: hidden;
  position: absolute;
  left: 0px;
  transform: translate(-0.8rem, 0px);
  /* background-color: #f1f1f1; */
  /* padding: 0.8rem; */
  /* box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26); */
  width: auto;
  border-radius: 3px;
  padding-top: 0px;
}
.popover__wrapper:hover .popover__content {
  z-index: 10;
  opacity: 1;
  visibility: visible;
  transform: translate(-0.8rem, 3.7rem);
  transition: opacity 0.5s cubic-bezier(0.75, -0.02, 0.2, 0.97);
  top: 0;
}
.popover__message {
  font-size: 1.8rem;
  font-weight: 500;
  margin-bottom: 0px;
}
</style>
