<template>
  <div class="new-contact-wrapper">
    <div class="new-contact-inner">
      <div class="start-icon">
        <img :src="addContactIcon" alt="" />
      </div>
      <div class="profile-box" @click="handleNewAccount">
        <p>New Contact</p>
      </div>
    </div>
  </div>
</template>

<script>
import addContactIcon from '../../../components/SvgIcon/add_contact_icon.svg'
export default {
  name: 'NewContact',

  data() {
    return {
      addContactIcon
    }
  },
  methods: {
    handleNewAccount() {
      const url = 'https://www.niswey.com/hubspot-whatsapp-integration'
      window.open(url, '_blank')
    }
  }
}
</script>
