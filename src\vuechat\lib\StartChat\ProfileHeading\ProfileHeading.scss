.profile-heading-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 10rem;
  background: #f5f5f5;
  border-right: 1px solid #d1d7db;
  padding: 0 4.8rem;

  .profile-box {
    display: flex;

    img {
      width: 7rem;
      height: 7rem;
      border-radius: 50%;
      margin-right: 3.2rem;
    }

    .profile-info {
      display: flex;
      flex-direction: column;
      justify-content: center;
      font-weight: 400;
      font-size: 1.6rem;
      line-height: 2rem;
      color: rgba(0, 0, 0, 0.7);

      .login-as {
        margin-bottom: 5px;
      }

      .profile-name {
        font-size: 2rem;
        font-weight: 500;
        color: #000;
      }
    }
  }

  .start-icon {
    img {
      width: 3.6rem;
      height: 3.6rem;
      cursor: pointer;
    }
  }

  @media screen and (max-width: 1919px) {
    .profile-box {
      .profile-info {
        font-size: 13px;
        line-height: 16px;

        .profile-name {
          font-size: 14px;
        }
      }
    }
  }
}
