<template>
  <div class="profile-heading-wrapper">
    <div class="profile-box">
      <p>New Chat</p>
    </div>
    <div class="start-icon">
      <img :src="crossChatIcon" alt="" @click="startChat" />
    </div>
  </div>
</template>

<script>
import crossChatIcon from '../../../components/SvgIcon/cross_icon.svg'
export default {
  name: 'ProfileHeading',

  emits: ['clicked'],
  data() {
    return {
      crossChatIcon
    }
  },
  methods: {
    startChat() {
      this.$emit('clicked')
    }
  }
}
</script>
<style scoped>
.profile-heading-wrapper .profile-box {
  display: flex;
  width: 100%;
  justify-content: center;
  vertical-align: middle;
}
.profile-heading-wrapper .profile-box p {
  text-align: center;
  margin-bottom: 0px;
  font-size: 2.4rem;
  font-weight: 600;
}
</style>
