.vac-room-container {
  display: flex;
  flex: 1;
  align-items: center;
  width: 100%;

  .hwa-room-meta {
    display: flex;
    flex-direction: column-reverse;
    margin-right: 10px;
    min-width: 3rem;
    position: absolute;
    left: 5px;

    .label-icon {
      display: inline-block;
      width: 3rem;
      height: 2rem;
      clip-path: polygon(65% 0, 99% 50%, 65% 100%, 0 100%, 0 0);
      background: red;

      &:not(:last-of-type) {
        margin-top: -1rem;
      }
    }

    .pin-icon {
      display: none;
      width: 2.9rem;
      height: 2.9rem;
    }
  }

  .vac-name-container {
    flex: 1;

    .vac-text-ellipsis {
      width: 23rem;

      span.msg {
        font-size: 2rem;
        line-height: 3rem;
        font-weight: 400;
        color: #000000;
      }
    }
  }

  .vac-title-container {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .vac-state-circle {
    width: 9px;
    height: 9px;
    border-radius: 50%;
    background-color: var(--chat-room-color-offline);
    margin-right: 6px;
    transition: 0.3s;
  }

  .vac-state-online {
    background-color: var(--chat-room-color-online);
  }

  .vac-room-name {
    font-weight: 600;
    font-size: 2.5rem;
    line-height: 3.7rem;
    color: #000000;
    flex: 1;
    color: var(--chat-room-color-username);
  }

  .vac-text-date {
    font-weight: 400;
    font-size: 1.8rem;
    line-height: 2.2rem;
    // letter-spacing: 0.003em;
    color: rgba(0, 0, 0, 0.7);
    margin-left: 5px;
    // color: var(--chat-room-color-timestamp);
  }

  .vac-text-last {
    display: flex;
    align-items: center;
    font-size: 12px;
    line-height: 19px;
    color: var(--chat-room-color-message);
  }

  .vac-message-new {
    color: var(--chat-room-color-username);
    font-weight: 500;
  }

  .vac-icon-check {
    display: flex;
    vertical-align: middle;
    height: 14px;
    width: 14px;
    margin-top: -2px;
    margin-right: 2px;
  }

  .vac-icon-microphone {
    height: 15px;
    width: 15px;
    vertical-align: middle;
    margin: -3px 1px 0 -2px;
    fill: var(--chat-room-color-message);
  }

  .vac-room-options-container {
    display: flex;
    align-items: center;
    margin-left: auto;
  }

  .vac-room-badge {
    background-color: var(--chat-room-bg-color-badge);
    color: var(--chat-room-color-badge);
    margin-left: 5px;
  }

  .vac-list-room-options {
    opacity: 0;
    height: 20px;
    width: 20px;
    align-items: center;
    margin-left: 5px;
  }

  .vac-menu-options {
    width: 19.6rem;
    // left: 93%;
    // top: 46px;

    .vac-menu-list {
      padding: 0;
      width: 100%;

      .vac-menu-item {
        font-weight: 400;
        font-size: 1.8rem;
        line-height: 2.2rem;
        color: #000000;
        padding: 1.9rem 1.7rem;

        &:hover {
          background: #f1f1f1;
        }
      }
    }
  }

  @media only screen and (max-width: 1919px) {
    .vac-menu-options {
      .vac-menu-list {
        .vac-menu-item {
          font-size: 13px;
          line-height: 17px;
          padding: 13px 14px;
        }
      }
    }
  }
}
