<template>
  <div class="vac-room-container">
    <slot name="room-list-item" v-bind="{ room }">
      <div class="hwa-room-meta">
        <span
          v-for="label in room.labels.slice(0, 2)"
          v-show="showRoomsLabel"
          :key="label.id"
          class="label-icon"
          :style="{ background: label.color }"
        />
        <img class="pin-icon" :class="{ 'd-block': room.pinned }" :src="pinIcon" alt="pin" height="15" />
      </div>

      <slot name="room-list-avatar" v-bind="{ room }">
        <div
          class="vac-avatar"
          :style="{
            'background-image': `url('${room.avatar || dummyAvatar}')`
          }"
        />
      </slot>
      <div class="vac-name-container vac-text-ellipsis">
        <div class="vac-title-container">
          <div v-if="userStatus" class="vac-state-circle" :class="{ 'vac-state-online': userStatus === 'online' }" />
          <div class="vac-room-name vac-text-ellipsis">
            {{ room.roomName }}
          </div>
          <!-- <div v-if="room.lastMessage" class="vac-text-date">
						{{ room.lastMessage.timestamp }}
					</div> -->
        </div>
      </div>
    </slot>
  </div>
</template>

<script>
import vClickOutside from 'v-click-outside'

import PinIcon from '../../../components/PngIcons/pin_icon.png'
import DummyAvatar from '../../../components/PngIcons/profile-placeholder.png'

// import typingText from '../../../utils/typing-text'
// const { isAudioFile } = require('../../../utils/media-file')

export default {
  name: 'RoomsContent',
  components: {},

  directives: {
    clickOutside: vClickOutside.directive
  },

  props: {
    currentUserId: { type: [String, Number], required: true },
    room: { type: Object, required: true },
    // textFormatting: { type: Boolean, required: true },
    // linkOptions: { type: Object, required: true },
    // textMessages: { type: Object, required: true },
    showRoomsLabel: { type: Boolean, required: true }
    // roomMenuOpened: { type: [String, Number], default: null },

    // unreadCounts: { type: Object, required: true }
  },

  emits: ['room-action-handler', 'handle-show-labels', 'open-room-menu', 'close-room-menu'],

  data() {
    return {
      pinIcon: PinIcon,
      dummyAvatar: DummyAvatar
    }
  },

  computed: {
    // getLastMessage() {
    // 	const isTyping = this.typingUsers
    // 	if (isTyping) return isTyping
    // 	const content = this.room.lastMessage.deleted
    // 		? this.textMessages.MESSAGE_DELETED
    // 		: this.room.lastMessage.content

    // 	return content
    // },
    userStatus() {
      if (!this.room.users || this.room.users.length !== 2) return

      const user = this.room.users.find(u => u._id !== this.currentUserId)
      if (user && user.status) return user.status.state

      return null
    }
    // typingUsers() {
    // 	return typingText(this.room, this.currentUserId, this.textMessages)
    // },
    // isMessageCheckmarkVisible() {
    // 	return (
    // 		!this.typingUsers &&
    // 		this.room.lastMessage &&
    // 		!this.room.lastMessage.deleted &&
    // 		this.room.lastMessage.fromMe === 1 &&
    // 		(this.room.lastMessage.saved ||
    // 			this.room.lastMessage.distributed ||
    // 			this.room.lastMessage.seen)
    // 	)
    // },
    // formattedDuration() {
    // 	const file = this.room.lastMessage.files[0]

    // 	if (!file.duration) {
    // 		return `${file.name}.${file.extension}`
    // 	}

    // 	let s = Math.floor(file.duration)
    // 	return (s - (s %= 60)) / 60 + (s > 9 ? ':' : ':0') + s
    // },
    // isAudio() {
    // 	return this.room.lastMessage.files
    // 		? isAudioFile(this.room.lastMessage.files[0])
    // 		: false
    // },
    // roomActions() {
    // 	return [
    // 		{
    // 			name: 'pinChat',
    // 			title: this.room.pinned ? 'Unpin Chat' : 'Pin Chat'
    // 		},
    // 		{
    // 			name: 'labelChat',
    // 			title: 'Label Chat'
    // 		}
    // 	]
    // },
    // unreadCount() {
    // 	return this.unreadCounts[this.room.roomId]?.val || 0
    // }
  },

  methods: {
    // roomActionHandler(action) {
    // 	this.closeRoomMenu()
    // 	if (action.name === 'labelChat') {
    // 		this.toggleLabelsModal(this.room)
    // 	} else if (action.name === 'pinChat') {
    // 		this.$emit('handle-show-labels', true)
    // 	}
    // 	this.$emit('room-action-handler', { action, did: this.room.did })
    // },
    // closeRoomMenu() {
    // 	this.$emit('close-room-menu')
    // }
  }
}
</script>
