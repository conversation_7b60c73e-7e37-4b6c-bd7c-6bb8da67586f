.vac-box-empty {
  margin-top: 10px;

  @media only screen and (max-width: 768px) {
    margin-top: 7px;
  }
}

.vac-box-search {
  position: relative;
  display: flex;
  align-items: center;
  height: 10.8rem;
  padding: 2.2rem 5.25rem 2.2rem 4.8rem;
  // border-bottom: 1px solid #ededed;

  .hwa-label {
    // margin-right: 2.8rem;
    display: flex;
    align-items: center;

    // .hwa-label_text {
    // 	font-size: 2.6rem;
    // 	line-height: 3.9rem;
    // 	letter-spacing: 0.003em;
    // 	color: #000000;
    // 	font-weight: 500;
    // 	margin-right: 1.4rem;
    // }

    img {
      height: 1.575rem;
      cursor: pointer;
    }

    .close-list {
      background: none;
      border: none;
      font-size: 5.5rem;
      position: relative;
      // top: 3px;
      cursor: pointer;
      padding: 0;
      font-weight: 300;
    }
  }

  .labels-list-wrapper {
    background: white;
    right: unset;
    right: 0;
    top: 80%;
    height: 27rem;
    padding: 2.6rem 0;
    width: 28.2rem;
    box-shadow: 2px 2px 30px rgba(123, 123, 123, 0.25);
  }

  .labels-list {
    overflow-y: auto;
    height: 100%;
    padding: 0 2.8rem;
    min-width: 170px;

    .list-group-item {
      border: none;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0;

      &:not(:last-of-type) {
        padding: 0 0 1.3rem 0;
      }

      .label-text {
        font-weight: 500;
        font-size: 1.6rem;
        line-height: 2.7rem;
        color: #000000;
      }

      .label-icon {
        display: inline-block;
        width: 3rem;
        height: 2rem;
        clip-path: polygon(65% 0, 99% 50%, 65% 100%, 0 100%, 0 0);
        background: red;
        margin-right: 2rem;
      }

      .label-check {
        display: inline-flex;
        border: 1px solid #919192;

        &:hover {
          outline: none;
          box-shadow: none;
          cursor: pointer;
        }
      }
    }
  }

  .hwa-search-box {
    position: relative;
  }

  img.vac-icon-search {
    display: block;
    max-width: 100%;
    position: absolute;
    width: 2.4rem;
    left: 2.4rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
  }

  .clear-search {
    position: absolute;
    width: 2.2rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
    right: 2.6rem;
    font-size: 4.5rem;
    display: block;
    line-height: 2.5rem;
    text-align: center;
    // top: -5px;
    position: absolute;
    color: #919192;
    cursor: pointer;
  }

  .vac-input {
    height: 38px;
    width: 100%;
    background: var(--chat-bg-color-input);
    color: var(--chat-color);
    border-radius: 4px;
    font-size: 15px;
    outline: 0;
    caret-color: var(--chat-color-caret);
    padding: 10px 10px 10px 40px;
    border: 1px solid var(--chat-sidemenu-border-color-search);
    border-radius: 20px;

    &::placeholder {
      color: var(--chat-color-placeholder);
    }
  }

  .hwa-chat-search-input {
    background: #f5f5f5;
    width: 100%;
    height: 6rem;
    padding: 1.8rem 6rem 1.8rem 7.2rem;
    color: rgba(156, 166, 175, 0.6);
    font-size: 2rem;
    line-height: 2.4rem;
    outline: none;
    box-shadow: none;
    background: #ffffff;
    border: 0.3rem solid #d2d2d2;
    border-radius: 6rem;

    &:focus {
      box-shadow: none;
      border: 0.3rem solid #d2d2d2;
    }

    &::placeholder {
      color: rgba(156, 166, 175, 0.6);
    }
  }

  .vac-add-icon {
    margin-left: auto;
    padding-left: 10px;
  }

  @media only screen and (max-width: 1919px) {
    height: 10.8rem;

    .hwa-label {
      // .close-list {
      // 	font-size: 35px;
      // 	top: 2px;
      // 	left: 5px;
      // 	font-weight: 300;
      // }

      img {
        height: 11px;
      }
    }

    .hwa-chat-search-input {
      font-size: 12px;
    }

    .labels-list {
      .list-group-item {
        .label-text {
          font-size: 11px;
          line-height: 20px;
        }
      }
    }
  }

  // @media only screen and (max-width: 768px) {
  // 	height: 58px;
  // }
}
