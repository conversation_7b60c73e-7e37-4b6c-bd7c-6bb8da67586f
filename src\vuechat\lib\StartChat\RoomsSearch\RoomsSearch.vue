<template>
  <div
    :class="{
      'vac-box-search': showSearchBar,
      'vac-box-empty': !showSearchBar
    }"
  >
    <div v-if="!loadingRooms && rooms.length" class="w-100">
      <!-- Search -->
      <template v-if="showSearch">
        <div class="hwa-search-box">
          <div v-if="!loadingRooms && rooms.length" class="">
            <img class="vac-icon-search" :src="searchIcon" alt="search icon" />
            <span v-if="searchText" class="clear-search" @click.prevent="$emit('clear-search')"> &times; </span>
          </div>
          <input
            v-if="!loadingRooms && rooms.length"
            type="input"
            placeholder="Search Contact"
            autocomplete="off"
            :value="searchText"
            class="form-control hwa-chat-search-input"
            @input="handleSearch"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import SearchIcon from '../../../components/SvgIcon/search_icon.svg'
// import FilterIcon from '../../../components/SvgIcon/filter_icon.svg'
import vClickOutside from 'v-click-outside'

export default {
  name: 'RoomsSearch',

  directives: {
    clickOutside: vClickOutside.directive
  },

  props: {
    searchText: { type: String, required: true },
    showSearch: { type: Boolean, required: true },
    showAddRoom: { type: Boolean, required: true },
    rooms: { type: Array, required: true },
    loadingRooms: { type: Boolean, required: true },
    // showRoomsLabel: { type: Boolean, required: true },
    labels: { type: Array, required: true }
  },

  emits: ['filter-room-by-labels', 'handle-show-labels', 'reset-room-search', 'handle-search', 'clear-search'],

  data() {
    return {
      // filterIcon: FilterIcon,
      searchIcon: SearchIcon,
      // labelsListOpened: false,
      labelsData: this.labels,
      timeout: null
    }
  },

  computed: {
    showSearchBar() {
      return this.showSearch || this.showAddRoom
    }
  },

  watch: {
    labels: function () {
      this.labelsData = this.labels
    }
  },

  beforeDestroy() {
    clearTimeout(this.timeout)
  },

  methods: {
    // resetList() {
    // 	// const labelsArr = this.labelsData.map(el => ({
    // 	// 	...el,
    // 	// 	selected: false
    // 	// }))
    // 	this.labelsData = this.labels
    // 	this.$emit('reset-room-search')
    // 	// this.searchText = ''
    // 	// this.$emit('handle-show-labels', true)
    // 	// this.$emit('filter-room-by-labels', [])
    // },
    // closeList() {
    // 	this.labelsListOpened = false
    // },
    // openList() {
    // 	const labelsArr = this.labelsData.map(el => ({
    // 		...el,
    // 		selected: false
    // 	}))
    // 	this.labelsData = labelsArr
    // 	this.labelsListOpened = true
    // 	this.$emit('handle-show-labels', false)
    // },
    // filterRooms(event, idx) {
    // 	this.labelsData[idx].selected = event.target.checked
    // 	const labelIds = this.labelsData
    // 		.filter(el => el.selected)
    // 		.map(el => el.id)
    // 	this.$emit('filter-room-by-labels', labelIds)
    // },
    handleSearch(event) {
      if (this.timeout) clearTimeout(this.timeout)

      this.timeout = setTimeout(() => {
        this.$emit('handle-search', event)
      }, 200) // delay
    }
  }
}
</script>
<style scoped>
.popover__wrapper {
  position: relative;
}
.popover__content {
  opacity: 0;
  visibility: hidden;
  position: absolute;
  left: 0px;
  transform: translate(-0.8rem, 0px);
  /* background-color: #f1f1f1; */
  /* padding: 0.8rem; */
  /* box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26); */
  width: auto;
  border-radius: 3px;
  padding-top: 0px;
}
.popover__wrapper:hover .popover__content {
  z-index: 10;
  opacity: 1;
  visibility: visible;
  transform: translate(-0.8rem, 3.7rem);
  transition: opacity 0.5s cubic-bezier(0.75, -0.02, 0.2, 0.97);
}
.popover__message {
  font-size: 1.8rem;
  font-weight: 500;
  margin-bottom: 0px;
}
.vac-box-search .hwa-chat-search-input {
  width: 100%;
}
</style>
