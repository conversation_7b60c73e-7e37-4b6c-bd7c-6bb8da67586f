<template>
  <transition name="vac-fade-message">
    <div v-show="showRoomsList" class="vac-rooms-container" :class="{ 'vac-rooms-container-full': isMobile }">
      <slot name="rooms-header" />
      <profile-heading :user-data="userData" @clicked="clicked" />
      <slot name="rooms-list-search">
        <transition name="vac-fade-message">
          <rooms-search
            :rooms="rooms"
            :loading-rooms="loadingRooms"
            :text-messages="textMessages"
            :show-search="showSearch"
            :show-add-room="showAddRoom"
            :show-rooms-label="showRoomsLabel"
            :labels="labelsData"
            :search-text="searchText"
            @reset-room-search="resetRoomSearch"
            @handle-search="handleSearch"
            @clear-search="clearSearch"
            @handle-show-labels="handleShowLabels"
            @filter-room-by-labels="filterRoomByLabels"
            @search-room="searchRoom"
            @add-room="$emit('add-room')"
          >
            <template v-for="(i, name) in $scopedSlots" #[name]="data">
              <slot :name="name" v-bind="data" />
            </template>
          </rooms-search>
        </transition>
      </slot>
      <!-- new contact  -->

      <NewContact />

      <!-- end new contact -->

      <loader :show="loadingRooms" />

      <div v-if="!loadingRooms && !rooms.length" class="vac-rooms-empty">
        <slot name="rooms-empty">
          {{ textMessages.ROOMS_EMPTY }}
        </slot>
      </div>

      <!-- filteredRoom -->

      <div v-if="!loadingRooms && (search || showRoomsLabel)" class="vac-room-list filter">
        <div
          v-for="(fRoom,index) in filteredRooms"
          :id="fRoom.roomId"
          :key="`${fRoom.roomId}-${fRoom.created_at}-${index}`"
          class="vac-room-item"
          :class="{ 'vac-room-selected': selectedRoomId === fRoom.roomId }"
          @click="openRoom(fRoom)"
        >
          <room-content
            :current-user-id="currentUserId"
            :room="fRoom"
            :text-formatting="textFormatting"
            :link-options="linkOptions"
            :text-messages="textMessages"
            :room-actions="roomActions"
            :show-rooms-label="showRoomsLabel"
            :toggle-labels-modal="toggleLabelsModal"
            :room-menu-opened="roomMenuOpened"
            :unread-counts="unreadCounts"
            @open-room-menu="openRoomMenu"
            @close-room-menu="closeRoomMenu"
            @handle-show-labels="handleShowLabels"
            @room-action-handler="$emit('room-action-handler', $event)"
          >
            <template v-for="(i, name) in $scopedSlots" #[name]="data">
              <slot :name="name" v-bind="data" />
            </template>
          </room-content>
        </div>
      </div>

      <!-- all rooms -->
      <!-- <div v-for="(value, key) in grouped" :key="key">
				<h2>{{ key }}</h2>
				<ul>
					<li v-for="contact in value" :key="contact.roomName">
						{{ contact.roomName }}
					</li>
				</ul>
			</div> -->

      <div v-show="!loadingRooms && !search && !showRoomsLabel" id="start-chat-list" class="vac-room-list">
        <div v-for="(value, key) in grouped" :key="key">
          <div class="alphabet-order">
            <h2>{{ key }}</h2>
          </div>
          <div
            v-for="(contact,index) in value"
            :id="contact.roomId"
            :key="`${contact.roomId}-${index}-${Date.now()}`"
            class="vac-room-item"
            :class="{ 'vac-room-selected': selectedRoomId === contact.roomId }"
            @click="openRoom(contact)"
          >
            <room-content
              :current-user-id="currentUserId"
              :room="contact"
              :text-formatting="textFormatting"
              :link-options="linkOptions"
              :text-messages="textMessages"
              :room-actions="roomActions"
              :show-rooms-label="showRoomsLabel"
              :toggle-labels-modal="toggleLabelsModal"
              :room-menu-opened="roomMenuOpened"
              :unread-counts="unreadCounts"
              @open-room-menu="openRoomMenu"
              @close-room-menu="closeRoomMenu"
              @handle-show-labels="handleShowLabels"
              @room-action-handler="$emit('room-action-handler', $event)"
            >
              <template v-for="(i, name) in $scopedSlots" #[name]="data">
                <slot :name="name" v-bind="data" />
              </template>
            </room-content>
          </div>
        </div>
        <transition name="vac-fade-message">
          <div v-if="rooms.length && !loadingRooms" id="start-chat-list-loader">
            <loader :show="showLoader" :infinite="true" />
          </div>
        </transition>
      </div>
    </div>
  </transition>
</template>

<script>
import Loader from '../../components/Loader/Loader'

import RoomsSearch from './RoomsSearch/RoomsSearch'
import RoomContent from './RoomContent/RoomContent'
import ProfileHeading from './ProfileHeading/ProfileHeading'
import filteredItems from '../../utils/filter-items'
import NewContact from './NewContact/NewContact'
import groupBy from 'lodash/groupBy'
import orderBy from 'lodash/orderBy'

export default {
  name: 'RoomsList',
  components: {
    Loader,
    RoomsSearch,
    RoomContent,
    ProfileHeading,
    NewContact
  },

  props: {
    userData: { type: Object, required: true },
    currentUserId: { type: [String, Number], required: true },
    textMessages: { type: Object, required: true },
    showRoomsList: { type: Boolean, required: true },
    showSearch: { type: Boolean, required: true },
    showAddRoom: { type: Boolean, required: true },
    textFormatting: { type: Boolean, required: true },
    linkOptions: { type: Object, required: true },
    isMobile: { type: Boolean, required: true },
    rooms: { type: Array, required: true },
    loadingRooms: { type: Boolean, required: true },
    roomsLoaded: { type: Boolean, required: true },
    room: { type: Object, required: true },
    roomActions: { type: Array, required: true },
    toggleLabelsModal: { type: Function, default: () => ({}) },
    labels: { type: Array, required: true },
    unreadCounts: { type: Object, required: true }
  },

  emits: ['add-room', 'room-action-handler', 'loading-more-rooms', 'fetch-room', 'fetch-more-rooms', 'closeChat'],

  data() {
    return {
      search: false,
      searchText: '',
      filteredRooms: this.rooms || [],
      sRooms: this.rooms || [],
      filteredRoomsByLabel: this.rooms || [],
      observer: null,
      showLoader: true,
      loadingMoreRooms: false,
      selectedRoomId: '',
      showRoomsLabel: false,
      roomMenuOpened: null
    }
  },

  computed: {
    labelsData() {
      return this.labels.map(el => ({ ...el, selected: false }))
    },
    grouped() {
      return groupBy(this.sRooms, item => {
        return item.roomName.charAt(0).toUpperCase()
      })
    }
  },

  watch: {
    rooms: {
      deep: true,
      handler(newVal, oldVal) {
        this.sRooms = orderBy(newVal, [user => user.roomName.toLowerCase()], ['asc'])
        if (newVal.length !== oldVal.length || this.roomsLoaded) {
          this.loadingMoreRooms = false
        }
      }
    },
    loadingRooms(val) {
      if (!val) {
        setTimeout(() => this.initIntersectionObserver())
      }
    },
    loadingMoreRooms(val) {
      this.$emit('loading-more-rooms', val)
    },
    roomsLoaded: {
      immediate: true,
      handler(val) {
        if (val) {
          this.loadingMoreRooms = false
          if (!this.loadingRooms) {
            this.showLoader = false
          }
        }
      }
    },
    room: {
      immediate: true,
      handler(val) {
        if (val && !this.isMobile) this.selectedRoomId = val.roomId
      }
    }
  },

  mounted() {
    // console.log(this.userData, 'user data')
    // console.log(this.sRooms, 'sRooms')
    const sortedRooms = orderBy(this.sRooms, [user => user.roomName.toLowerCase()], ['asc'])
    // console.log('sorted array', sortedRooms)
    this.sRooms = sortedRooms
    // console.log('final array', this.sRooms)
  },

  methods: {
    initIntersectionObserver() {
      if (this.observer) {
        this.showLoader = true
        this.observer.disconnect()
      }

      const loader = document.getElementById('start-chat-list-loader')

      if (loader) {
        const options = {
          root: document.getElementById('start-chat-list'),
          rootMargin: '60px',
          threshold: 0
        }

        this.observer = new IntersectionObserver(entries => {
          if (entries[0].isIntersecting) {
            this.loadMoreRooms()
          }
        }, options)

        this.observer.observe(loader)
      }
    },
    searchRoom(ev) {
      if (ev.target.value) {
        this.search = true
        this.showLoader = false
      } else {
        this.search = false
        if (!this.roomsLoaded && !this.showRoomsLabel) {
          this.showLoader = true
        }
      }
      const searchArray = this.showRoomsLabel ? this.filteredRoomsByLabel : this.sRooms
      this.filteredRooms = filteredItems(searchArray, 'roomName', 'phone', ev.target.value)
    },
    openRoom(room) {
      if (room.roomId === this.room.roomId && !this.isMobile) return
      if (!this.isMobile) this.selectedRoomId = room.roomId
      this.$emit('fetch-room', { room })
    },
    loadMoreRooms() {
      if (this.loadingMoreRooms || this.search || this.showRoomsLabel) return

      if (this.roomsLoaded) {
        this.loadingMoreRooms = false
        return (this.showLoader = false)
      }

      if (this.sRooms.length < 300) return

      this.$emit('fetch-more-rooms')
      this.loadingMoreRooms = true
    },
    handleShowLabels(show) {
      if (show) {
        this.searchText = ''
        this.showRoomsLabel = false
        if (!this.roomsLoaded) {
          this.showLoader = true
        }
        this.search = false
      } else {
        this.searchText = ''
        this.showRoomsLabel = true
        this.showLoader = false
        this.filteredRooms = this.sRooms
      }
    },
    filterRoomByLabels(labelIds) {
      this.searchText = ''
      if (labelIds.length) {
        const filteredRooms = []
        labelIds.forEach(id => {
          const _rooms = this.rooms.filter(room => {
            const labels = room.labels.map(lbl => lbl.id === id)
            return labels.includes(true)
          })
          filteredRooms.push(..._rooms)
        })
        this.filteredRooms = [...new Set(filteredRooms)]
        this.filteredRoomsByLabel = [...new Set(filteredRooms)]
      } else {
        this.filteredRooms = this.rooms
        this.filteredRoomsByLabel = this.rooms
      }
    },
    openRoomMenu(roomId) {
      this.roomMenuOpened = null
      this.roomMenuOpened = roomId
    },
    closeRoomMenu() {
      this.roomMenuOpened = null
    },
    resetRoomSearch() {
      this.searchText = ''
      this.handleShowLabels(true)
      this.filterRoomByLabels([])
    },
    handleSearch(event) {
      this.searchText = event.target.value
      this.searchRoom(event)
    },
    clearSearch() {
      this.searchText = ''
      this.searchRoom({ target: { value: '' } })
    },
    clicked() {
      this.$emit('closeChat')
    }
  }
}
</script>
