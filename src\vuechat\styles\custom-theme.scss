@import 'bootstrap';

// Variables
$color-primary: #34b7f1;
$color-btn-secondary: #2c3d4f;
$color-gray: #d2d2d2;
$color-white: #fff;
$color-orange: #f7892f;

// Colors
$theme-colors: (
  'primary': $color-primary,
  'danger': #ff6a6a,
  'success': #2be597,
  'warning': #e5bc2b,
  'secondary': #eef3fb,
  'orange': #f7892f,
  'darkblue': #2c3f51,
  'primary-100': #ccedfc,
  'primary-300': #aee2f9,
  'primary-700': #2fa5d9,
  'primary-900': #2789b5,
  'gray-100': #f5f5f5,
  'gray-300': #ededed,
  'gray-500': $color-gray,
  'gray-700': #919192,
  'gray-900': #7e7e7e
);
$font-family-sans-serif: 'Montserrat', sans-serif;
$grid-gutter-width: 40px;

@import 'bootstrap';

// Typography
h1 {
  font-size: 5rem;
  font-weight: 600;
}

h2 {
  font-size: 4rem;
  font-weight: 700;
}

h3 {
  font-size: 3rem;
  font-weight: 700;
}

h4 {
  font-size: 2.6rem;
  font-weight: 700;
}

h5 {
  font-size: 2.5rem;
  font-weight: 600;
}

h6 {
  font-size: 2.5rem;
  font-weight: 500;
}

p {
  font-size: 2.5rem;
  font-weight: 400;
  max-width: 800px;
}

.body-1 {
  font-size: 2.5rem;
  font-weight: 400;
}

.body-2 {
  font-size: 2rem;
  font-weight: 400;
}

.subtitle-1 {
  font-size: 3rem;
  font-weight: 600;
}

.subtitle-2 {
  font-size: 2.5rem;
  font-weight: 600;
}

.subtitle-3 {
  font-size: 2.5rem;
  font-weight: 500;
}

.caption {
  font-size: 1.5rem;
  font-weight: 400;
}

.overline {
  font-size: 1.2rem;
  font-weight: 400;
}

.overline-m {
  font-size: 1.2rem;
  font-weight: 500;
}

// Buttons
.btn {
  font-size: 2rem;
  font-weight: 600;
  padding: 1.3rem 2.6rem;
  border-radius: 0.8rem;
}

.btn-primary {
  color: $color-white;

  &:focus {
    color: $color-white;
  }

  &:hover {
    color: $color-primary;
    background: $color-white;
    border-color: $color-primary;
  }

  &:disabled {
    background: $color-gray;
    color: $color-white;
    border-color: $color-gray;
  }
}

.btn-secondary {
  color: $color-white;
  background: $color-btn-secondary;

  &:focus {
    color: $color-white;
    background: $color-btn-secondary;
  }

  &:hover {
    color: $color-btn-secondary;
    background: $color-white;
    border-color: $color-btn-secondary;
  }

  &:disabled {
    background: $color-gray;
    color: $color-white;
    border-color: $color-gray;
  }
}
