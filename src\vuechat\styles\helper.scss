.vac-app-border {
  border: var(--chat-border-style);
}

.vac-app-border-t {
  border-top: var(--chat-border-style);
}

.vac-app-border-r {
  border-right: var(--chat-border-style);
}

// .vac-app-border-b {
// 	border-bottom: var(--chat-border-style);
// }

.vac-app-box-shadow {
  transition: all 0.5s;
  box-shadow: 0 2px 2px -4px rgba(0, 0, 0, 0.1), 0 2px 2px 1px rgba(0, 0, 0, 0.12), 0 1px 8px 1px rgba(0, 0, 0, 0.12);
}

.vac-item-clickable {
  cursor: pointer;
}

.vac-vertical-center {
  display: flex;
  align-items: center;
  height: 100%;

  .vac-vertical-custom-checkbox {
    width: 100%;
    text-align: center;
  }
}

.vac-svg-button {
  // max-height: 30px;
  display: flex;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    transform: scale(1.1);
    opacity: 0.7;
  }
}

.vac-avatar {
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-color: #ddd;
  height: 7rem;
  width: 7rem;
  min-height: 7rem;
  min-width: 7rem;
  margin-right: 15px;
  border-radius: 50%;
}

.vac-badge-counter {
  height: 13px;
  width: auto;
  min-width: 13px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3px;
  font-size: 9px;
  font-weight: 500;
}

.vac-text-ellipsis {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 2rem;
  line-height: 3rem;
  font-weight: 400;
  color: #000000;
}

.vac-text-bold{
  font-weight: 700 !important;
}

.vac-text-italic {
  font-style: italic;
}

.vac-text-strike {
  text-decoration: line-through;
}

.vac-text-underline {
  text-decoration: underline;
}

.vac-text-inline-code {
  display: inline-block;
  font-size: 12px;
  color: var(--chat-markdown-color);
  background: var(--chat-markdown-bg);
  border: 1px solid var(--chat-markdown-border);
  border-radius: 3px;
  margin: 2px 0;
  padding: 2px 3px;
}

.vac-text-multiline-code {
  display: block;
  font-size: 12px;
  color: var(--chat-markdown-color-multi);
  background: var(--chat-markdown-bg);
  border: 1px solid var(--chat-markdown-border);
  border-radius: 3px;
  margin: 4px 0;
  padding: 7px;
}

.vac-text-tag {
  color: var(--chat-message-color-tag);
  cursor: pointer;
}

.vac-file-custom-checkbox {
  display: flex;
  align-content: center;
  justify-content: center;
  flex-wrap: wrap;
  text-align: center;
  background: var(--chat-bg-color-input);
  border: var(--chat-border-style-input);
  border-radius: 4px;
  padding: 10px;

  svg {
    height: 28px;
    width: 28px;
  }

  .vac-text-extension {
    font-size: 12px;
    color: var(--chat-message-color-file-extension);
    margin-top: -2px;
  }
}

.opacity-100 {
  opacity: 1 !important;
}

.custom-checkbox {
  display: block;
  position: relative;
  padding-left: 13px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.custom-checkbox.forward {
  padding-left: 22px;
  margin-bottom: 18px;
}

.custom-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 13px;
  width: 13px;
  border: 1px solid #919192;
  border-radius: 2px;
}

.custom-checkbox.forward .checkmark {
  height: 3rem;
  width: 3rem;
}

.custom-checkbox:hover input ~ .checkmark {
  background-color: #ccc;
}

.checkmark:after {
  content: '';
  position: absolute;
  display: none;
}

.custom-checkbox.standard input:checked ~ .checkmark:after {
  display: block;
}

.custom-checkbox.forward.checked input ~ .checkmark:after {
  display: block;
}

.custom-checkbox.forward.checked .checkmark {
  background: #34b7f1;
  border: 1px solid #34b7f1;
}

.custom-checkbox.forward.checked .checkmark:after {
  border: solid #fff;
  border-width: 0 2px 2px 0;
}

.custom-checkbox .checkmark:after {
  left: 4px;
  top: 1px;
  width: 4px;
  height: 7px;
  border: solid black;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.custom-checkbox.forward .checkmark:after {
  left: 0.8rem;
}

.custom-checkbox.forward .checkmark:after {
  width: 1rem;
  height: 1.7rem;
}

@media only screen and (min-width: 1920px) {
  .custom-checkbox,
  .custom-checkbox.forward {
    padding-left: 21px;
    margin-bottom: 21px;
  }
  .checkmark,
  .custom-checkbox.forward .checkmark {
    height: 21px;
    width: 21px;
  }

  .custom-checkbox .checkmark:after,
  .custom-checkbox.forward .checkmark:after {
    left: 7px;
    top: 3px;
    width: 6px;
    height: 11px;
  }

  .vac-badge-counter {
    height: 1.8rem;
    min-width: 1.8rem;
    font-size: 1.2rem;
  }
}
