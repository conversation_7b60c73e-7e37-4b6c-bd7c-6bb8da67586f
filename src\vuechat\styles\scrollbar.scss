::-webkit-scrollbar {
  width: 5px;
  height: 1.2rem;
}

::-webkit-scrollbar-track {
  background: $color-white;
}

::-webkit-scrollbar-thumb {
  background: #d2d2d2;
  border-radius: 30px;
}

::-webkit-scrollbar-thumb:hover {
  background: darken(#d2d2d2, 10%);
}

.vac-container-scroll {
  &::-webkit-scrollbar {
    width: 1.2rem;
  }

  &::-webkit-scrollbar-thumb {
    background: $color-primary;
    border-radius: 30px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: darken($color-primary, 10%);
  }
}

.vac-rooms-container .vac-room-list {
  &::-webkit-scrollbar {
    width: 1.2rem;
  }

  &::-webkit-scrollbar-thumb {
    background: $color-primary;
    border-radius: 30px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: darken($color-primary, 10%);
  }
}

.vac-files-box {
  &::-webkit-scrollbar {
    height: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d2d2d2;
    border-radius: 30px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: darken(#d2d2d2, 10%);
  }
}

.labels-list {
  &::-webkit-scrollbar-thumb {
    background: #d2d2d2;
    border-radius: 30px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: darken(#d2d2d2, 10%);
  }
}

.hwa-menu-list,
.labels-list-wrapper .labels-list {
  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background: $color-primary;
    border-radius: 30px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: darken($color-primary, 10%);
  }
}

.vac-emoji-picker {
  &::-webkit-scrollbar {
    height: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d2d2d2;
    border-radius: 30px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: darken(#d2d2d2, 10%);
  }
}
