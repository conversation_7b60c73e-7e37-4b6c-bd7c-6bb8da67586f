export const IMAGE_TYPES = ['png', 'jpg', 'jpeg', 'webp', 'svg', 'gif'] 
export const VIDEO_TYPES = ['mp4', 'video/ogg', 'webm', 'quicktime']
export const AUDIO_TYPES = ['mp3', 'audio/ogg', 'wav', 'mpeg', 'mpga']
export const STATIC_STATUSES = [
        { name: 'Delivered', listId: 'delivered' },
        { name: 'Read', listId: 'read' },
        { name: 'Failed', listId: 'failed' },
        { name: 'Sent', listId: 'sent' }
      ]
export const TEMPLATE_CATEGORY = ['UTILITY', 'MARKETING', 'AUTHENTICATION']
export const VARIABLE_TYPE = ['Number', 'Name']
export const TEMPLATE_TYPE = [
  { title: 'None', value: 'NONE', icon: null },
  { title: 'Text', value: 'TEXT', icon: require('../components/SvgIcon/text_icon.svg') }, // Text icon from react-icons
  { title: 'Image', value: 'IMAGE', icon: require('../components/SvgIcon/img_icon.svg') }, // Image icon from react-icons
  { title: 'Video', value: 'VIDEO', icon: require('../components/SvgIcon/video_icon.svg') }, // Video icon from react-icons
  { title: 'Document', value: 'DOCUMENT', icon: require('../components/SvgIcon/document.svg') }, // Document icon from react-icons
  { title: 'Location', value: 'LOCATION', icon: require('../components/SvgIcon/location_icon.svg') }
]
